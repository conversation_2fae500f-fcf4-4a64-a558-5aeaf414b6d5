import { useState } from "react";
import { useTaskSystem } from "@/context/TaskSystemContext";
import { useAniList } from "@/hooks/useAniList";
import {
  Calendar,
  Play,
  Plus,
  Star,
  RefreshCw,
  Layers,
  Zap,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Trophy,
  Lock
} from "lucide-react";

const TaskSystemList = () => {
  const { tasks, completeTask, getTaskProgress, getTotalAvailableXp, getEarnedXp, isLoading } = useTaskSystem();
  const { isAuthenticated } = useAniList();
  const [expanded, setExpanded] = useState(true);

  // Filter tasks into regular and bonus categories
  const regularTasks = tasks.filter(task => !task.isBonus);
  const bonusTasks = tasks.filter(task => task.isBonus);

  // Calculate progress
  const progress = getTaskProgress();
  const totalXp = getTotalAvailableXp();
  const earnedXp = getEarnedXp();

  // Get task icon based on icon name
  const getTaskIcon = (iconName, completed) => {
    const iconProps = {
      size: 16,
      className: completed ? "text-green-500" : "text-white/60"
    };

    switch (iconName) {
      case "calendar": return <Calendar {...iconProps} />;
      case "play": return <Play {...iconProps} />;
      case "plus": return <Plus {...iconProps} />;
      case "star": return <Star {...iconProps} />;
      case "refresh": return <RefreshCw {...iconProps} />;
      case "layers": return <Layers {...iconProps} />;
      case "zap": return <Zap {...iconProps} />;
      case "check-circle": return <CheckCircle {...iconProps} />;
      default: return <Calendar {...iconProps} />;
    }
  };

  // Get difficulty badge color
  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case "easy": return "bg-green-500/20 text-green-400";
      case "medium": return "bg-yellow-500/20 text-yellow-400";
      case "hard": return "bg-red-500/20 text-red-400";
      default: return "bg-blue-500/20 text-blue-400";
    }
  };

  if (isLoading) {
    return (
      <div className="w-full bg-black/30 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm text-white/80 flex items-center gap-1">
            <Trophy size={14} className="text-yellow-500" />
            Daily Tasks
          </h3>
        </div>
        <div className="text-center py-4 text-white/60 text-sm">Loading tasks...</div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm text-white/80 flex items-center gap-1">
          <Trophy size={14} className="text-yellow-500" />
          Daily Tasks
        </h3>
        <button
          onClick={() => setExpanded(!expanded)}
          className="text-white/60 hover:text-white"
        >
          {expanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
        </button>
      </div>

      {/* Progress bar */}
      <div className="bg-black/30 rounded-lg p-3 mb-3">
        <div className="flex justify-between items-center mb-1">
          <span className="text-xs text-white/80">Daily Progress</span>
          <span className="text-xs text-white/60">{progress}%</span>
        </div>
        <div className="w-full h-1.5 bg-black/50 rounded-full overflow-hidden">
          <div
            className="h-full bg-blue-500 rounded-full"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <div className="flex justify-between items-center mt-1">
          <span className="text-[10px] text-white/60">
            {earnedXp} / {totalXp} XP earned
          </span>
        </div>
      </div>

      {expanded && (
        <div className="space-y-2">
          {/* Regular tasks */}
          {regularTasks.map(task => (
            <TaskItem
              key={task.id}
              task={task}
              onComplete={completeTask}
              getTaskIcon={getTaskIcon}
              getDifficultyColor={getDifficultyColor}
            />
          ))}

          {/* Bonus tasks */}
          {bonusTasks.map(task => (
            <TaskItem
              key={task.id}
              task={task}
              onComplete={completeTask}
              getTaskIcon={getTaskIcon}
              getDifficultyColor={getDifficultyColor}
              isBonus
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Individual task item component
const TaskItem = ({ task, onComplete, getTaskIcon, getDifficultyColor, isBonus = false }) => {
  const { isAuthenticated } = useAniList();
  const completed = task.isCompleted;

  return (
    <div
      className={`bg-black/30 border ${completed ? 'border-green-500/30' : 'border-white/10'}
        rounded-lg p-3 flex items-center gap-3 transition-colors
        ${isBonus ? 'bg-yellow-900/10' : ''}
        ${!completed && !isBonus ? 'hover:border-white/20' : ''}`}
      data-task-id={task.id}
      data-completed={completed ? 'true' : 'false'}
    >
      {/* Task icon */}
      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${completed ? 'bg-green-500/20' : 'bg-black/50'}`}>
        {getTaskIcon(task.icon, completed)}
      </div>

      {/* Task details */}
      <div className="flex-1">
        <div className="flex items-center gap-2">
          <h4 className="text-sm font-medium">{task.title}</h4>
          <span className={`text-[10px] px-1.5 py-0.5 rounded-full ${getDifficultyColor(task.difficulty)}`}>
            {task.difficulty}
          </span>
          {isBonus && (
            <span className="text-[10px] bg-yellow-500/20 text-yellow-400 px-1.5 py-0.5 rounded-full">
              BONUS
            </span>
          )}
        </div>
        <p className="text-xs text-white/60 mt-0.5">{task.description}</p>
      </div>

      {/* XP reward and complete button */}
      <div className="flex flex-col items-end gap-1">
        <span className="text-xs text-yellow-400">+{task.xpReward} XP</span>
        {completed ? (
          <span className="text-[10px] text-green-500 flex items-center gap-1">
            <Lock size={12} />
            Completed
          </span>
        ) : (
          <button
            onClick={() => onComplete(task.id)}
            className="text-[10px] text-white/60 hover:text-white flex items-center gap-1 bg-white/5 hover:bg-white/10 px-2 py-1 rounded-full transition-colors"
            disabled={!isAuthenticated}
          >
            Complete
          </button>
        )}
      </div>
    </div>
  );
};

export default TaskSystemList;
