import { useState, useEffect } from "react";
import { useAniList } from "@/hooks/useAniList";
import { useUserActivity } from "@/context/UserActivityContext";
import profileApi from "@/api/profileApi";
import { toast } from "sonner";
import {
  Calendar,
  Play,
  Plus,
  Star,
  RefreshCw,
  Layers,
  Zap,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Trophy,
  XCircle
} from "lucide-react";

const TaskSystem = () => {
  const { user, isAuthenticated } = useAniList();
  const { addXp, fetchUserActivity } = useUserActivity();
  const [expanded, setExpanded] = useState(true);
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  
  // Initialize tasks on component mount
  useEffect(() => {
    if (isAuthenticated && user?.id) {
      loadTasks();
    }
  }, [isAuthenticated, user]);
  
  // Load tasks from the database
  const loadTasks = async () => {
    setLoading(true);
    try {
      const response = await profileApi.getDailyTasks(user.id);
      console.log("Tasks loaded from database:", response);
      
      if (response && response.completedTasks) {
        initializeTasks(response.completedTasks);
      } else {
        initializeTasks([]);
      }
    } catch (error) {
      console.error("Error loading tasks:", error);
      initializeTasks([]);
    } finally {
      setLoading(false);
    }
  };
  
  // Initialize tasks with completion status
  const initializeTasks = (completedTaskIds) => {
    const taskDefinitions = [
      {
        id: "visit",
        title: "Daily Visit",
        description: "Visit the site today",
        xpReward: 10,
        icon: "calendar",
        difficulty: "easy"
      },
      {
        id: "watch-episode",
        title: "Watch an Episode",
        description: "Watch at least one anime episode",
        xpReward: 15,
        icon: "play",
        difficulty: "easy"
      },
      {
        id: "add-anime",
        title: "Add to List",
        description: "Add an anime to your list",
        xpReward: 20,
        icon: "plus",
        difficulty: "easy"
      },
      {
        id: "rate-anime",
        title: "Rate an Anime",
        description: "Rate an anime in your list",
        xpReward: 25,
        icon: "star",
        difficulty: "medium"
      },
      {
        id: "update-progress",
        title: "Update Progress",
        description: "Update your progress on an anime",
        xpReward: 15,
        icon: "refresh",
        difficulty: "easy"
      },
      {
        id: "explore-genres",
        title: "Explore Genres",
        description: "Browse anime from a specific genre",
        xpReward: 20,
        icon: "layers",
        difficulty: "medium"
      },
      {
        id: "watch-seasonal",
        title: "Watch Seasonal",
        description: "Watch an episode from a currently airing anime",
        xpReward: 30,
        icon: "zap",
        difficulty: "medium"
      },
      {
        id: "complete-all",
        title: "Complete All Tasks",
        description: "Complete all other daily tasks",
        xpReward: 50,
        icon: "check-circle",
        difficulty: "hard",
        isBonus: true
      }
    ];
    
    // Mark tasks as completed based on completedTaskIds
    const tasksWithCompletionStatus = taskDefinitions.map(task => ({
      ...task,
      isCompleted: completedTaskIds.includes(task.id)
    }));
    
    setTasks(tasksWithCompletionStatus);
    
    // Auto-complete the visit task if not already completed
    const visitTask = tasksWithCompletionStatus.find(t => t.id === "visit");
    if (visitTask && !visitTask.isCompleted) {
      completeTask("visit");
    }
  };
  
  // Complete a task
  const completeTask = async (taskId) => {
    if (!isAuthenticated || !user?.id) return;
    
    // Find the task
    const task = tasks.find(t => t.id === taskId);
    if (!task) return;
    
    // If task is already completed, do nothing
    if (task.isCompleted) return;
    
    try {
      // Update UI immediately for better UX
      const updatedTasks = tasks.map(t => 
        t.id === taskId ? { ...t, isCompleted: true } : t
      );
      setTasks(updatedTasks);
      
      // Save to database
      await profileApi.completeTask(user.id, taskId);
      
      // Award XP
      await profileApi.addXp(user.id, task.xpReward, 'task_completion');
      
      // Refresh user profile data after a short delay
      setTimeout(() => {
        fetchUserActivity(user.id);
      }, 1000);
      
      // Show success toast
      toast.success(`Task Completed: ${task.title}`, {
        description: `You earned ${task.xpReward} XP!`,
        duration: 3000
      });
      
      // Check if all regular tasks are completed
      const regularTasks = tasks.filter(t => !t.isBonus);
      const allRegularTasksCompleted = regularTasks.every(t => 
        t.id === taskId ? true : t.isCompleted
      );
      
      // Complete the bonus task automatically if all regular tasks are completed
      if (allRegularTasksCompleted && !tasks.find(t => t.id === "complete-all").isCompleted) {
        setTimeout(() => {
          completeTask("complete-all");
        }, 1000);
      }
    } catch (error) {
      console.error("Error completing task:", error);
      
      // Revert UI change on error
      const revertedTasks = tasks.map(t => 
        t.id === taskId ? { ...t, isCompleted: false } : t
      );
      setTasks(revertedTasks);
      
      // Show error toast
      toast.error("Failed to complete task", {
        description: "Please try again later",
        duration: 3000
      });
    }
  };
  
  // Get task icon based on icon name
  const getTaskIcon = (iconName, completed) => {
    const iconProps = {
      size: 16,
      className: completed ? "text-green-500" : "text-white/60"
    };

    switch (iconName) {
      case "calendar": return <Calendar {...iconProps} />;
      case "play": return <Play {...iconProps} />;
      case "plus": return <Plus {...iconProps} />;
      case "star": return <Star {...iconProps} />;
      case "refresh": return <RefreshCw {...iconProps} />;
      case "layers": return <Layers {...iconProps} />;
      case "zap": return <Zap {...iconProps} />;
      case "check-circle": return <CheckCircle {...iconProps} />;
      default: return <Calendar {...iconProps} />;
    }
  };

  // Get difficulty badge color
  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case "easy": return "bg-green-500/20 text-green-400";
      case "medium": return "bg-yellow-500/20 text-yellow-400";
      case "hard": return "bg-red-500/20 text-red-400";
      default: return "bg-blue-500/20 text-blue-400";
    }
  };

  // Calculate progress
  const getTaskProgress = () => {
    if (tasks.length === 0) return 0;
    const regularTasks = tasks.filter(t => !t.isBonus);
    const completedRegularTasks = regularTasks.filter(t => t.isCompleted);
    return Math.round((completedRegularTasks.length / regularTasks.length) * 100);
  };

  // Get total XP available from tasks
  const getTotalAvailableXp = () => {
    return tasks.reduce((total, task) => total + task.xpReward, 0);
  };

  // Get earned XP from completed tasks
  const getEarnedXp = () => {
    return tasks
      .filter(task => task.isCompleted)
      .reduce((total, task) => total + task.xpReward, 0);
  };

  // Filter tasks into regular and bonus categories
  const regularTasks = tasks.filter(task => !task.isBonus);
  const bonusTasks = tasks.filter(task => task.isBonus);

  // Calculate progress
  const progress = getTaskProgress();
  const totalXp = getTotalAvailableXp();
  const earnedXp = getEarnedXp();

  if (loading) {
    return (
      <div className="w-full bg-black/20 rounded-lg p-4 animate-pulse">
        <div className="h-6 bg-white/10 rounded w-1/3 mb-4"></div>
        <div className="h-4 bg-white/10 rounded w-full mb-2"></div>
        <div className="h-4 bg-white/10 rounded w-full mb-2"></div>
        <div className="h-4 bg-white/10 rounded w-full"></div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm text-white/80 flex items-center gap-1">
          <Trophy size={14} className="text-yellow-500" />
          Daily Tasks
        </h3>
        <button
          onClick={() => setExpanded(!expanded)}
          className="text-white/60 hover:text-white"
        >
          {expanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
        </button>
      </div>

      {/* Progress bar */}
      <div className="bg-black/30 rounded-lg p-3 mb-3">
        <div className="flex justify-between items-center mb-1">
          <span className="text-xs text-white/80">Daily Progress</span>
          <span className="text-xs text-white/60">{progress}%</span>
        </div>
        <div className="w-full h-1.5 bg-black/50 rounded-full overflow-hidden">
          <div
            className="h-full bg-blue-500 rounded-full"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <div className="flex justify-between items-center mt-1">
          <span className="text-[10px] text-white/60">
            {earnedXp} / {totalXp} XP earned
          </span>
        </div>
      </div>

      {expanded && (
        <div className="space-y-2">
          {/* Regular tasks */}
          {regularTasks.map(task => (
            <TaskItem
              key={task.id}
              task={task}
              onComplete={completeTask}
              getTaskIcon={getTaskIcon}
              getDifficultyColor={getDifficultyColor}
            />
          ))}

          {/* Bonus tasks */}
          {bonusTasks.map(task => (
            <TaskItem
              key={task.id}
              task={task}
              onComplete={completeTask}
              getTaskIcon={getTaskIcon}
              getDifficultyColor={getDifficultyColor}
              isBonus
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Individual task item component
const TaskItem = ({ task, onComplete, getTaskIcon, getDifficultyColor, isBonus = false }) => {
  return (
    <div
      className={`bg-black/30 border ${task.isCompleted ? 'border-green-500/30' : 'border-white/10'}
        rounded-lg p-3 flex items-center gap-3 transition-colors
        ${isBonus ? 'bg-yellow-900/10' : ''}
        ${!task.isCompleted && !isBonus ? 'hover:border-white/20' : ''}`}
      data-task-id={task.id}
      data-completed={task.isCompleted ? 'true' : 'false'}
    >
      {/* Task icon */}
      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${task.isCompleted ? 'bg-green-500/20' : 'bg-black/50'}`}>
        {getTaskIcon(task.icon, task.isCompleted)}
      </div>

      {/* Task details */}
      <div className="flex-1">
        <div className="flex items-center gap-2">
          <h4 className="text-sm font-medium">{task.title}</h4>
          <span className={`text-[10px] px-1.5 py-0.5 rounded-full ${getDifficultyColor(task.difficulty)}`}>
            {task.difficulty}
          </span>
          {isBonus && (
            <span className="text-[10px] bg-yellow-500/20 text-yellow-400 px-1.5 py-0.5 rounded-full">
              BONUS
            </span>
          )}
        </div>
        <p className="text-xs text-white/60 mt-0.5">{task.description}</p>
      </div>

      {/* XP reward and complete button */}
      <div className="flex flex-col items-end gap-1">
        <span className="text-xs text-yellow-400">+{task.xpReward} XP</span>
        {task.isCompleted ? (
          <span className="text-[10px] text-green-500 flex items-center gap-1">
            <CheckCircle size={12} />
            Completed
          </span>
        ) : (
          <button
            onClick={() => onComplete(task.id)}
            className="text-[10px] text-white/60 hover:text-white flex items-center gap-1 bg-white/5 hover:bg-white/10 px-2 py-1 rounded-full transition-colors"
          >
            Complete
          </button>
        )}
      </div>
    </div>
  );
};

export default TaskSystem;
