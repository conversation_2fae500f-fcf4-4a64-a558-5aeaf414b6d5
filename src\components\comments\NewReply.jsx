import { useState } from 'react';
import { Send, Loader2, X, AlertTriangle } from 'lucide-react';
import { addReply } from '@/api/comments';
import { toast } from 'sonner';

const NewReply = ({ commentId, user, onReplyAdded, onCancel }) => {
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasSpoiler, setHasSpoiler] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!content.trim()) {
      toast.error('Reply cannot be empty');
      return;
    }

    try {
      setIsSubmitting(true);

      const replyData = {
        userId: user.id,
        userName: user.name,
        userAvatar: user.avatar?.medium || '',
        content: content.trim(),
        hasSpoiler: hasSpoiler
      };

      const updatedComment = await addReply(commentId, replyData);
      onReplyAdded(updatedComment);
      setContent('');
      toast.success('Reply added successfully');
      onCancel(); // Close the reply form after successful submission
    } catch (error) {
      toast.error('Failed to add reply. Please try again.');
      console.error('Error adding reply:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white/5 hover:bg-white/8 transition-colors duration-200 rounded-lg p-2 ml-8 mt-1 border border-white/10 border-l-2 border-l-blue-500/30">
      <div className="flex gap-2">
        <div className="shrink-0">
          <img
            src={user.avatar?.medium || 'https://i.imgur.com/6VBx3io.png'}
            alt={user.name}
            className="w-6 h-6 rounded-full object-cover border border-white/20 shadow-sm"
          />
        </div>

        <form onSubmit={handleSubmit} className="flex-1">
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Write a reply..."
            className="w-full bg-white/10 border border-white/20 focus:border-blue-500/50 outline-none rounded-lg p-2 text-xs resize-none min-h-[60px] transition-colors"
            disabled={isSubmitting}
            autoFocus
          />

          <div className="flex items-center mt-2">
            <label className="flex items-center gap-2 text-xs cursor-pointer">
              <input
                type="checkbox"
                checked={hasSpoiler}
                onChange={() => setHasSpoiler(!hasSpoiler)}
                className="rounded text-blue-500 focus:ring-blue-500 h-3 w-3"
              />
              <div className="flex items-center gap-1.5">
                <AlertTriangle size={12} className="text-yellow-500" />
                <span>Mark as spoiler</span>
              </div>
            </label>
          </div>

          <div className="flex justify-end mt-2 gap-2">
            <button
              type="button"
              onClick={onCancel}
              className="flex items-center gap-1.5 px-3 py-1.5 bg-white/10 hover:bg-white/15 rounded-lg text-xs font-medium transition-colors"
            >
              <X size={12} /> Cancel
            </button>

            <button
              type="submit"
              disabled={isSubmitting || !content.trim()}
              className={`flex items-center gap-1.5 px-3 py-1.5 rounded-lg text-xs font-medium transition-colors ${
                isSubmitting || !content.trim()
                  ? 'bg-white/10 text-gray-400 cursor-not-allowed'
                  : hasSpoiler
                    ? 'bg-yellow-600 hover:bg-yellow-700 text-white'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {isSubmitting ? (
                <>
                  <Loader2 size={12} className="animate-spin" />
                  Submitting...
                </>
              ) : (
                <>
                  {hasSpoiler && <AlertTriangle size={12} className="mr-1" />}
                  <Send size={12} />
                  Reply
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NewReply;
