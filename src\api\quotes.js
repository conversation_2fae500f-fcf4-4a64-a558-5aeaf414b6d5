import axios from "axios";

/**
 * API service for fetching different types of quotes and backgrounds
 */

// Cache for anime backgrounds
const backgroundCache = {
  anime: [],
  bible: [],
  quran: []
};

// Cache quotes to reduce API calls
const quoteCache = {
  anime: [],
  bible: [],
  quran: []
};

/**
 * Fetch a random anime quote
 * @param {boolean} bypassCache - Whether to bypass the cache and fetch a new quote
 * @returns {Promise<Object>} Quote object with anime, character, and quote properties
 */
export const getRandomAnimeQuote = async (bypassCache = false) => {
  try {
    // When bypassing cache, we'll try to get a new quote from APIs or fallback
    // Otherwise, use the cache if available
    if (!bypassCache && quoteCache.anime.length > 0) {
      const randomIndex = Math.floor(Math.random() * quoteCache.anime.length);
      return quoteCache.anime[randomIndex];
    }

    // If we're bypassing cache or have no cached quotes, try to get a new one
    // We'll use a random approach to alternate between APIs and fallback quotes
    // This ensures we get different quotes even if APIs are failing

    // Randomly decide whether to use APIs or fallback quotes (70% chance for APIs)
    const useApis = Math.random() < 0.7;

    if (useApis) {
      // Try API 1 - animechan.vercel.app (more reliable mirror of animechan)
      try {
        const response = await axios.get("https://animechan.vercel.app/api/random");

        if (response.data && response.data.anime && response.data.character && response.data.quote) {
          const quote = {
            anime: response.data.anime,
            character: response.data.character,
            quote: response.data.quote
          };

          // Cache the quote
          if (!quoteCache.anime.some(q => q.quote === quote.quote)) {
            quoteCache.anime.push(quote);
          }

          return quote;
        }
      } catch (error1) {
        console.log("First anime quote API failed, trying alternative...");
      }

      // Try API 2 - some-random-api.ml
      try {
        const response = await axios.get("https://some-random-api.ml/animu/quote");

        if (response.data && response.data.anime && response.data.character && response.data.sentence) {
          const quote = {
            anime: response.data.anime,
            character: response.data.character,
            quote: response.data.sentence
          };

          // Cache the quote
          if (!quoteCache.anime.some(q => q.quote === quote.quote)) {
            quoteCache.anime.push(quote);
          }

          return quote;
        }
      } catch (error2) {
        console.log("Second anime quote API failed, using fallback quotes");
      }
    }

    // If APIs failed or we randomly chose to use fallback quotes
    // Get a fallback quote that's different from the last one if possible
    const fallbackQuote = getFallbackAnimeQuote();

    // Only cache if it's not already in the cache
    if (!quoteCache.anime.some(q => q.quote === fallbackQuote.quote)) {
      quoteCache.anime.push(fallbackQuote);
    }

    return fallbackQuote;
  } catch (error) {
    console.error("Error fetching anime quote:", error);
    return getFallbackAnimeQuote();
  }
};

/**
 * Fetch a random Bible verse
 * @param {boolean} bypassCache - Whether to bypass the cache and fetch a new verse
 * @returns {Promise<Object>} Verse object with text and reference properties
 */
export const getRandomBibleVerse = async (bypassCache = false) => {
  try {
    // Return from cache if available and not bypassing cache
    if (!bypassCache && quoteCache.bible.length > 0) {
      const randomIndex = Math.floor(Math.random() * quoteCache.bible.length);
      return quoteCache.bible[randomIndex];
    }

    // Fetch new verses
    const response = await axios.get("https://bible-api.com/?random=verse");

    if (!response.data) {
      return getFallbackBibleVerse();
    }

    // Format the response
    const verse = {
      text: response.data.text,
      reference: response.data.reference
    };

    // Cache the verse
    quoteCache.bible.push(verse);

    return verse;
  } catch (error) {
    console.error("Error fetching Bible verse:", error);
    return getFallbackBibleVerse();
  }
};

/**
 * Fetch a random Quran verse
 * @param {boolean} bypassCache - Whether to bypass the cache and fetch a new verse
 * @returns {Promise<Object>} Verse object with text and reference properties
 */
export const getRandomQuranVerse = async (bypassCache = false) => {
  try {
    // Return from cache if available and not bypassing cache
    if (!bypassCache && quoteCache.quran.length > 0) {
      const randomIndex = Math.floor(Math.random() * quoteCache.quran.length);
      return quoteCache.quran[randomIndex];
    }

    // Generate random surah (1-114) and ayah (1-10)
    const surah = Math.floor(Math.random() * 114) + 1;
    const ayah = Math.floor(Math.random() * 10) + 1;

    // Fetch the verse
    const response = await axios.get(`https://api.alquran.cloud/v1/ayah/${surah}:${ayah}/en.asad`);

    if (!response.data || !response.data.data) {
      return getFallbackQuranVerse();
    }

    // Format the response
    const verse = {
      text: response.data.data.text,
      reference: `Surah ${response.data.data.surah.englishName} (${surah}:${ayah})`
    };

    // Cache the verse
    quoteCache.quran.push(verse);

    return verse;
  } catch (error) {
    console.error("Error fetching Quran verse:", error);
    return getFallbackQuranVerse();
  }
};

// Keep track of the last fallback quote index to avoid repetition
let lastFallbackQuoteIndex = -1;

/**
 * Fallback anime quotes in case the API is down
 * @returns {Object} A random anime quote that's different from the last one if possible
 */
const getFallbackAnimeQuote = () => {
  const fallbackQuotes = [
    {
      anime: "Naruto",
      character: "Naruto Uzumaki",
      quote: "I'm not gonna run away, I never go back on my word! That's my nindo: my ninja way!"
    },
    {
      anime: "Attack on Titan",
      character: "Eren Yeager",
      quote: "If you win, you live. If you lose, you die. If you don't fight, you can't win!"
    },
    {
      anime: "Fullmetal Alchemist",
      character: "Edward Elric",
      quote: "A lesson without pain is meaningless. That's because no one can gain without sacrificing something."
    },
    {
      anime: "One Piece",
      character: "Monkey D. Luffy",
      quote: "I don't want to conquer anything. I just think the guy with the most freedom in this whole ocean... that's the Pirate King!"
    },
    {
      anime: "My Hero Academia",
      character: "All Might",
      quote: "It's not bad to dream. But you also have to consider what's realistic."
    },
    {
      anime: "Demon Slayer",
      character: "Tanjiro Kamado",
      quote: "No matter how many people you may lose, you have no choice but to go on living -- no matter how devastating the blows might be."
    },
    {
      anime: "Death Note",
      character: "L Lawliet",
      quote: "Being alone is better than being with the wrong person."
    },
    {
      anime: "Steins;Gate",
      character: "Okabe Rintarou",
      quote: "The universe has a beginning, but no end. Infinite. Stars too have a beginning, but their own power leads them to their destruction. Finite."
    },
    {
      anime: "Hunter x Hunter",
      character: "Ging Freecss",
      quote: "You should enjoy the little detours to the fullest. Because that's where you'll find the things more important than what you want."
    },
    {
      anime: "Violet Evergarden",
      character: "Violet Evergarden",
      quote: "I want to know what 'I love you' means."
    },
    {
      anime: "Haikyuu!!",
      character: "Hinata Shoyo",
      quote: "As long as I'm here, you're invincible."
    },
    {
      anime: "Jujutsu Kaisen",
      character: "Satoru Gojo",
      quote: "Throughout heaven and earth, I alone am the honored one."
    },
    {
      anime: "Cowboy Bebop",
      character: "Spike Spiegel",
      quote: "Whatever happens, happens."
    },
    {
      anime: "Your Lie in April",
      character: "Kaori Miyazono",
      quote: "Maybe there's only a dark road ahead. But you still have to believe and keep going. Believe that the stars will light your path, even a little bit."
    },
    {
      anime: "Tokyo Ghoul",
      character: "Ken Kaneki",
      quote: "If you were to write a story with me in the lead role, it would certainly be... a tragedy."
    }
  ];

  // If we have more than one quote, make sure we don't repeat the last one
  if (fallbackQuotes.length > 1) {
    let newIndex;
    do {
      newIndex = Math.floor(Math.random() * fallbackQuotes.length);
    } while (newIndex === lastFallbackQuoteIndex && fallbackQuotes.length > 1);

    lastFallbackQuoteIndex = newIndex;
    return fallbackQuotes[newIndex];
  } else {
    // If there's only one quote, just return it
    return fallbackQuotes[0];
  }
};

/**
 * Fallback Bible verses in case the API is down
 */
const getFallbackBibleVerse = () => {
  const fallbackVerses = [
    {
      text: "For God so loved the world, that he gave his only Son, that whoever believes in him should not perish but have eternal life.",
      reference: "John 3:16"
    },
    {
      text: "I can do all things through him who strengthens me.",
      reference: "Philippians 4:13"
    },
    {
      text: "Trust in the LORD with all your heart, and do not lean on your own understanding.",
      reference: "Proverbs 3:5"
    },
    {
      text: "Be strong and courageous. Do not be frightened, and do not be dismayed, for the LORD your God is with you wherever you go.",
      reference: "Joshua 1:9"
    },
    {
      text: "The LORD is my shepherd; I shall not want.",
      reference: "Psalm 23:1"
    }
  ];

  const randomIndex = Math.floor(Math.random() * fallbackVerses.length);
  return fallbackVerses[randomIndex];
};

/**
 * Fallback Quran verses in case the API is down
 */
const getFallbackQuranVerse = () => {
  const fallbackVerses = [
    {
      text: "Indeed, Allah is with the patient.",
      reference: "Surah Al-Baqarah (2:153)"
    },
    {
      text: "So remember Me; I will remember you.",
      reference: "Surah Al-Baqarah (2:152)"
    },
    {
      text: "And when My servants ask you concerning Me - indeed I am near. I respond to the invocation of the supplicant when he calls upon Me.",
      reference: "Surah Al-Baqarah (2:186)"
    },
    {
      text: "For indeed, with hardship [will be] ease. Indeed, with hardship [will be] ease.",
      reference: "Surah Ash-Sharh (94:5-6)"
    },
    {
      text: "And We have already created man and know what his soul whispers to him, and We are closer to him than [his] jugular vein.",
      reference: "Surah Qaf (50:16)"
    }
  ];

  const randomIndex = Math.floor(Math.random() * fallbackVerses.length);
  return fallbackVerses[randomIndex];
};

/**
 * Collection of Bible-themed background images
 */
const bibleBackgrounds = [
  'https://i.imgur.com/JFgCUHw.jpg',
  'https://i.imgur.com/Rl5JnZn.jpg',
  'https://i.imgur.com/8KbJGpK.jpg',
  'https://images.unsplash.com/photo-1504052434569-70ad5836ab65?q=80&w=1470&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1507434965515-61970f2bd7c6?q=80&w=1470&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1529946179074-87642f6204d7?q=80&w=1470&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1478826160983-e6db8c7d537a?q=80&w=1470&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1499209974431-9dddcece7f88?q=80&w=1470&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1501686637-b7aa9c48a882?q=80&w=1470&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1542273917363-3b1817f69a2d?q=80&w=1474&auto=format&fit=crop'
];

/**
 * Collection of Quran/Islamic-themed background images
 */
const quranBackgrounds = [
  'https://i.imgur.com/Rl5JnZn.jpg',
  'https://i.imgur.com/JFgCUHw.jpg',
  'https://i.imgur.com/8KbJGpK.jpg',
  'https://images.unsplash.com/photo-1564121211835-e88c852648ab?q=80&w=1470&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1542820229-081e0c12af0b?q=80&w=1470&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1519817650390-64a93db51149?q=80&w=1470&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1584551246679-0daf3d275d0f?q=80&w=1470&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1585036156171-384164a8c675?q=80&w=1470&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1591956689134-b6740a398ccb?q=80&w=1470&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1618022307837-0d80b8d0a4b5?q=80&w=1470&auto=format&fit=crop'
];

/**
 * Fetch random background images based on quote type
 * @param {string} type - Type of background ('anime', 'bible', or 'quran')
 * @param {boolean} bypassCache - Whether to bypass the cache and fetch a new background
 * @returns {Promise<string>} URL of the background image
 */
export const getRandomBackground = async (type = 'anime', bypassCache = false) => {
  try {
    // If we have cached backgrounds and not bypassing cache, use one of those
    if (!bypassCache && backgroundCache[type] && backgroundCache[type].length > 0) {
      const randomIndex = Math.floor(Math.random() * backgroundCache[type].length);
      return backgroundCache[type][randomIndex];
    }

    // For anime backgrounds, use Jikan API to get popular anime
    if (type === 'anime') {
      // Get a random page between 1 and 10
      const page = Math.floor(Math.random() * 10) + 1;
      const response = await axios.get(`https://api.jikan.moe/v4/top/anime?page=${page}`);

      if (response.data && response.data.data && response.data.data.length > 0) {
        // Extract image URLs from the response
        const images = response.data.data
          .filter(anime => anime.images && anime.images.jpg && anime.images.jpg.large_image_url)
          .map(anime => anime.images.jpg.large_image_url);

        // Cache the images
        backgroundCache.anime = [...images];

        // Return a random image
        if (images.length > 0) {
          const randomIndex = Math.floor(Math.random() * images.length);
          return images[randomIndex];
        }
      }
    }
    // For Bible backgrounds, use the predefined collection
    else if (type === 'bible') {
      // Cache the images if not already cached
      if (!backgroundCache.bible.length) {
        backgroundCache.bible = [...bibleBackgrounds];
      }

      const randomIndex = Math.floor(Math.random() * bibleBackgrounds.length);
      return bibleBackgrounds[randomIndex];
    }
    // For Quran backgrounds, use the predefined collection
    else if (type === 'quran') {
      // Cache the images if not already cached
      if (!backgroundCache.quran.length) {
        backgroundCache.quran = [...quranBackgrounds];
      }

      const randomIndex = Math.floor(Math.random() * quranBackgrounds.length);
      return quranBackgrounds[randomIndex];
    }

    // Fallback images for each type
    const fallbackImages = {
      anime: 'https://i.imgur.com/8KbJGpK.jpg',
      bible: 'https://i.imgur.com/JFgCUHw.jpg',
      quran: 'https://i.imgur.com/Rl5JnZn.jpg'
    };

    return fallbackImages[type] || fallbackImages.anime;
  } catch (error) {
    console.error(`Error fetching ${type} background:`, error);

    // Fallback images
    const fallbackImages = {
      anime: 'https://i.imgur.com/8KbJGpK.jpg',
      bible: 'https://i.imgur.com/JFgCUHw.jpg',
      quran: 'https://i.imgur.com/Rl5JnZn.jpg'
    };

    return fallbackImages[type] || fallbackImages.anime;
  }
};
