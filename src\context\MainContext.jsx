import { createContext, useEffect, useMemo, useState, useContext } from "react";

export const MainContext = createContext();

// Export the hook directly from the context file
export const useMainContext = () => {
  const context = useContext(MainContext);
  if (context === undefined) {
    throw new Error('useMainContext must be used within a MainContext.Provider');
  }
  return context;
};

export const AppContext = ({ children }) => {
  const [view, setView] = useState(window?.innerWidth);
  const [src, setSrc] = useState("");
  const [hideHeader, setHideHeader] = useState(false);
  const [hideFooter, setHideFooter] = useState(false);

  const reW = () => setView(window?.innerWidth);

  useEffect(() => {
    reW();
    window.addEventListener("resize", reW);
    return () => window.removeEventListener("resize", reW);
  }, []);

  function formatBudget(n) {
    try {
      if (n >= 1e9) {
        return (n / 1e9).toFixed(1) + "B";
      } else if (n >= 1e6) {
        return (n / 1e6).toFixed(1) + "M";
      } else if (n >= 1e3) {
        return (n / 1e3).toFixed(1) + "K"; // Thousand
      } else {
        return n;
      }
    } catch (e) {
      console.log(e?.message);
      return n;
    }
  }

  const getStorage = (key) => {
    try {
      const v = localStorage.getItem(key);
      return v !== undefined ? JSON.parse(v) : null;
    } catch (e) {
      console.log(e);
      return null;
    }
  };
  const setStorage = (key, value) => {
    try {
      if (key && value !== undefined) {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
      }
    } catch (e) {
      console.log(e);
      return false;
    }
  };
  const rc = useMemo(() => {
    const sc = [
      "#a8dadc",
      "#f4a261",
      "#ffddd2",
      "#457b9d",
      "#e9c46a",
      "#2a9d8f",
      "#e76f51",
      "#f6bd60",
      "#cb997e",
      "#b7b7a4",
      "#C54D6F",
      "#DC3355",
      "pink",
    ];
    return sc[Math.floor(Math.random() * sc.length)];
  }, []);
  const share = async ({ title = "", url }) => {
    const u = url || window.location.href;
    const cc = async () => {
      try {
        await navigator?.clipboard?.writeText(u);
        toast.info("Link Copied to Clipboard");
      } catch (e) {
        console.log(e?.message);
      }
    };
    if (navigator?.share) {
      try {
        await navigator?.share({
          title: title,
          text: title?.length ? `Watch ${title}` : "",
          url: url,
        });
      } catch (e) {
        console.log("Error sharing content:", e);
        cc();
      }
    } else {
      cc();
    }
  };

  return (
    <MainContext.Provider
      value={{
        view,
        getStorage,
        setStorage,
        formatBudget,
        src,
        setSrc,
        share,
        rc,
        hideHeader,
        setHideHeader,
        hideFooter,
        setHideFooter,
      }}
    >
      {children}
    </MainContext.Provider>
  );
};
