/**
 * Activity tracking utilities
 * These functions track user actions for task verification
 */

/**
 * Track that the user has watched an episode
 * @param {string} userId - The user's AniList ID
 * @param {Object} episodeData - Data about the episode watched
 */
export const trackWatchEpisode = (userId, episodeData) => {
  try {
    // Record that the user watched an episode
    localStorage.setItem(`watched-episode-${userId}`, Date.now().toString());
    
    // If it's a seasonal anime, also record that
    if (episodeData?.isAiring) {
      localStorage.setItem(`watched-seasonal-${userId}`, Date.now().toString());
    }
  } catch (error) {
    console.error("Error tracking watch episode:", error);
  }
};

/**
 * Track that the user has added an anime to their list
 * @param {string} userId - The user's AniList ID
 * @param {Object} animeData - Data about the anime added
 */
export const trackAddAnime = (userId, animeData) => {
  try {
    localStorage.setItem(`added-anime-${userId}`, Date.now().toString());
  } catch (error) {
    console.error("Error tracking add anime:", error);
  }
};

/**
 * Track that the user has rated an anime
 * @param {string} userId - The user's AniList ID
 * @param {Object} ratingData - Data about the rating
 */
export const trackRateAnime = (userId, ratingData) => {
  try {
    localStorage.setItem(`rated-anime-${userId}`, Date.now().toString());
  } catch (error) {
    console.error("Error tracking rate anime:", error);
  }
};

/**
 * Track that the user has updated their progress on an anime
 * @param {string} userId - The user's AniList ID
 * @param {Object} progressData - Data about the progress update
 */
export const trackUpdateProgress = (userId, progressData) => {
  try {
    localStorage.setItem(`updated-progress-${userId}`, Date.now().toString());
  } catch (error) {
    console.error("Error tracking update progress:", error);
  }
};

/**
 * Track that the user has explored anime by genre
 * @param {string} userId - The user's AniList ID
 * @param {string} genre - The genre explored
 */
export const trackExploreGenres = (userId, genre) => {
  try {
    localStorage.setItem(`explored-genres-${userId}`, Date.now().toString());
    localStorage.setItem(`explored-genre-${userId}`, genre);
  } catch (error) {
    console.error("Error tracking explore genres:", error);
  }
};

/**
 * Clear all tracking data for a user
 * This should be called when the user logs out
 * @param {string} userId - The user's AniList ID
 */
export const clearTrackingData = (userId) => {
  try {
    localStorage.removeItem(`watched-episode-${userId}`);
    localStorage.removeItem(`watched-seasonal-${userId}`);
    localStorage.removeItem(`added-anime-${userId}`);
    localStorage.removeItem(`rated-anime-${userId}`);
    localStorage.removeItem(`updated-progress-${userId}`);
    localStorage.removeItem(`explored-genres-${userId}`);
    localStorage.removeItem(`explored-genre-${userId}`);
  } catch (error) {
    console.error("Error clearing tracking data:", error);
  }
};
