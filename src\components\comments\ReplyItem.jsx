import { useState, useRef, useEffect } from 'react';
import { Heart, Edit, Trash, X, Check, AlertTriangle, MoreVertical } from 'lucide-react';
import { useAniList } from '@/hooks/useAniList';
import { updateReply, deleteReply, likeReply } from '@/api/comments';
import { toast } from 'sonner';
import { formatTimeAgo } from '@/utils/formatDate';
import SpoilerContent from './SpoilerContent';

const ReplyItem = ({ commentId, reply, onReplyUpdated, onReplyDeleted }) => {
  const { user } = useAniList();
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(reply.content);
  const [isLiking, setIsLiking] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  const optionsRef = useRef(null);
  const [likes, setLikes] = useState(reply.likes || 0);
  const [hasSpoiler, setHasSpoiler] = useState(reply.hasSpoiler || false);

  // For testing purposes, always consider the user as the author
  const isAuthor = true; // user && user.id === reply.userId;
  const formattedDate = formatTimeAgo(reply.createdAt);

  const handleLike = async () => {
    if (isLiking) return;

    try {
      setIsLiking(true);
      const updatedComment = await likeReply(commentId, reply._id);

      // Find the updated reply in the comment
      const updatedReply = updatedComment.replies.find(r => r._id === reply._id);
      if (updatedReply) {
        setLikes(updatedReply.likes);
      }

      onReplyUpdated(updatedComment);
    } catch (error) {
      toast.error('Failed to like reply');
    } finally {
      setIsLiking(false);
    }
  };

  // Handle click outside to close options menu
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (optionsRef.current && !optionsRef.current.contains(event.target)) {
        setShowOptions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [optionsRef]);

  const handleEdit = () => {
    setIsEditing(true);
    setShowOptions(false);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditContent(reply.content);
  };

  const handleSaveEdit = async () => {
    if (!editContent.trim()) {
      toast.error('Reply cannot be empty');
      return;
    }

    try {
      // For testing, use the reply's original userId instead of the current user's ID
      const updatedComment = await updateReply(commentId, reply._id, {
        content: editContent,
        userId: reply.userId, // Use the original reply's userId
        hasSpoiler: hasSpoiler
      });

      setIsEditing(false);
      onReplyUpdated(updatedComment);
      toast.success('Reply updated');
    } catch (error) {
      console.error('Error updating reply:', error);
      toast.error('Failed to update reply');
    }
  };

  const toggleSpoiler = () => {
    setHasSpoiler(!hasSpoiler);
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this reply?')) return;

    try {
      // For testing, use the reply's original userId instead of the current user's ID
      await deleteReply(commentId, reply._id, reply.userId);
      onReplyDeleted(reply._id);
      toast.success('Reply deleted');
    } catch (error) {
      console.error('Error deleting reply:', error);
      toast.error('Failed to delete reply');
    }
  };

  return (
    <div className="flex gap-2 p-2 bg-white/5 hover:bg-white/8 transition-colors duration-200 rounded-lg ml-8 mt-1 border border-white/10 border-l-2 border-l-blue-500/30">
      <div className="shrink-0">
        <img
          src={reply.userAvatar || 'https://i.imgur.com/6VBx3io.png'}
          alt={reply.userName}
          className="w-6 h-6 rounded-full object-cover border border-white/20 shadow-sm"
        />
      </div>

      <div className="flex-1">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="font-semibold text-xs text-white">{reply.userName}</span>
            <span className="text-xs text-gray-400 italic">{formattedDate}</span>
          </div>

          <div className="flex items-center gap-2">

            {user && (
              <div className="relative" ref={optionsRef}>
                <button
                  onClick={() => setShowOptions(!showOptions)}
                  className="p-1 hover:bg-white/10 rounded-full transition-colors"
                >
                  <MoreVertical size={14} className="text-gray-400" />
                </button>

                {showOptions && (
                  <div className="absolute right-0 top-full mt-1 bg-gray-800/90 backdrop-blur-sm rounded-lg shadow-xl z-10 py-1 w-32 border border-white/10">
                    <>
                      <button
                        onClick={handleEdit}
                        className="flex items-center gap-2 w-full px-3 py-2 hover:bg-white/10 text-left text-xs transition-colors"
                      >
                        <Edit size={12} /> Edit
                      </button>
                      <button
                        onClick={handleDelete}
                        className="flex items-center gap-2 w-full px-3 py-2 hover:bg-red-500/20 text-left text-xs text-red-400 transition-colors"
                      >
                        <Trash size={12} /> Delete
                      </button>
                    </>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {isEditing ? (
          <div className="mt-2">
            <textarea
              value={editContent}
              onChange={(e) => setEditContent(e.target.value)}
              className="w-full bg-white/10 border border-white/20 focus:border-blue-500/50 outline-none rounded-lg p-2 text-xs resize-none min-h-[80px] transition-colors"
              placeholder="Edit your reply..."
            />

            <div className="flex items-center mt-2">
              <label className="flex items-center gap-2 text-xs cursor-pointer">
                <input
                  type="checkbox"
                  checked={hasSpoiler}
                  onChange={toggleSpoiler}
                  className="rounded text-blue-500 focus:ring-blue-500 h-3 w-3"
                />
                <div className="flex items-center gap-1.5">
                  <AlertTriangle size={12} className="text-yellow-500" />
                  <span>Mark as spoiler</span>
                </div>
              </label>
            </div>

            <div className="flex justify-end gap-2 mt-2">
              <button
                onClick={handleCancelEdit}
                className="flex items-center gap-1 px-3 py-1 bg-white/10 hover:bg-white/15 rounded-lg text-xs transition-colors"
              >
                <X size={12} /> Cancel
              </button>
              <button
                onClick={handleSaveEdit}
                className="flex items-center gap-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded-lg text-xs transition-colors"
              >
                <Check size={12} /> Save
              </button>
            </div>
          </div>
        ) : (
          <>
            {reply.hasSpoiler ? (
              <SpoilerContent content={reply.content} isReply={true} />
            ) : (
              <p className="text-xs mt-1 leading-relaxed">{reply.content}</p>
            )}

            <div className="flex items-center gap-2 mt-2">
              <button
                onClick={handleLike}
                disabled={isLiking}
                className={`flex items-center gap-1 text-xs px-2 py-0.5 rounded-md transition-all ${
                  likes > 0
                    ? 'text-pink-400 bg-pink-500/10'
                    : 'text-gray-300 hover:text-pink-400 hover:bg-pink-500/10'
                }`}
              >
                <Heart size={12} className={likes > 0 ? 'fill-pink-400 text-pink-400' : ''} />
                {likes > 0 ? likes : 'Like'}
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default ReplyItem;
