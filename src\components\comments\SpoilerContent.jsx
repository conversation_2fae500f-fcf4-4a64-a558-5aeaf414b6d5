import { useState } from 'react';
import { AlertTriangle } from 'lucide-react';

/**
 * Component to display content that may contain spoilers
 * Content is blurred by default and can be revealed by clicking
 */
const SpoilerContent = ({ content, isReply = false }) => {
  const [isRevealed, setIsRevealed] = useState(false);

  const toggleReveal = () => {
    setIsRevealed(!isRevealed);
  };

  return (
    <div className="w-full relative mt-1">
      <div
        onClick={toggleReveal}
        className={`
          relative cursor-pointer rounded-md
          ${isReply ? 'text-xs' : 'text-sm'} leading-relaxed
        `}
      >
        <div className="flex items-center gap-1.5 mb-1 opacity-80">
          <AlertTriangle size={isReply ? 12 : 14} className="text-yellow-500" />
          <span className="text-yellow-500 text-xs">Spoiler</span>
        </div>

        <p className={`
          ${isRevealed ? '' : 'blur-sm select-none'}
          transition-all duration-200 ease-in-out
        `}>
          {content}
        </p>

        <button
          onClick={(e) => {
            e.stopPropagation();
            toggleReveal();
          }}
          className="mt-1 text-xs text-blue-400 hover:text-blue-300 transition-colors"
        >
          {isRevealed ? 'Hide spoiler' : 'Show spoiler'}
        </button>
      </div>
    </div>
  );
};

export default SpoilerContent;
