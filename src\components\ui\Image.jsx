import { useEffect, useState } from "react";
import { LazyLoadImage } from "react-lazy-load-image-component";

const Image = ({ className, effect, src, proxy, children, quality = "high", ...ps }) => {
  const [s, ss] = useState(src || "");
  const p = "";

  useEffect(() => {
    ss(src || "");
  }, [src]);

  // Apply quality settings
  const qualityClass = quality === "high" ? "image-rendering-high" : "";

  return s?.length ? (
    <div className="relative overflow-hidden w-full h-full">
      <LazyLoadImage
        {...ps}
        effect={effect || "opacity"}
        width={"100%"}
        height={"100%"}
        src={proxy ? p + s : s}
        onError={() => ss("")}
        className={
          `size-full object-cover object-center !select-none shrink-0 ${qualityClass} ${className || ""}`
        }
        loading="lazy"
        placeholderSrc="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Ccircle cx='8.5' cy='8.5' r='1.5'%3E%3C/circle%3E%3Cpolyline points='21 15 16 10 5 21'%3E%3C/polyline%3E%3C/svg%3E"
      />
    </div>
  ) : (
    children || ""
  );
};

export default Image;
