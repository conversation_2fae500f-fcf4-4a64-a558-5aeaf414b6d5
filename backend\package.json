{"name": "anime-streaming-backend", "version": "1.0.0", "description": "Backend server for anime streaming", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"anisearch": "^1.0.14", "axios": "^1.8.4", "cheerio": "^1.0.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^4.18.2", "mongoose": "^8.13.3", "nyaa.si-client": "github:Ashu11-A/Nyaa.si", "webtorrent": "^2.1.32"}, "devDependencies": {"nodemon": "^3.0.1"}}