import { createContext, useEffect, useState } from "react";
import { toast } from "sonner";
import { makeAniListRequest, clearApiCache } from "@/utils/apiUtils";
import { trackAddAnime, trackRateAnime, trackUpdateProgress } from "@/utils/activityTracking";

// AniList API constants
const ANILIST_CLIENT_ID = "26304"; // Your AniList client ID

// Note: For the implicit flow, the redirect URI is configured in the AniList developer settings
// Make sure your redirect URI in AniList developer settings is set to:
// http://localhost:5173/auth/callback
// We don't need to include it in the auth URL for implicit flow

console.log("Using AniList client ID:", ANILIST_CLIENT_ID);

// Create context
export const AniListContext = createContext();

export const AniListProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [loading, setLoading] = useState(true);

  // Initialize auth state from localStorage
  useEffect(() => {
    const storedToken = localStorage.getItem("anilist_token");
    if (storedToken) {
      console.log("Found stored token, initializing session");
      setToken(storedToken);
      fetchUserData(storedToken);
    } else {
      console.log("No stored token found");
      setLoading(false);
    }

    // We're now using implicit flow, so we don't need to check for code parameter
    // The AuthCallback component will handle the token from the URL hash
  }, []);

  // We're now using implicit flow, so we don't need the authorization code exchange function

  // Fetch user data from AniList
  const fetchUserData = async (accessToken) => {
    try {
      setLoading(true);
      console.log("Fetching user data with token:", accessToken ? "Token exists (first 10 chars): " + accessToken.substring(0, 10) + "..." : "No token");

      const query = `
        query {
          Viewer {
            id
            name
            avatar {
              large
              medium
            }
            bannerImage
            about
            statistics {
              anime {
                count
                episodesWatched
                minutesWatched
                statuses {
                  status
                  count
                }
                genres {
                  genre
                  count
                  meanScore
                }
                formats {
                  format
                  count
                }
                releaseYears {
                  releaseYear
                  count
                }
              }
            }
          }
        }
      `;

      // Use our utility function with caching and retry logic
      const response = await makeAniListRequest({
        query,
        token: accessToken,
        cacheKey: "user_profile", // Cache the user profile data
        cacheTTL: 5 * 60 * 1000, // 5 minutes cache
      });

      console.log("Received response from AniList:", response);

      if (response.data && response.data.Viewer) {
        console.log("Successfully fetched user data:", response.data.Viewer);
        setUser(response.data.Viewer);
        toast.success(`Welcome, ${response.data.Viewer.name}!`);
      } else {
        console.error("Invalid response format:", response);
        toast.error("Failed to fetch user data: Invalid response format");

        // Don't automatically logout, just in case it's a temporary issue
        // Instead, just set loading to false
      }
    } catch (error) {
      console.error("Error fetching user data:", error);

      // If the error is related to authentication, log out
      if (error.response?.status === 401 ||
          (error.message && error.message.toLowerCase().includes("unauthorized"))) {
        toast.error("Authentication error: Your session has expired");
        logout();
      } else if (error.message && error.message.includes("Rate limit")) {
        // Rate limit errors are already handled by the utility function
        // No need to show another toast
      } else {
        // For other errors
        toast.error(`Failed to fetch user data: ${error.message || "Unknown error"}`);
      }
    } finally {
      setLoading(false);
    }
  };

  // Login function - redirects to AniList OAuth page using implicit flow
  const login = () => {
    // Using implicit grant flow according to AniList docs: https://docs.anilist.co/guide/auth/implicit
    // The redirect_uri parameter is not required for implicit flow, but we include it for completeness
    const authUrl = `https://anilist.co/api/v2/oauth/authorize?client_id=${ANILIST_CLIENT_ID}&response_type=token`;

    console.log("Redirecting to AniList auth URL (implicit flow):", authUrl);
    window.location.href = authUrl;
  };

  // Logout function
  const logout = () => {
    localStorage.removeItem("anilist_token");
    setToken(null);
    setUser(null);

    // Clear all API caches when logging out
    clearApiCache();

    toast.info("Logged out from AniList");
  };

  // Get user's anime list
  const getUserAnimeList = async (status = null) => {
    if (!token) {
      console.error("Cannot fetch anime list: No token available");
      toast.error("Please log in to view your anime list");
      return null;
    }

    if (!user || !user.id) {
      console.error("Cannot fetch anime list: No user data available");
      toast.error("User data not loaded. Please try again later.");
      return null;
    }

    try {
      const statusFilter = status ? `(status: ${status})` : "";

      console.log(`Fetching anime list for user ${user.id} with status filter: ${statusFilter || "none"}`);

      // Create a cache key based on user ID and status
      const cacheKey = `anime_list_${user.id}_${status || "all"}`;

      const query = `
        query {
          MediaListCollection(userId: ${user.id}, type: ANIME ${statusFilter}) {
            lists {
              name
              status
              entries {
                id
                mediaId
                status
                score
                progress
                media {
                  id
                  title {
                    romaji
                    english
                    native
                  }
                  coverImage {
                    large
                    medium
                  }
                  episodes
                  status
                  averageScore
                }
              }
            }
          }
        }
      `;

      // Use our utility function with caching and retry logic
      const response = await makeAniListRequest({
        query,
        token,
        cacheKey,
        cacheTTL: 2 * 60 * 1000, // 2 minutes cache for lists (shorter than profile)
      });

      if (response.data && response.data.MediaListCollection && response.data.MediaListCollection.lists) {
        const lists = response.data.MediaListCollection.lists;
        console.log(`Successfully fetched ${lists.length} anime lists`);
        return lists;
      } else {
        console.error("Invalid response format:", response);
        toast.error("Failed to fetch anime list: Invalid response format");
        return null;
      }
    } catch (error) {
      console.error("Error fetching anime list:", error);

      // If the error is related to authentication, log out
      if (error.response?.status === 401 ||
          (error.message && error.message.toLowerCase().includes("unauthorized"))) {
        toast.error("Authentication error: Your session has expired");
        logout();
      } else if (error.message && error.message.includes("Rate limit")) {
        // Rate limit errors are already handled by the utility function
        // No need to show another toast
      } else {
        // For other errors
        toast.error(`Failed to fetch anime list: ${error.message || "Unknown error"}`);
      }

      return null;
    }
  };

  // Update anime status on AniList
  const updateAnimeStatus = async (mediaId, status, progress = 0, score = 0) => {
    if (!token) {
      toast.error("You need to log in to update your list");
      return false;
    }

    try {
      console.log(`Updating anime status for media ID ${mediaId} to ${status} with progress ${progress} and score ${score}`);

      const query = `
        mutation ($mediaId: Int, $status: MediaListStatus, $progress: Int, $score: Float) {
          SaveMediaListEntry(mediaId: $mediaId, status: $status, progress: $progress, score: $score) {
            id
            status
            progress
            score
          }
        }
      `;

      const variables = {
        mediaId: parseInt(mediaId),
        status,
        progress
      };

      // Only include score if it's provided and greater than 0
      if (score > 0) {
        variables.score = score;
      }

      // Use our utility function with retry logic (no caching for mutations)
      const response = await makeAniListRequest({
        query,
        variables,
        token,
        maxRetries: 5, // More retries for important operations
      });

      // After successful update, clear the anime list cache to ensure fresh data
      clearApiCache(`anime_list_${user.id}_${status}`);
      clearApiCache(`anime_list_${user.id}_all`);

      if (response.data && response.data.SaveMediaListEntry) {
        console.log("Successfully updated anime status:", response.data.SaveMediaListEntry);
        toast.success(`Anime updated to ${status.replace("_", " ")}`);

        // Track user activity for task verification
        if (user && user.id) {
          // Track adding anime to list
          if (status === 'PLANNING' || status === 'CURRENT') {
            trackAddAnime(user.id, { id: mediaId });
          }

          // Track rating anime (if score is provided)
          if (variables.score) {
            trackRateAnime(user.id, { id: mediaId, score: variables.score });
          }

          // Track progress update
          if (progress > 0) {
            trackUpdateProgress(user.id, { id: mediaId, progress });
          }
        }

        return true;
      } else {
        console.error("Invalid response format:", response);
        toast.error("Failed to update anime: Invalid response format");
        return false;
      }
    } catch (error) {
      console.error("Error updating anime status:", error);

      // If the error is related to authentication, log out
      if (error.response?.status === 401 ||
          (error.message && error.message.toLowerCase().includes("unauthorized"))) {
        toast.error("Authentication error: Your session has expired");
        logout();
      } else if (error.message && error.message.includes("Rate limit")) {
        // Rate limit errors are already handled by the utility function
        // No need to show another toast
      } else {
        // For other errors
        toast.error(`Failed to update anime: ${error.message || "Unknown error"}`);
      }

      return false;
    }
  };

  // Delete anime from list
  const deleteAnimeFromList = async (mediaId) => {
    if (!token) {
      toast.error("You need to log in to update your list");
      return false;
    }

    if (!user || !user.id) {
      console.error("Cannot delete anime: No user data available");
      toast.error("User data not loaded. Please try again later.");
      return false;
    }

    try {
      console.log(`Deleting anime with media ID ${mediaId} from user ${user.id}'s list`);

      // First, get the list entry ID
      const findQuery = `
        query ($mediaId: Int, $userId: Int) {
          MediaList(mediaId: $mediaId, userId: $userId) {
            id
          }
        }
      `;

      const findVariables = {
        mediaId: parseInt(mediaId),
        userId: user.id
      };

      // Use our utility function with retry logic
      const listResponse = await makeAniListRequest({
        query: findQuery,
        variables: findVariables,
        token,
        maxRetries: 3,
      });

      const listEntryId = listResponse.data?.MediaList?.id;
      if (!listEntryId) {
        console.error("Anime not found in user's list");
        toast.error("Anime not found in your list");
        return false;
      }

      console.log(`Found list entry ID: ${listEntryId}, proceeding with deletion`);

      // Delete the entry
      const deleteQuery = `
        mutation ($id: Int) {
          DeleteMediaListEntry(id: $id) {
            deleted
          }
        }
      `;

      const deleteVariables = {
        id: listEntryId
      };

      // Use our utility function with retry logic
      const deleteResponse = await makeAniListRequest({
        query: deleteQuery,
        variables: deleteVariables,
        token,
        maxRetries: 5, // More retries for important operations
      });

      // After successful deletion, clear all anime list caches to ensure fresh data
      ["CURRENT", "PLANNING", "COMPLETED", "PAUSED", "DROPPED"].forEach(status => {
        clearApiCache(`anime_list_${user.id}_${status}`);
      });
      clearApiCache(`anime_list_${user.id}_all`);

      if (deleteResponse.data?.DeleteMediaListEntry?.deleted) {
        console.log("Successfully deleted anime from list");
        toast.success("Anime removed from your list");
        return true;
      } else {
        console.error("Invalid response format:", deleteResponse);
        toast.error("Failed to remove anime: Invalid response format");
        return false;
      }
    } catch (error) {
      console.error("Error deleting anime from list:", error);

      // If the error is related to authentication, log out
      if (error.response?.status === 401 ||
          (error.message && error.message.toLowerCase().includes("unauthorized"))) {
        toast.error("Authentication error: Your session has expired");
        logout();
      } else if (error.message && error.message.includes("Rate limit")) {
        // Rate limit errors are already handled by the utility function
        // No need to show another toast
      } else {
        // For other errors
        toast.error(`Failed to remove anime: ${error.message || "Unknown error"}`);
      }

      return false;
    }
  };

  // Check if an anime is in the user's list and get its status
  const checkAnimeInList = async (mediaId) => {
    if (!token || !user || !user.id) {
      return null;
    }

    try {
      console.log(`Checking if anime with media ID ${mediaId} is in user ${user.id}'s list`);

      const query = `
        query ($mediaId: Int, $userId: Int) {
          MediaList(mediaId: $mediaId, userId: $userId) {
            id
            status
            progress
            media {
              episodes
              title {
                romaji
                english
                native
              }
            }
          }
        }
      `;

      const variables = {
        mediaId: parseInt(mediaId),
        userId: user.id
      };

      // Use our utility function with caching and retry logic
      const response = await makeAniListRequest({
        query,
        variables,
        token,
        cacheKey: `anime_status_${user.id}_${mediaId}`,
        cacheTTL: 60 * 1000, // 1 minute cache
      });

      if (response.data && response.data.MediaList) {
        console.log(`Anime found in user's list with status: ${response.data.MediaList.status}`);
        return response.data.MediaList;
      } else {
        console.log("Anime not found in user's list");
        return null;
      }
    } catch (error) {
      console.error("Error checking anime in list:", error);
      return null;
    }
  };

  return (
    <AniListContext.Provider
      value={{
        user,
        token,
        loading,
        login,
        logout,
        getUserAnimeList,
        updateAnimeStatus,
        deleteAnimeFromList,
        checkAnimeInList,
        isAuthenticated: !!token
      }}
    >
      {children}
    </AniListContext.Provider>
  );
};
