import React, { useState, useEffect, useCallback } from 'react';
import { Loader2, Send, <PERSON>rk<PERSON>, Star, Settings, X, Save, Info } from 'lucide-react';
import { Link } from 'react-router-dom';
import Image from '@/components/ui/Image';
import axios from 'axios';

// API URL for anime data
const ANIME_API_URL = "https://raw.githubusercontent.com/bal-mackup/mal-backup/refs/heads/master/anilist/anime/";

// Fallback anime database with common titles for initial matching
const fallbackAnimeDatabase = {
  "death note": 1535,
  "attack on titan": 16498,
  "my hero academia": 21459,
  "demon slayer": 101922,
  "kimetsu no yaiba": 101922,
  "fullmetal alchemist: brotherhood": 5114,
  "fullmetal alchemist brotherhood": 5114,
  "one punch man": 21087,
  "spy x family": 140960,
  "naruto": 20,
  "naruto shippuden": 1735,
  "one piece": 21,
  "jujutsu kaisen": 113415,
  "hunter x hunter": 11061,
  "hunter x hunter (2011)": 11061,
  "steins;gate": 9253,
  "steins gate": 9253,
  "cowboy bebop": 1,
  "code geass": 1575,
  "tokyo ghoul": 20605,
  "sword art online": 11757,
  "violet evergarden": 21827,
  "your name": 21519,
  "kimi no na wa": 21519,
  "your lie in april": 20665,
  "shigatsu wa kimi no uso": 20665,
  "haikyuu": 20464,
  "dragon ball": 223,
  "dragon ball z": 813,
  "dragon ball super": 21175,
  "bleach": 269,
  "death parade": 20931,
  "mob psycho 100": 21507,
  "made in abyss": 97986,
  "vinland saga": 101348,
  "chainsaw man": 127230
};

const AIRecommendationsNew = () => {
  const [prompt, setPrompt] = useState('');
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [processingAnime, setProcessingAnime] = useState(false);
  const [error, setError] = useState(null);
  const [conversation, setConversation] = useState([]);
  const [animeCache, setAnimeCache] = useState({});

  // Function to fetch anime data from the API
  const fetchAnimeData = useCallback(async (animeId) => {
    // Check if we already have this anime in cache
    if (animeCache[animeId]) {
      return animeCache[animeId];
    }

    try {
      const response = await axios.get(`${ANIME_API_URL}${animeId}.json`);
      const animeData = response.data;

      // Extract the image URL from the response
      let imageUrl = null;
      if (animeData && animeData.image) {
        imageUrl = animeData.image;
      } else if (animeData && animeData.coverImage && animeData.coverImage.large) {
        imageUrl = animeData.coverImage.large;
      } else if (animeData && animeData.coverImage && animeData.coverImage.medium) {
        imageUrl = animeData.coverImage.medium;
      } else if (animeData && animeData.trendynyth && animeData.trendynyth.image) {
        imageUrl = animeData.trendynyth.image;
      }

      // Create a processed anime object
      const processedAnime = {
        id: animeId,
        title: animeData.title || `Anime #${animeId}`,
        image: imageUrl || `https://s4.anilist.co/file/anilistcdn/media/anime/cover/medium/b${animeId}.jpg`
      };

      // Update cache
      setAnimeCache(prev => ({
        ...prev,
        [animeId]: processedAnime
      }));

      return processedAnime;
    } catch (err) {
      console.error(`Error fetching anime data for ID ${animeId}:`, err);
      // Return a fallback object
      return {
        id: animeId,
        title: `Anime #${animeId}`,
        image: `https://s4.anilist.co/file/anilistcdn/media/anime/cover/medium/b${animeId}.jpg`
      };
    }
  }, [animeCache]);

  // Example recommendations with specific prompts to help users
  const examplePrompts = [
    "Recommend anime similar to Demon Slayer with great action scenes",
    "What are some psychological thriller anime like Death Note?",
    "I enjoy slice of life anime with heartwarming stories, what should I watch?",
    "Suggest anime with complex world-building and fantasy elements",
    "What are some underrated anime from the last 5 years?",
    "Recommend anime with beautiful animation and emotional storylines"
  ];

  // Settings state
  const [showSettings, setShowSettings] = useState(false);
  const [apiKey, setApiKey] = useState(() => {
    // Try to get API key from localStorage first
    const savedKey = localStorage.getItem('gemini_api_key');
    return savedKey || "AIzaSyBYZKDToEH6-s7-UPV-159RColNEUzCaOA"; // Default fallback key
  });
  const [tempApiKey, setTempApiKey] = useState('');

  // Save API key to localStorage
  const saveApiKey = () => {
    if (tempApiKey.trim()) {
      setApiKey(tempApiKey.trim());
      localStorage.setItem('gemini_api_key', tempApiKey.trim());
      setShowSettings(false);
    }
  };

  // Clear API key and use default
  const useDefaultApiKey = () => {
    const defaultKey = "AIzaSyBYZKDToEH6-s7-UPV-159RColNEUzCaOA";
    setApiKey(defaultKey);
    setTempApiKey('');
    localStorage.removeItem('gemini_api_key');
    setShowSettings(false);
  };

  // Function to fetch anime recommendations from Gemini API
  const fetchRecommendations = async (userPrompt) => {
    setLoading(true);
    setError(null);

    try {
      // Add user message to conversation
      const userMessage = userPrompt || prompt;
      const newConversation = [
        ...conversation,
        { role: 'user', content: userMessage }
      ];
      setConversation(newConversation);

      // Prepare the prompt for Gemini API
      const geminiPrompt = `
        You are an anime recommendation assistant. Based on the following request, recommend 3-5 anime titles.

        IMPORTANT: For each recommendation, you MUST provide:
        1. The title (exact official English title)
        2. A brief description (1-2 sentences)
        3. Genres (as an array of strings)
        4. A rating out of 10
        5. An ID - MUST be the EXACT AniList ID number (this is critical for linking to work)

        Here are some common anime with their EXACT AniList IDs for reference:
        - Death Note: ID 1535
        - Attack on Titan: ID 16498
        - My Hero Academia: ID 21459
        - Demon Slayer: ID 101922
        - Fullmetal Alchemist: Brotherhood: ID 5114
        - One Punch Man: ID 21087
        - Spy x Family: ID 140960
        - One Piece: ID 21
        - Naruto: ID 20
        - Jujutsu Kaisen: ID 113415
        - Hunter x Hunter (2011): ID 11061
        - Steins;Gate: ID 9253
        - Cowboy Bebop: ID 1
        - Code Geass: ID 1575

        CRITICAL: The ID MUST be the correct AniList ID number, not a random number. The system will use this ID to fetch the correct image and details.

        Also provide a brief explanation of why you're recommending these anime based on the user's request.

        Format your response as a JSON object with two properties:
        1. "recommendations": An array of anime objects with the properties: id, title, description, genres, rating
        2. "explanation": A string explaining your recommendations

        User request: ${userMessage}
      `;

      // Call the Gemini API with the endpoint using the user's API key or default
      const response = await axios.post(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`,
        {
          contents: [
            {
              parts: [
                {
                  text: geminiPrompt
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 2048
          }
        }
      );

      // Extract the response text
      const responseText = response.data.candidates[0].content.parts[0].text;

      // Extract the JSON from the response
      // The response might contain markdown code blocks or other text
      const jsonMatch = responseText.match(/```json\n([\s\S]*?)\n```/) ||
                        responseText.match(/```\n([\s\S]*?)\n```/) ||
                        responseText.match(/{[\s\S]*?}/);

      let parsedResponse;
      if (jsonMatch) {
        // If we found a code block or JSON object, parse it
        try {
          parsedResponse = JSON.parse(jsonMatch[1] || jsonMatch[0]);
        } catch (e) {
          console.error("Failed to parse JSON from response", e);
          throw new Error("Failed to parse recommendations from AI");
        }
      } else {
        throw new Error("AI response did not contain valid JSON");
      }

      // Validate the response format
      if (!parsedResponse.recommendations || !Array.isArray(parsedResponse.recommendations) || !parsedResponse.explanation) {
        throw new Error("AI response was not in the expected format");
      }

      // Process the recommendations
      const processRecommendations = async (recommendations) => {
        setProcessingAnime(true);
        const processedRecommendations = [];

        for (const anime of recommendations) {
          // Start with the original anime data
          let processedAnime = { ...anime };
          let animeId = null;

          // Try to find the anime in our database by title (case insensitive)
          const normalizedTitle = anime.title.toLowerCase().trim();

          // Check if we have this title in our fallback database
          if (fallbackAnimeDatabase[normalizedTitle]) {
            animeId = fallbackAnimeDatabase[normalizedTitle];
            console.log(`Found exact match in database for: ${anime.title} (ID: ${animeId})`);
          } else {
            // If no exact match, try to find a partial match
            const partialMatches = Object.keys(fallbackAnimeDatabase).filter(key =>
              normalizedTitle.includes(key) || key.includes(normalizedTitle)
            );

            if (partialMatches.length > 0) {
              // Use the longest partial match (likely the most specific)
              const bestMatch = partialMatches.reduce((a, b) => a.length > b.length ? a : b);
              animeId = fallbackAnimeDatabase[bestMatch];
              console.log(`Found partial match in database: "${bestMatch}" for "${anime.title}" (ID: ${animeId})`);
            } else {
              // If still no match, try to extract ID from any provided image URL
              if (anime.image) {
                const anilistMatch = anime.image.match(/\/b(\d+)(?:-[A-Za-z0-9]+)?\.jpg/);
                if (anilistMatch && anilistMatch[1]) {
                  animeId = parseInt(anilistMatch[1]);
                  console.log(`Extracted ID ${animeId} from image URL for: ${anime.title}`);
                }

                // Check for img.anili.st format
                const aniliMatch = anime.image.match(/img\.anili\.st\/media\/(\d+)/);
                if (!animeId && aniliMatch && aniliMatch[1]) {
                  animeId = parseInt(aniliMatch[1]);
                  console.log(`Extracted ID ${animeId} from anili.st URL for: ${anime.title}`);
                }
              }

              // Last resort: use the ID provided by the API if it seems valid
              if (!animeId && anime.id && !isNaN(anime.id) && anime.id > 0 && anime.id < 150000) {
                animeId = parseInt(anime.id);
                console.log(`Using API-provided ID ${animeId} for: ${anime.title}`);
              }
            }
          }

          // If we found an ID, fetch the anime data from the API
          if (animeId) {
            try {
              const animeData = await fetchAnimeData(animeId);

              // Update the processed anime with the fetched data
              processedAnime.id = animeId;
              processedAnime.image = animeData.image;

              // If the API returned a title and it seems more accurate, use it
              if (animeData.title && animeData.title.length > 3) {
                // Keep the original title if it's more specific
                if (anime.title.length > animeData.title.length) {
                  console.log(`Keeping original title: "${anime.title}" instead of "${animeData.title}"`);
                } else {
                  processedAnime.title = animeData.title;
                }
              }
            } catch (err) {
              console.error(`Error processing anime ${anime.title}:`, err);
              // Keep the original data but ensure we have an ID
              processedAnime.id = animeId;
              processedAnime.image = processedAnime.image || `https://s4.anilist.co/file/anilistcdn/media/anime/cover/medium/b${animeId}.jpg`;
            }
          } else {
            // If all else fails, mark as unknown
            console.warn(`Could not find valid ID for: ${anime.title}`);
            processedAnime.id = 0;
            processedAnime.image = "https://via.placeholder.com/225x320?text=No+Image";
            processedAnime.unknownAnime = true;
          }

          // Ensure we have genres as an array
          processedAnime.genres = Array.isArray(anime.genres) ? anime.genres : [anime.genres].filter(Boolean);

          // Ensure rating is a number
          processedAnime.rating = parseFloat(anime.rating) || 0;

          processedRecommendations.push(processedAnime);
        }

        setProcessingAnime(false);
        return processedRecommendations;
      };

      // Process recommendations and update state
      const processedRecommendations = await processRecommendations(parsedResponse.recommendations);

      // Find a featured recommendation (highest rated or first one)
      let featuredAnime = null;
      if (processedRecommendations.length > 0) {
        // Try to find the highest rated anime
        featuredAnime = processedRecommendations.reduce((prev, current) =>
          (prev.rating > current.rating) ? prev : current
        );
      }

      // Add emojis and personalized message based on content
      let enhancedExplanation = parsedResponse.explanation;

      // Add emojis based on content
      if (enhancedExplanation.toLowerCase().includes('action')) {
        enhancedExplanation = enhancedExplanation.replace(/action/i, "action 💥");
      }
      if (enhancedExplanation.toLowerCase().includes('romance')) {
        enhancedExplanation = enhancedExplanation.replace(/romance/i, "romance ❤️");
      }
      if (enhancedExplanation.toLowerCase().includes('comedy')) {
        enhancedExplanation = enhancedExplanation.replace(/comedy/i, "comedy 😂");
      }
      if (enhancedExplanation.toLowerCase().includes('fantasy')) {
        enhancedExplanation = enhancedExplanation.replace(/fantasy/i, "fantasy ✨");
      }
      if (enhancedExplanation.toLowerCase().includes('drama')) {
        enhancedExplanation = enhancedExplanation.replace(/drama/i, "drama 😢");
      }

      // Add featured recommendation highlight if we have one
      if (featuredAnime) {
        enhancedExplanation += `\n\n✨ **I think you'll absolutely love "${featuredAnime.title}"!** It's a standout anime that matches what you're looking for. Give it a try! ✨`;
      }

      // Add AI response to conversation with enhanced content
      setConversation([
        ...newConversation,
        {
          role: 'assistant',
          content: enhancedExplanation,
          recommendations: processedRecommendations,
          featuredAnime: featuredAnime
        }
      ]);

      setRecommendations(processedRecommendations);
      setLoading(false);
      setPrompt(''); // Clear the input field after sending

    } catch (err) {
      console.error('Error fetching recommendations:', err);
      setError(err.message || 'Failed to get recommendations. Please try again.');
      setLoading(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!prompt.trim()) return;
    fetchRecommendations();
  };

  const handleExampleClick = (examplePrompt) => {
    setPrompt(examplePrompt);
    // Keep the prompt in the input field after sending
    fetchRecommendations(examplePrompt);
  };

  // Reference to the chat container for auto-scrolling
  const chatContainerRef = React.useRef(null);

  // Auto-scroll to bottom when conversation updates
  useEffect(() => {
    if (chatContainerRef.current) {
      const chatContainer = chatContainerRef.current;
      chatContainer.scrollTop = chatContainer.scrollHeight;
    }
  }, [conversation]);

  // Initialize with a welcome message
  useEffect(() => {
    // Add a welcome message to the conversation
    setConversation([
      {
        role: 'assistant',
        content: 'Hello there! I\'m Yor Forger, assassin by night and anime expert by day. ✨ I\'d be delighted to help you discover new anime based on your preferences. What kind of anime are you looking for? 🎬'
      }
    ]);
  }, []);

  // Settings Modal Component
  const SettingsModal = () => {
    // Initialize tempApiKey when opening settings
    useEffect(() => {
      if (showSettings) {
        setTempApiKey(apiKey === "AIzaSyBYZKDToEH6-s7-UPV-159RColNEUzCaOA" ? "" : apiKey);
      }
    }, [showSettings]);

    if (!showSettings) return null;

    return (
      <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-black/80 border border-white/20 rounded-xl w-full max-w-md overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-white/10">
            <h2 className="text-white font-medium flex items-center gap-2">
              <Settings size={18} className="text-white/70" />
              API Settings
            </h2>
            <button
              onClick={() => setShowSettings(false)}
              className="text-white/70 hover:text-white transition-colors"
            >
              <X size={20} />
            </button>
          </div>

          {/* Content */}
          <div className="p-5">
            <div className="mb-5">
              <p className="text-white/80 text-sm mb-4">
                Enter your own Gemini API key to use for anime recommendations.
                You can get a free API key from the <a href="https://aistudio.google.com/" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">Google AI Studio</a>.
              </p>

              <div className="bg-black/40 border border-white/10 rounded-lg p-3 mb-4">
                <div className="flex items-start gap-2">
                  <Info size={16} className="text-blue-400 mt-0.5 flex-shrink-0" />
                  <p className="text-white/70 text-xs">
                    Your API key is stored locally in your browser and is never sent to our servers.
                    Using your own API key gives you full control over your usage and billing.
                  </p>
                </div>
              </div>
            </div>

            <div className="mb-5">
              <label className="block text-white/80 text-sm mb-2">Gemini API Key</label>
              <input
                type="text"
                value={tempApiKey}
                onChange={(e) => setTempApiKey(e.target.value)}
                placeholder="Enter your Gemini API key..."
                className="w-full bg-black/40 border border-white/20 rounded-lg px-3 py-2 text-white text-sm placeholder:text-white/40 focus:outline-none focus:border-white/50"
              />
            </div>

            <div className="flex items-center justify-between gap-3 mt-6">
              <button
                onClick={useDefaultApiKey}
                className="px-4 py-2 bg-black/40 hover:bg-black/60 border border-white/10 rounded-lg text-white/80 text-sm transition-colors"
              >
                Use Default Key
              </button>

              <button
                onClick={saveApiKey}
                disabled={!tempApiKey.trim()}
                className="px-4 py-2 bg-white/10 hover:bg-white/20 disabled:bg-white/5 disabled:text-white/40 border border-white/20 rounded-lg text-white text-sm transition-colors flex items-center gap-2"
              >
                <Save size={16} />
                Save API Key
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="h-[calc(100vh-80px)] flex flex-col relative">
      {/* Settings Modal */}
      <SettingsModal />

      {/* Glass effect background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black/90 to-black/70 backdrop-blur-md z-0"></div>
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-20 -right-20 w-60 h-60 bg-white/5 rounded-full blur-xl"></div>
        <div className="absolute top-1/3 -left-20 w-40 h-40 bg-white/5 rounded-full blur-xl"></div>
        <div className="absolute -bottom-20 right-1/3 w-60 h-60 bg-white/5 rounded-full blur-xl"></div>
      </div>

      {/* Main content */}
      <div className="relative z-10 flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-white/10 bg-black/40 backdrop-blur-md">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-black/60 backdrop-blur-md border border-white/20 overflow-hidden">
              <img
                src="https://i.pinimg.com/736x/5e/9c/a6/5e9ca6dcaf1ae57da33acb5f8f2820f9.jpg"
                alt="Yor Forger"
                className="w-full h-full object-cover"
              />
            </div>
            <div>
              <h1 className="text-lg font-medium text-white">Yor Forger</h1>
              <p className="text-xs text-white/60">Your Personal Anime Expert</p>
            </div>
          </div>

          {/* Settings Button */}
          <button
            onClick={() => setShowSettings(true)}
            className="w-9 h-9 rounded-full bg-black/40 border border-white/10 flex items-center justify-center text-white/70 hover:text-white hover:bg-black/60 hover:border-white/30 transition-all"
            title="API Settings"
          >
            <Settings size={16} />
          </button>
        </div>

        {/* Two-column layout */}
        <div className="flex flex-1 overflow-hidden">
          {/* Main chat area */}
          <div className="flex-1 flex flex-col h-full">
            {/* Chat messages */}
            <div ref={chatContainerRef} className="flex-1 overflow-y-auto p-4 md:p-6 space-y-6">
              {conversation.map((message, index) => (
                <div
                  key={index}
                  className={`flex items-start gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  {/* AI Avatar (only for assistant messages) */}
                  {message.role === 'assistant' && (
                    <div className="flex flex-col">
                      <span className="text-xs text-white/60 ml-2 mb-1">Yor</span>
                      <div className="w-10 h-10 rounded-full bg-black/40 backdrop-blur-md border border-white/20 flex-shrink-0 overflow-hidden">
                        <img
                          src="https://i.pinimg.com/736x/5e/9c/a6/5e9ca6dcaf1ae57da33acb5f8f2820f9.jpg"
                          alt="Yor Forger"
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                  )}

                  {/* Message Content */}
                  <div
                    className={`max-w-[85%] rounded-2xl p-4 backdrop-blur-sm ${
                      message.role === 'user'
                        ? 'bg-white/15 border border-white/20 rounded-tr-none'
                        : 'bg-black/40 border border-white/10 rounded-tl-none'
                    }`}
                  >
                    {message.role === 'assistant' ? (
                      message.recommendations ? (
                        <div className="space-y-4">
                          <div className="text-white/90 text-sm leading-relaxed">
                            {message.content.split('\n\n').map((paragraph, i) => {
                              // Check if this is the featured anime paragraph
                              if (paragraph.includes('**I think you\'ll absolutely love') && message.featuredAnime) {
                                return (
                                  <div key={i} className="mt-3 p-3 bg-white/10 border border-white/20 rounded-lg">
                                    <p className="font-medium text-white">
                                      {paragraph.replace(/\*\*/g, '')}
                                    </p>
                                  </div>
                                );
                              }
                              // Regular paragraph
                              return <p key={i} className={i > 0 ? "mt-3" : ""}>{paragraph}</p>;
                            })}
                          </div>

                          {/* Recommendations List */}
                          <div className="mt-4 grid grid-cols-1 gap-3">
                            {message.recommendations.map((anime) => (
                              <div
                                key={anime.id}
                                className={`group backdrop-blur-sm rounded-xl overflow-hidden transition-all hover:shadow-lg
                                  ${message.featuredAnime && message.featuredAnime.id === anime.id
                                    ? 'bg-black/70 border-2 border-white/30 shadow-lg'
                                    : 'bg-black/60 border border-white/10 hover:border-white/30'
                                  }`}
                              >
                                <div className="flex">
                                  {/* Anime Image */}
                                  <div className="w-20 h-28 relative flex-shrink-0">
                                    <Image src={anime.image} className="object-cover w-full h-full" />
                                    <div className="absolute inset-0 bg-gradient-to-r from-transparent to-black/60"></div>
                                  </div>

                                  {/* Anime Info */}
                                  <div className="p-3 flex-1">
                                    <div className="flex items-start justify-between">
                                      <div>
                                        <h3 className="font-medium text-white text-sm">
                                          <Link
                                            to={`/anime/${anime.id}`}
                                            className="hover:text-white/80 transition-colors"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                          >
                                            {anime.title}
                                          </Link>
                                        </h3>
                                        {message.featuredAnime && message.featuredAnime.id === anime.id && (
                                          <div className="mt-1 inline-flex items-center gap-1 bg-white/20 backdrop-blur-md rounded-full px-2 py-0.5 text-[10px]">
                                            <span className="text-white">✨ Recommended for you</span>
                                          </div>
                                        )}
                                      </div>
                                      <div className="flex items-center gap-1 bg-black/40 backdrop-blur-md rounded-full px-2 py-0.5 text-xs ml-1 flex-shrink-0">
                                        <Star size={10} className="text-yellow-400 fill-yellow-400" />
                                        <span className="text-yellow-400">{anime.rating}</span>
                                      </div>
                                    </div>

                                    <p className="text-white/70 text-xs mt-1 line-clamp-2">{anime.description}</p>

                                    <div className="flex flex-wrap gap-1 mt-2">
                                      {anime.genres.slice(0, 3).map((genre, idx) => (
                                        <span key={idx} className="text-[10px] bg-white/10 backdrop-blur-sm px-2 py-0.5 rounded-full text-white/90">
                                          {genre}
                                        </span>
                                      ))}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>

                          {/* Dynamic Quick Reply Buttons for recommendations */}
                          <div className="mt-4 pt-3 border-t border-white/10">
                            <div className="flex flex-wrap gap-2">
                              {/* Always show the first anime button */}
                              {message.recommendations[0] && (
                                <button
                                  onClick={() => {
                                    const newPrompt = `Tell me more about ${message.recommendations[0]?.title}`;
                                    setPrompt(newPrompt);
                                    setTimeout(() => fetchRecommendations(newPrompt), 0);
                                  }}
                                  className="text-xs bg-white/10 hover:bg-white/15 backdrop-blur-sm border border-white/20 rounded-full py-1.5 px-3 text-white/90 transition-all"
                                >
                                  Tell me more about {message.recommendations[0]?.title.split(':')[0]}
                                </button>
                              )}

                              {/* Dynamically generate genre-specific buttons */}
                              {(() => {
                                const content = message.content.toLowerCase();
                                const quickReplies = [];

                                // Extract genres from recommendations
                                const allGenres = message.recommendations
                                  .flatMap(anime => anime.genres || [])
                                  .filter(Boolean);

                                // Get unique genres
                                const uniqueGenres = [...new Set(allGenres)];

                                // If we have genres, create a related button
                                if (uniqueGenres.length > 0) {
                                  // Pick a prominent genre
                                  const prominentGenre = uniqueGenres[0];
                                  quickReplies.push(
                                    <button
                                      key="more-genre"
                                      onClick={() => {
                                        const newPrompt = `More ${prominentGenre} anime recommendations`;
                                        setPrompt(newPrompt);
                                        setTimeout(() => fetchRecommendations(newPrompt), 0);
                                      }}
                                      className="text-xs bg-white/10 hover:bg-white/15 backdrop-blur-sm border border-white/20 rounded-full py-1.5 px-3 text-white/90 transition-all"
                                    >
                                      More {prominentGenre} anime
                                    </button>
                                  );
                                } else {
                                  // Fallback if no genres
                                  quickReplies.push(
                                    <button
                                      key="other-genres"
                                      onClick={() => {
                                        const newPrompt = "Any other genres?";
                                        setPrompt(newPrompt);
                                        setTimeout(() => fetchRecommendations(newPrompt), 0);
                                      }}
                                      className="text-xs bg-white/10 hover:bg-white/15 backdrop-blur-sm border border-white/20 rounded-full py-1.5 px-3 text-white/90 transition-all"
                                    >
                                      Any other genres?
                                    </button>
                                  );
                                }

                                // Add a related anime button if we can detect a theme
                                if (content.includes('action') ||
                                    uniqueGenres.includes('Action') ||
                                    uniqueGenres.includes('Shounen')) {
                                  quickReplies.push(
                                    <button
                                      key="similar-action"
                                      onClick={() => {
                                        const newPrompt = "Similar action anime with great animation";
                                        setPrompt(newPrompt);
                                        setTimeout(() => fetchRecommendations(newPrompt), 0);
                                      }}
                                      className="text-xs bg-white/10 hover:bg-white/15 backdrop-blur-sm border border-white/20 rounded-full py-1.5 px-3 text-white/90 transition-all"
                                    >
                                      Great animation
                                    </button>
                                  );
                                } else if (content.includes('romance') ||
                                           uniqueGenres.includes('Romance') ||
                                           uniqueGenres.includes('Drama')) {
                                  quickReplies.push(
                                    <button
                                      key="similar-romance"
                                      onClick={() => {
                                        const newPrompt = "Romance anime with emotional stories";
                                        setPrompt(newPrompt);
                                        setTimeout(() => fetchRecommendations(newPrompt), 0);
                                      }}
                                      className="text-xs bg-white/10 hover:bg-white/15 backdrop-blur-sm border border-white/20 rounded-full py-1.5 px-3 text-white/90 transition-all"
                                    >
                                      Emotional stories
                                    </button>
                                  );
                                }

                                // Always add the thanks button
                                quickReplies.push(
                                  <button
                                    key="thanks"
                                    onClick={() => {
                                      const newPrompt = "Thanks Yor!";
                                      setPrompt("");
                                      // Add user message
                                      const userMessage = { role: 'user', content: newPrompt };
                                      // Add predefined response without API call
                                      const assistantMessage = {
                                        role: 'assistant',
                                        content: "You're welcome! I'm happy to help with more anime recommendations anytime. Enjoy watching!"
                                      };
                                      // Update conversation with both messages
                                      setConversation(prev => [...prev, userMessage, assistantMessage]);
                                    }}
                                    className="text-xs bg-white/10 hover:bg-white/15 backdrop-blur-sm border border-white/20 rounded-full py-1.5 px-3 text-white/90 transition-all"
                                  >
                                    Thanks Yor!
                                  </button>
                                );

                                return quickReplies;
                              })()}
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <div className="text-white/90 text-sm leading-relaxed">
                            {message.content.split('\n\n').map((paragraph, i) => (
                              <p key={i} className={i > 0 ? "mt-3" : ""}>{paragraph}</p>
                            ))}
                          </div>

                          {/* Dynamic Quick Reply Buttons based on message content */}
                          <div className="mt-4 pt-3 border-t border-white/10">
                            <div className="flex flex-wrap gap-2">
                              {/* Dynamically generate quick replies based on message content */}
                              {(() => {
                                const content = message.content.toLowerCase();
                                const quickReplies = [];

                                // Default "Thanks" button that's always available
                                quickReplies.push(
                                  <button
                                    key="thanks"
                                    onClick={() => {
                                      const newPrompt = "Thanks Yor!";
                                      setPrompt("");
                                      // Add user message
                                      const userMessage = { role: 'user', content: newPrompt };
                                      // Add predefined response without API call
                                      const assistantMessage = {
                                        role: 'assistant',
                                        content: "You're welcome! I'm happy to help with more anime recommendations anytime. Enjoy watching!"
                                      };
                                      // Update conversation with both messages
                                      setConversation(prev => [...prev, userMessage, assistantMessage]);
                                    }}
                                    className="text-xs bg-white/10 hover:bg-white/15 backdrop-blur-sm border border-white/20 rounded-full py-1.5 px-3 text-white/90 transition-all"
                                  >
                                    Thanks Yor!
                                  </button>
                                );

                                // If welcome message or no specific context yet
                                if (content.includes('welcome') || content.includes('hello') || content.includes('hi there') ||
                                    content.includes('what kind of anime') || index === 0) {
                                  quickReplies.push(
                                    <button
                                      key="popular"
                                      onClick={() => {
                                        const newPrompt = "Show me popular anime";
                                        setPrompt(newPrompt);
                                        setTimeout(() => fetchRecommendations(newPrompt), 0);
                                      }}
                                      className="text-xs bg-white/10 hover:bg-white/15 backdrop-blur-sm border border-white/20 rounded-full py-1.5 px-3 text-white/90 transition-all"
                                    >
                                      Popular anime
                                    </button>
                                  );
                                  quickReplies.push(
                                    <button
                                      key="action"
                                      onClick={() => {
                                        const newPrompt = "I like action anime";
                                        setPrompt(newPrompt);
                                        setTimeout(() => fetchRecommendations(newPrompt), 0);
                                      }}
                                      className="text-xs bg-white/10 hover:bg-white/15 backdrop-blur-sm border border-white/20 rounded-full py-1.5 px-3 text-white/90 transition-all"
                                    >
                                      Action anime
                                    </button>
                                  );
                                }

                                // If message mentions specific genres
                                if (content.includes('action')) {
                                  quickReplies.push(
                                    <button
                                      key="more-action"
                                      onClick={() => {
                                        const newPrompt = "More action anime with great fight scenes";
                                        setPrompt(newPrompt);
                                        setTimeout(() => fetchRecommendations(newPrompt), 0);
                                      }}
                                      className="text-xs bg-white/10 hover:bg-white/15 backdrop-blur-sm border border-white/20 rounded-full py-1.5 px-3 text-white/90 transition-all"
                                    >
                                      More action anime
                                    </button>
                                  );
                                }

                                if (content.includes('romance') || content.includes('love')) {
                                  quickReplies.push(
                                    <button
                                      key="romance"
                                      onClick={() => {
                                        const newPrompt = "Best romance anime with happy endings";
                                        setPrompt(newPrompt);
                                        setTimeout(() => fetchRecommendations(newPrompt), 0);
                                      }}
                                      className="text-xs bg-white/10 hover:bg-white/15 backdrop-blur-sm border border-white/20 rounded-full py-1.5 px-3 text-white/90 transition-all"
                                    >
                                      Romance with happy endings
                                    </button>
                                  );
                                }

                                if (content.includes('fantasy') || content.includes('adventure')) {
                                  quickReplies.push(
                                    <button
                                      key="fantasy"
                                      onClick={() => {
                                        const newPrompt = "Epic fantasy anime with world-building";
                                        setPrompt(newPrompt);
                                        setTimeout(() => fetchRecommendations(newPrompt), 0);
                                      }}
                                      className="text-xs bg-white/10 hover:bg-white/15 backdrop-blur-sm border border-white/20 rounded-full py-1.5 px-3 text-white/90 transition-all"
                                    >
                                      Epic fantasy worlds
                                    </button>
                                  );
                                }

                                // If message mentions specific anime titles, offer related suggestions
                                if (content.includes('naruto') || content.includes('dragon ball') || content.includes('one piece')) {
                                  quickReplies.push(
                                    <button
                                      key="shonen"
                                      onClick={() => {
                                        const newPrompt = "More popular shonen anime like Naruto and One Piece";
                                        setPrompt(newPrompt);
                                        setTimeout(() => fetchRecommendations(newPrompt), 0);
                                      }}
                                      className="text-xs bg-white/10 hover:bg-white/15 backdrop-blur-sm border border-white/20 rounded-full py-1.5 px-3 text-white/90 transition-all"
                                    >
                                      More shonen anime
                                    </button>
                                  );
                                }

                                // If no specific context is detected, add a generic option
                                if (quickReplies.length < 3) {
                                  quickReplies.push(
                                    <button
                                      key="underrated"
                                      onClick={() => {
                                        const newPrompt = "Underrated anime I should watch";
                                        setPrompt(newPrompt);
                                        setTimeout(() => fetchRecommendations(newPrompt), 0);
                                      }}
                                      className="text-xs bg-white/10 hover:bg-white/15 backdrop-blur-sm border border-white/20 rounded-full py-1.5 px-3 text-white/90 transition-all"
                                    >
                                      Underrated gems
                                    </button>
                                  );
                                }

                                return quickReplies;
                              })()}
                            </div>
                          </div>
                        </div>
                      )
                    ) : (
                      // User message - no quick replies
                      <div className="text-white/90 text-sm leading-relaxed">
                        {message.content.split('\n\n').map((paragraph, i) => (
                          <p key={i} className={i > 0 ? "mt-3" : ""}>{paragraph}</p>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* User Avatar (only for user messages) */}
                  {message.role === 'user' && (
                    <div className="w-10 h-10 rounded-full bg-white/15 backdrop-blur-md border border-white/20 flex items-center justify-center flex-shrink-0">
                      <div className="w-5 h-5 bg-white/80 rounded-full"></div>
                    </div>
                  )}
                </div>
              ))}

              {/* Loading indicator */}
              {(loading || processingAnime) && (
                <div className="flex items-start gap-3 justify-start">
                  <div className="flex flex-col">
                    <span className="text-xs text-white/60 ml-2 mb-1 block">Yor</span>
                    <div className="w-10 h-10 rounded-full bg-black/40 backdrop-blur-md border border-white/20 flex-shrink-0 overflow-hidden">
                      <img
                        src="https://i.pinimg.com/736x/5e/9c/a6/5e9ca6dcaf1ae57da33acb5f8f2820f9.jpg"
                        alt="Yor Forger"
                        className="w-full h-full object-cover opacity-70"
                      />
                    </div>
                  </div>
                  <div className="bg-black/40 backdrop-blur-sm border border-white/10 rounded-2xl rounded-tl-none p-4 flex items-center gap-3 max-w-[75%]">
                    <Loader2 size={16} className="animate-spin text-white" />
                    <span className="text-white/90 text-sm">
                      {loading ? "Analyzing your request..." : "Gathering intelligence on these anime..."}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Input area */}
            <div className="p-4 backdrop-blur-md bg-black/20 border-t border-white/10">
              <form onSubmit={handleSubmit} className="flex gap-3 items-center">
                <input
                  type="text"
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="Ask Yor for anime recommendations..."
                  className="flex-1 bg-black/40 backdrop-blur-sm border border-white rounded-full px-4 py-3 text-white text-sm placeholder:text-white/50 focus:outline-none focus:border-white focus:bg-black/50 transition-all"
                />
                <button
                  type="submit"
                  disabled={loading || processingAnime || !prompt.trim()}
                  className="bg-white/15 hover:bg-white/25 backdrop-blur-sm border border-white disabled:border-white/50 disabled:bg-white/5 disabled:text-white/40 text-white rounded-full w-12 h-12 flex items-center justify-center transition-all disabled:cursor-not-allowed"
                >
                  {loading || processingAnime ? <Loader2 size={20} className="animate-spin" /> : <Send size={20} />}
                </button>
              </form>
              {error && <p className="text-red-400 mt-2 text-xs px-2">{error}</p>}
            </div>
          </div>

          {/* Right sidebar */}
          <div className="hidden lg:flex flex-col w-80 border-l border-white/10 bg-black/30 backdrop-blur-md">
            {/* About Yor section */}
            <div className="p-5 border-b border-white/10 bg-black/40">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-12 h-12 rounded-full overflow-hidden border border-white/20">
                  <img
                    src="https://i.pinimg.com/736x/5e/9c/a6/5e9ca6dcaf1ae57da33acb5f8f2820f9.jpg"
                    alt="Yor Forger"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <h2 className="text-white font-medium">Yor Forger</h2>
                  <p className="text-xs text-white/60">Anime Expert</p>
                </div>
              </div>
              <p className="text-xs leading-relaxed text-white/70 bg-black/30 rounded-lg p-3 border border-white/10">
                Yor Forger, the elegant assassin from Spy x Family, is your personal anime expert. Powered by advanced AI, she'll help you discover your next favorite anime series with her impeccable taste and recommendations.
              </p>
            </div>

            {/* Suggested prompts section */}
            <div className="p-4 border-b border-white/10">
              <h2 className="text-white font-medium text-sm flex items-center gap-2">
                <span className="w-1 h-4 bg-white/80 rounded-full"></span>
                Try asking about...
              </h2>
            </div>

            <div className="p-3 overflow-y-auto flex-1">
              <div className="grid grid-cols-1 gap-2">
                {examplePrompts.map((examplePrompt, index) => (
                  <button
                    key={index}
                    onClick={() => handleExampleClick(examplePrompt)}
                    className="w-full text-left bg-gradient-to-r from-black/40 to-black/60 hover:from-black/50 hover:to-black/70 backdrop-blur-sm border border-white/10 hover:border-white/30 rounded-lg p-3 text-xs text-white/90 transition-all"
                  >
                    <div className="flex items-start gap-2">
                      <div className="w-5 h-5 rounded-full bg-white/10 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-[10px] text-white/80">{index + 1}</span>
                      </div>
                      <span>{examplePrompt}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Tips section */}
            <div className="p-4 border-t border-white/10 bg-black/40">
              <div className="text-xs text-white/50 mb-2 flex items-center gap-2">
                <span className="w-1 h-4 bg-white/80 rounded-full"></span>
                Pro Tips
              </div>
              <ul className="text-xs text-white/70 space-y-2 pl-3">
                <li className="list-disc list-inside">Be specific about genres you enjoy</li>
                <li className="list-disc list-inside">Mention anime you've already watched</li>
                <li className="list-disc list-inside">Ask about specific themes or elements</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIRecommendationsNew;
