import AddToList from "@/components/AddToList";
import Image from "@/components/ui/Image";
import useWatchlist from "@/hooks/useWatchlist";
import { Star } from "lucide-react";
import { Link } from "react-router-dom";

const Watchlist = () => {
  const { watchlist } = useWatchlist();
  const ww = (watchlist || [])
    ?.slice()
    .sort((a, b) => b?.dateAdded - a?.dateAdded);
    document.title = "Watchlist";
  return (
    <div className="w-full py-4 flex flex-col gap-4">
      <div className="flex w-full text-xl md:text-2xl xl:text-3xl font-semibold">
        Watchlist
      </div>
      <div className="flex w-full flex-wrap gap-y-4 ">
        {ww?.length ? (
          ww?.map((w) => {
            return (
              <div
                key={`${w?.type}-${w?.id}`}
                className="flex p-2 w-1/2 sm:w-1/4 md:w-1/5 lg:w-1/6 xl:w-[14.28%] flex-wrap shrink-0"
              >
                <div className="flex flex-col group w-full gap-1">
                  <div className="flex w-full aspect-[1/1.45] pp smooth relative bg-white/5 shrink-0 rounded-xl overflow-hidden">
                    <Link
                      to={`/watch/${w?.type}/${w?.id}`}
                      className="size-full"
                    >
                      <Image src={w?.image} />
                    </Link>
                    <span className="flex flex-col gap-1 absolute items-end text-xs left-1 top-1">
                      {Number(w?.rating) > 0 && (
                        <span className="bg-black/75 p-[.1rem] px-1 gap-1 rounded-md flex items-center">
                          <Star fill="gold" color="gold" size={12} />
                          {Number(w?.rating)?.toFixed(1) || "n/a"}
                        </span>
                      )}
                    </span>
                    {/* Removed AddToList */}
                  </div>
                  <Link
                    to={`/watch/${w?.type}/${w?.id}`}
                    className="flex w-full gap-1 shrink-0 flex-col"
                  >
                    <div className="w-full flex text-xs items-center justify-between">
                      <span className="uppercase">{w?.type}</span>
                      <span>
                        {w?.release_date ? w?.release_date?.slice(-4) : ""}
                      </span>
                    </div>
                    <div className="line-clamp-2 text-sm lg:text-base tracking-wider">
                      {w?.title}
                    </div>
                  </Link>
                </div>
              </div>
            );
          })
        ) : (
          <span className="text-sm text-gray-300 flex items-center size-full justify-center gap-1 p-10">
            Nothing here yet
          </span>
        )}
      </div>
    </div>
  );
};

export default Watchlist;
