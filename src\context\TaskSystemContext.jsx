import { createContext, useContext, useState, useEffect } from "react";
import { useAniList } from "@/hooks/useAniList";
import { toast } from "sonner";
import profileApi from "@/api/profileApi";
import { useUserActivity } from "@/context/UserActivityContext";

// Create context
const TaskSystemContext = createContext();

// Task definitions
const TASK_DEFINITIONS = [
  {
    id: "visit",
    title: "Daily Visit",
    description: "Visit the site today",
    xpReward: 10,
    icon: "calendar",
    difficulty: "easy",
    autoComplete: true
  },
  {
    id: "watch-episode",
    title: "Watch an Episode",
    description: "Watch at least one anime episode",
    xpReward: 15,
    icon: "play",
    difficulty: "easy"
  },
  {
    id: "add-anime",
    title: "Add to List",
    description: "Add an anime to your list",
    xpReward: 20,
    icon: "plus",
    difficulty: "easy"
  },
  {
    id: "rate-anime",
    title: "Rate an Anime",
    description: "Rate an anime in your list",
    xpReward: 25,
    icon: "star",
    difficulty: "medium"
  },
  {
    id: "update-progress",
    title: "Update Progress",
    description: "Update your progress on an anime",
    xpReward: 15,
    icon: "refresh",
    difficulty: "easy"
  },
  {
    id: "explore-genres",
    title: "Explore Genres",
    description: "Browse anime from a specific genre",
    xpReward: 20,
    icon: "layers",
    difficulty: "medium"
  },
  {
    id: "watch-seasonal",
    title: "Watch Seasonal",
    description: "Watch an episode from a currently airing anime",
    xpReward: 30,
    icon: "zap",
    difficulty: "medium"
  },
  {
    id: "complete-all",
    title: "Complete All Tasks",
    description: "Complete all other daily tasks",
    xpReward: 50,
    icon: "check-circle",
    difficulty: "hard",
    isBonus: true
  }
];

// Hook for using the context
export const useTaskSystem = () => useContext(TaskSystemContext);

// Provider component
export const TaskSystemProvider = ({ children }) => {
  const { user, isAuthenticated } = useAniList();
  const { addXp, fetchUserActivity } = useUserActivity();
  const [tasks, setTasks] = useState([]);
  const [completedTaskIds, setCompletedTaskIds] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastReset, setLastReset] = useState(null);

  // Initialize tasks when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user?.id) {
      loadTasks();
    } else {
      setTasks(TASK_DEFINITIONS.map(task => ({ ...task, isCompleted: false })));
      setCompletedTaskIds([]);
      setIsLoading(false);
    }
  }, [isAuthenticated, user]);

  // Load tasks from MongoDB
  const loadTasks = async () => {
    setIsLoading(true);
    try {
      // Try to get tasks from MongoDB
      const response = await profileApi.getDailyTasks(user.id);
      
      if (response && response.completedTasks) {
        console.log("Loaded tasks from MongoDB:", response);
        
        // Check if tasks need to be reset (new day)
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (!response.lastReset || new Date(response.lastReset) < today) {
          // Reset tasks for new day
          console.log("Resetting tasks for new day");
          await resetTasks();
        } else {
          // Use tasks from MongoDB
          setCompletedTaskIds(response.completedTasks);
          setLastReset(response.lastReset);
          
          // Update tasks with completion status
          updateTasksWithCompletionStatus(response.completedTasks);
        }
      } else {
        // No tasks found, initialize with empty completed tasks
        console.log("No tasks found in MongoDB, initializing empty");
        setCompletedTaskIds([]);
        updateTasksWithCompletionStatus([]);
        
        // Set last reset to today
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        setLastReset(today);
      }
    } catch (error) {
      console.error("Error loading tasks:", error);
      
      // Fallback to localStorage
      loadTasksFromLocalStorage();
    } finally {
      setIsLoading(false);
    }
  };

  // Load tasks from localStorage as fallback
  const loadTasksFromLocalStorage = () => {
    try {
      const storedCompletedTasks = localStorage.getItem(`new-completed-tasks-${user.id}`);
      const storedLastReset = localStorage.getItem(`new-tasks-last-reset-${user.id}`);
      
      if (storedCompletedTasks) {
        const parsedTasks = JSON.parse(storedCompletedTasks);
        setCompletedTaskIds(parsedTasks);
        updateTasksWithCompletionStatus(parsedTasks);
      } else {
        setCompletedTaskIds([]);
        updateTasksWithCompletionStatus([]);
      }
      
      if (storedLastReset) {
        setLastReset(new Date(parseInt(storedLastReset)));
      } else {
        // Set last reset to today
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        setLastReset(today);
      }
    } catch (error) {
      console.error("Error loading tasks from localStorage:", error);
      setCompletedTaskIds([]);
      updateTasksWithCompletionStatus([]);
    }
  };

  // Update tasks with completion status
  const updateTasksWithCompletionStatus = (completedIds) => {
    const updatedTasks = TASK_DEFINITIONS.map(task => ({
      ...task,
      isCompleted: completedIds.includes(task.id)
    }));
    
    setTasks(updatedTasks);
    
    // Auto-complete visit task if not already completed
    const visitTask = updatedTasks.find(t => t.id === "visit");
    if (visitTask && !visitTask.isCompleted && visitTask.autoComplete) {
      completeTask("visit");
    }
  };

  // Reset tasks for new day
  const resetTasks = async () => {
    try {
      // Set today as last reset
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      // Reset completed tasks
      setCompletedTaskIds([]);
      setLastReset(today);
      updateTasksWithCompletionStatus([]);
      
      // Save to MongoDB
      await profileApi.updateUserProfile(user.id, {
        tasks: {
          completedTasks: [],
          lastReset: today
        }
      });
      
      // Save to localStorage as backup
      localStorage.setItem(`new-completed-tasks-${user.id}`, JSON.stringify([]));
      localStorage.setItem(`new-tasks-last-reset-${user.id}`, today.getTime().toString());
      
      console.log("Tasks reset successfully");
    } catch (error) {
      console.error("Error resetting tasks:", error);
      
      // Save to localStorage as backup
      localStorage.setItem(`new-completed-tasks-${user.id}`, JSON.stringify([]));
      localStorage.setItem(`new-tasks-last-reset-${user.id}`, new Date().setHours(0, 0, 0, 0).toString());
    }
  };

  // Complete a task
  const completeTask = async (taskId) => {
    if (!isAuthenticated || !user?.id) return;
    
    // Find the task
    const task = tasks.find(t => t.id === taskId);
    if (!task) {
      console.error(`Task ${taskId} not found`);
      return;
    }
    
    // Check if task is already completed
    if (task.isCompleted || completedTaskIds.includes(taskId)) {
      console.log(`Task ${taskId} is already completed`);
      return;
    }
    
    // For non-auto-complete tasks, verify completion
    if (!task.autoComplete && taskId !== "complete-all") {
      const verified = await verifyTaskCompletion(taskId);
      if (!verified) {
        toast.error("Task Verification Failed", {
          description: "You need to complete the required action first",
          duration: 3000
        });
        return;
      }
    }
    
    // For complete-all task, verify all other tasks are completed
    if (taskId === "complete-all") {
      const regularTaskIds = TASK_DEFINITIONS
        .filter(t => !t.isBonus)
        .map(t => t.id);
      
      const allRegularTasksCompleted = regularTaskIds.every(id => 
        completedTaskIds.includes(id) || id === taskId
      );
      
      if (!allRegularTasksCompleted) {
        toast.error("Complete All Tasks Failed", {
          description: "You need to complete all other tasks first",
          duration: 3000
        });
        return;
      }
    }
    
    try {
      // Update UI immediately
      const newCompletedTaskIds = [...completedTaskIds, taskId];
      setCompletedTaskIds(newCompletedTaskIds);
      updateTasksWithCompletionStatus(newCompletedTaskIds);
      
      // Save to MongoDB
      await profileApi.completeTask(user.id, taskId);
      
      // Award XP
      await profileApi.addXp(user.id, task.xpReward, 'task_completion');
      
      // Save to localStorage as backup
      localStorage.setItem(`new-completed-tasks-${user.id}`, JSON.stringify(newCompletedTaskIds));
      
      // Show success toast
      toast.success(`Task Completed: ${task.title}`, {
        description: `You earned ${task.xpReward} XP!`,
        duration: 3000
      });
      
      // Refresh user profile data
      setTimeout(() => {
        fetchUserActivity(user.id);
      }, 1000);
      
      // Check if all regular tasks are completed
      if (taskId !== "complete-all") {
        const regularTaskIds = TASK_DEFINITIONS
          .filter(t => !t.isBonus)
          .map(t => t.id);
        
        const allRegularTasksCompleted = regularTaskIds.every(id => 
          newCompletedTaskIds.includes(id)
        );
        
        if (allRegularTasksCompleted && !newCompletedTaskIds.includes("complete-all")) {
          // Complete the bonus task automatically
          setTimeout(() => {
            completeTask("complete-all");
          }, 1000);
        }
      }
    } catch (error) {
      console.error("Error completing task:", error);
      
      // Revert UI changes
      updateTasksWithCompletionStatus(completedTaskIds);
      
      // Show error toast
      toast.error("Failed to complete task", {
        description: "Please try again later",
        duration: 3000
      });
    }
  };

  // Verify task completion
  const verifyTaskCompletion = async (taskId) => {
    // In development mode, always return true for easier testing
    if (process.env.NODE_ENV === 'development') {
      return true;
    }
    
    // In production, implement proper verification
    // This would check if the user has actually performed the required action
    // For now, we'll return true for simplicity
    return true;
  };

  // Get task completion progress
  const getTaskProgress = () => {
    if (tasks.length === 0) return 0;
    
    const regularTasks = tasks.filter(t => !t.isBonus);
    const completedRegularTasks = regularTasks.filter(t => t.isCompleted);
    
    return Math.round((completedRegularTasks.length / regularTasks.length) * 100);
  };

  // Get total XP available
  const getTotalAvailableXp = () => {
    return tasks.reduce((total, task) => total + task.xpReward, 0);
  };

  // Get earned XP
  const getEarnedXp = () => {
    return tasks
      .filter(task => task.isCompleted)
      .reduce((total, task) => total + task.xpReward, 0);
  };

  return (
    <TaskSystemContext.Provider
      value={{
        tasks,
        completedTaskIds,
        isLoading,
        completeTask,
        getTaskProgress,
        getTotalAvailableXp,
        getEarnedXp
      }}
    >
      {children}
    </TaskSystemContext.Provider>
  );
};
