import { useEffect, useRef, useState } from "react";

const SmoothCursor = () => {
  const cursorRef = useRef(null);
  const cursorDotRef = useRef(null);
  const [isPointer, setIsPointer] = useState(false);
  const [isHidden, setIsHidden] = useState(false);
  const [cursorPos, setCursorPos] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const cursor = cursorRef.current;
    const cursorDot = cursorDotRef.current;

    if (!cursor || !cursorDot) return;

    let mouseX = 0;
    let mouseY = 0;
    let cursorX = 0;
    let cursorY = 0;
    let dotX = 0;
    let dotY = 0;

    // Mouse move handler
    const handleMouseMove = (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
    };

    // Mouse enter/leave handlers
    const handleMouseEnter = () => setIsHidden(false);
    const handleMouseLeave = () => setIsHidden(true);

    // Check if element is interactive
    const handleMouseOver = (e) => {
      const target = e.target;
      const isInteractive =
        target.tagName === 'BUTTON' ||
        target.tagName === 'A' ||
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA' ||
        target.tagName === 'SELECT' ||
        target.type === 'button' ||
        target.type === 'submit' ||
        target.role === 'button' ||
        target.classList.contains('cursor-pointer') ||
        window.getComputedStyle(target).cursor === 'pointer';

      setIsPointer(isInteractive);
    };

    // Animation loop
    const animate = () => {
      // Smooth cursor movement with different speeds
      const speed = 0.15;
      const dotSpeed = 0.8;

      cursorX += (mouseX - cursorX) * speed;
      cursorY += (mouseY - cursorY) * speed;

      dotX += (mouseX - dotX) * dotSpeed;
      dotY += (mouseY - dotY) * dotSpeed;

      // Update cursor position state for trailing effect
      setCursorPos({ x: cursorX, y: cursorY });

      if (cursor) {
        cursor.style.transform = `translate3d(${cursorX - 24}px, ${cursorY - 24}px, 0)`;
      }

      if (cursorDot) {
        cursorDot.style.transform = `translate3d(${dotX - 6}px, ${dotY - 6}px, 0)`;
      }

      requestAnimationFrame(animate);
    };

    // Event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseenter', handleMouseEnter);
    document.addEventListener('mouseleave', handleMouseLeave);
    document.addEventListener('mouseover', handleMouseOver);

    // Start animation
    animate();

    // Cleanup
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseenter', handleMouseEnter);
      document.removeEventListener('mouseleave', handleMouseLeave);
      document.removeEventListener('mouseover', handleMouseOver);
    };
  }, []);

  // Hide on mobile devices
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768 || 'ontouchstart' in window);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  if (isMobile) return null;

  return (
    <>
      {/* Main cursor - anime style */}
      <div
        ref={cursorRef}
        className={`fixed top-0 left-0 w-12 h-12 pointer-events-none z-[9999] transition-all duration-300 ease-out ${
          isHidden ? 'opacity-0 scale-0' : 'opacity-100'
        } ${
          isPointer ? 'scale-125' : 'scale-100'
        }`}
      >
        {/* Outer ring with anime glow */}
        <div className={`w-full h-full rounded-full border-2 bg-transparent transition-all duration-300 ${
          isPointer
            ? 'border-white bg-white/10 shadow-[0_0_20px_rgba(255,255,255,0.5)]'
            : 'border-white/60 shadow-[0_0_10px_rgba(255,255,255,0.3)]'
        }`} />

        {/* Inner decorative ring */}
        <div className={`absolute inset-2 rounded-full border transition-all duration-300 ${
          isPointer ? 'border-white/40' : 'border-white/20'
        }`} />

        {/* Anime-style sparkle effects */}
        <div className={`absolute -top-1 -right-1 w-2 h-2 bg-white rounded-full transition-all duration-300 ${
          isPointer ? 'opacity-100 scale-100' : 'opacity-60 scale-75'
        }`} />
        <div className={`absolute -bottom-1 -left-1 w-1 h-1 bg-white rounded-full transition-all duration-300 ${
          isPointer ? 'opacity-80 scale-100' : 'opacity-40 scale-75'
        }`} />
      </div>

      {/* Cursor dot - anime center */}
      <div
        ref={cursorDotRef}
        className={`fixed top-0 left-0 w-3 h-3 pointer-events-none z-[9999] transition-all duration-150 ease-out ${
          isHidden ? 'opacity-0 scale-0' : 'opacity-100'
        } ${
          isPointer ? 'scale-150' : 'scale-100'
        }`}
      >
        <div className={`w-full h-full rounded-full transition-all duration-150 ${
          isPointer
            ? 'bg-white shadow-[0_0_15px_rgba(255,255,255,0.8)]'
            : 'bg-white/80 shadow-[0_0_8px_rgba(255,255,255,0.4)]'
        }`} />
      </div>

      {/* Trailing particles for anime effect */}
      <div
        className={`fixed top-0 left-0 pointer-events-none z-[9998] transition-all duration-500 ${
          isHidden ? 'opacity-0' : 'opacity-100'
        }`}
        style={{
          transform: `translate3d(${cursorPos.x - 6}px, ${cursorPos.y - 6}px, 0)`,
        }}
      >
        <div className="w-3 h-3 bg-white/20 rounded-full animate-pulse" />
      </div>

      {/* Hide default cursor */}
      <style jsx global>{`
        * {
          cursor: none !important;
        }

        a, button, input, textarea, select, [role="button"], .cursor-pointer {
          cursor: none !important;
        }
      `}</style>
    </>
  );
};

export default SmoothCursor;
