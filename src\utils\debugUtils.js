/**
 * Debug utilities for task verification
 */

/**
 * Check the status of all task verification flags for a user
 * @param {string} userId - The user's AniList ID
 * @returns {Object} - Object containing the status of all task verification flags
 */
export const checkTaskVerificationStatus = (userId) => {
  try {
    // Get all task verification flags
    const watchedEpisode = localStorage.getItem(`watched-episode-${userId}`);
    const watchedSeasonal = localStorage.getItem(`watched-seasonal-${userId}`);
    const addedAnime = localStorage.getItem(`added-anime-${userId}`);
    const ratedAnime = localStorage.getItem(`rated-anime-${userId}`);
    const updatedProgress = localStorage.getItem(`updated-progress-${userId}`);
    const exploredGenres = localStorage.getItem(`explored-genres-${userId}`);
    const exploredGenre = localStorage.getItem(`explored-genre-${userId}`);
    
    // Get completed tasks
    const completedTasksJson = localStorage.getItem(`completed-tasks-${userId}`);
    const completedTasks = completedTasksJson ? JSON.parse(completedTasksJson) : [];
    
    // Get watch history
    const watchHistoryJson = localStorage.getItem(`watch-progress`);
    const watchHistory = watchHistoryJson ? JSON.parse(watchHistoryJson) : {};
    
    // Format timestamps for better readability
    const formatTimestamp = (timestamp) => {
      if (!timestamp) return 'Not set';
      
      const date = new Date(parseInt(timestamp));
      return `${date.toLocaleDateString()} ${date.toLocaleTimeString()} (${Date.now() - parseInt(timestamp)}ms ago)`;
    };
    
    // Check if each flag is set and if it's within the last 24 hours
    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    
    return {
      userId,
      watchedEpisode: {
        set: !!watchedEpisode,
        timestamp: formatTimestamp(watchedEpisode),
        withinLastDay: watchedEpisode ? parseInt(watchedEpisode) > oneDayAgo : false
      },
      watchedSeasonal: {
        set: !!watchedSeasonal,
        timestamp: formatTimestamp(watchedSeasonal),
        withinLastDay: watchedSeasonal ? parseInt(watchedSeasonal) > oneDayAgo : false
      },
      addedAnime: {
        set: !!addedAnime,
        timestamp: formatTimestamp(addedAnime),
        withinLastDay: addedAnime ? parseInt(addedAnime) > oneDayAgo : false
      },
      ratedAnime: {
        set: !!ratedAnime,
        timestamp: formatTimestamp(ratedAnime),
        withinLastDay: ratedAnime ? parseInt(ratedAnime) > oneDayAgo : false
      },
      updatedProgress: {
        set: !!updatedProgress,
        timestamp: formatTimestamp(updatedProgress),
        withinLastDay: updatedProgress ? parseInt(updatedProgress) > oneDayAgo : false
      },
      exploredGenres: {
        set: !!exploredGenres,
        timestamp: formatTimestamp(exploredGenres),
        withinLastDay: exploredGenres ? parseInt(exploredGenres) > oneDayAgo : false,
        genre: exploredGenre || 'None'
      },
      completedTasks,
      watchHistoryEntries: Object.keys(watchHistory).length
    };
  } catch (error) {
    console.error('Error checking task verification status:', error);
    return {
      error: error.message
    };
  }
};

/**
 * Reset all task verification flags for a user
 * @param {string} userId - The user's AniList ID
 */
export const resetTaskVerificationFlags = (userId) => {
  try {
    localStorage.removeItem(`watched-episode-${userId}`);
    localStorage.removeItem(`watched-seasonal-${userId}`);
    localStorage.removeItem(`added-anime-${userId}`);
    localStorage.removeItem(`rated-anime-${userId}`);
    localStorage.removeItem(`updated-progress-${userId}`);
    localStorage.removeItem(`explored-genres-${userId}`);
    localStorage.removeItem(`explored-genre-${userId}`);
    
    return {
      success: true,
      message: 'All task verification flags have been reset'
    };
  } catch (error) {
    console.error('Error resetting task verification flags:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Set a specific task verification flag for testing
 * @param {string} userId - The user's AniList ID
 * @param {string} flagName - The name of the flag to set (e.g., 'watchedEpisode')
 */
export const setTaskVerificationFlag = (userId, flagName) => {
  try {
    const flagMap = {
      watchedEpisode: `watched-episode-${userId}`,
      watchedSeasonal: `watched-seasonal-${userId}`,
      addedAnime: `added-anime-${userId}`,
      ratedAnime: `rated-anime-${userId}`,
      updatedProgress: `updated-progress-${userId}`,
      exploredGenres: `explored-genres-${userId}`
    };
    
    const key = flagMap[flagName];
    if (!key) {
      return {
        success: false,
        error: `Unknown flag name: ${flagName}`
      };
    }
    
    localStorage.setItem(key, Date.now().toString());
    
    if (flagName === 'exploredGenres') {
      localStorage.setItem(`explored-genre-${userId}`, 'Action');
    }
    
    return {
      success: true,
      message: `Flag ${flagName} has been set for user ${userId}`
    };
  } catch (error) {
    console.error(`Error setting task verification flag ${flagName}:`, error);
    return {
      success: false,
      error: error.message
    };
  }
};
