import { createContext, useContext, useState, useEffect } from "react";

// Create the context
const WatchHistoryContext = createContext();

// Custom hook to use the context
export const useWatchHistory = () => {
  return useContext(WatchHistoryContext);
};

// Provider component
export const WatchHistoryProvider = ({ children }) => {
  const [watchHistory, setWatchHistory] = useState({});

  // Load watch history from localStorage on mount
  useEffect(() => {
    console.log('🚀 WatchHistoryContext initializing...');

    try {
      // Check if localStorage is available
      if (typeof window !== 'undefined' && window.localStorage) {
        console.log('🚀 localStorage is available');

        // Try to write a test value
        localStorage.setItem('watch-history-test', 'test');
        const testValue = localStorage.getItem('watch-history-test');
        console.log('🚀 localStorage test:', testValue);
        localStorage.removeItem('watch-history-test');

        // Use the same key as the existing system for compatibility
        const storedHistory = localStorage.getItem("watch-progress");
        console.log('🚀 Raw stored history:', storedHistory);

        if (storedHistory) {
          try {
            const parsedHistory = JSON.parse(storedHistory);
            console.log('🚀 Loaded watch history:', parsedHistory);

            // Validate the parsed data
            if (parsedHistory && typeof parsedHistory === 'object') {
              console.log('🚀 Watch history is valid, setting state');
              setWatchHistory(parsedHistory);

              // Log the keys to help debug
              const animeIds = Object.keys(parsedHistory);
              console.log('🚀 Anime IDs in history:', animeIds);

              // Log episode data for each anime
              animeIds.forEach(animeId => {
                const episodes = Object.keys(parsedHistory[animeId] || {});
                console.log(`🚀 Episodes for anime ${animeId}:`, episodes);

                episodes.forEach(episode => {
                  const progress = parsedHistory[animeId][episode]?.progress || 0;
                  console.log(`🚀 Progress for anime ${animeId}, episode ${episode}: ${progress}%`);
                });
              });
            } else {
              console.warn('🚀 Parsed history is not a valid object:', parsedHistory);
              setWatchHistory({});
            }
          } catch (parseError) {
            console.error("🚀 Error parsing watch history:", parseError);
            console.log('🚀 Invalid JSON in localStorage, resetting');
            localStorage.removeItem("watch-progress");
            setWatchHistory({});
          }
        } else {
          console.log('🚀 No watch history found in localStorage');
          setWatchHistory({});

          // Initialize with empty object
          localStorage.setItem("watch-progress", JSON.stringify({}));
        }
      } else {
        console.warn('🚀 localStorage is not available');
        setWatchHistory({});
      }
    } catch (error) {
      console.error("🚀 Error loading watch history:", error);
      // If there's an error, reset the history
      try {
        localStorage.removeItem("watch-progress");
      } catch (e) {
        console.error("🚀 Could not remove item from localStorage:", e);
      }
      setWatchHistory({});
    }

    console.log('🚀 WatchHistoryContext initialization complete');
  }, []);

  // Save watch history to localStorage whenever it changes
  useEffect(() => {
    try {
      // Use the same key as the existing system for compatibility
      console.log('Saving watch history to localStorage:', watchHistory);
      localStorage.setItem("watch-progress", JSON.stringify(watchHistory));
    } catch (error) {
      console.error("Error saving watch history:", error);
    }
  }, [watchHistory]);

  // Update progress for an episode
  const updateProgress = (animeId, episodeNumber, progress) => {
    console.log(`🔄 Updating progress for anime ${animeId}, episode ${episodeNumber}: ${progress}%`);

    // Convert parameters to ensure consistent types
    const animeIdStr = String(animeId);
    const episodeNumberStr = String(episodeNumber);

    console.log(`🔄 Current watch history before update:`, watchHistory);

    setWatchHistory(prev => {
      const animeHistory = prev[animeIdStr] || {};
      const newHistory = {
        ...prev,
        [animeIdStr]: {
          ...animeHistory,
          [episodeNumberStr]: {
            progress,
            lastWatched: Date.now(),
          }
        }
      };

      console.log('🔄 Updated watch history:', newHistory);

      // Force save to localStorage immediately for debugging
      try {
        localStorage.setItem("watch-progress", JSON.stringify(newHistory));
        console.log('🔄 Directly saved to localStorage');

        // Verify it was saved
        const saved = localStorage.getItem("watch-progress");
        console.log('🔄 Verification - Read from localStorage:', saved);
      } catch (error) {
        console.error('🔄 Error directly saving to localStorage:', error);
      }

      return newHistory;
    });
  };

  // Get progress for an episode
  const getProgress = (animeId, episodeNumber) => {
    // Convert parameters to ensure consistent types
    const animeIdStr = String(animeId);
    const episodeNumberStr = String(episodeNumber);

    console.log(`📊 Getting progress for anime ${animeIdStr}, episode ${episodeNumberStr}`);
    console.log('📊 Current watch history from state:', watchHistory);

    // Also check localStorage directly
    try {
      const savedData = localStorage.getItem("watch-progress");
      console.log('📊 Raw localStorage data:', savedData);

      if (savedData) {
        const parsedData = JSON.parse(savedData);
        console.log('📊 Parsed localStorage data:', parsedData);

        // Check if the data exists in localStorage but not in state
        if (parsedData[animeIdStr] &&
            parsedData[animeIdStr][episodeNumberStr] &&
            (!watchHistory[animeIdStr] || !watchHistory[animeIdStr][episodeNumberStr])) {
          console.log('📊 Found data in localStorage but not in state!');

          // Update state with localStorage data
          setWatchHistory(parsedData);

          // Return the progress from localStorage
          const progress = parsedData[animeIdStr][episodeNumberStr].progress || 0;
          console.log(`📊 Found progress in localStorage: ${progress}%`);
          return progress;
        }
      }
    } catch (error) {
      console.error('📊 Error reading from localStorage:', error);
    }

    if (!watchHistory[animeIdStr] || !watchHistory[animeIdStr][episodeNumberStr]) {
      console.log(`📊 No progress found for anime ${animeIdStr}, episode ${episodeNumberStr}`);
      return 0;
    }

    const progress = watchHistory[animeIdStr][episodeNumberStr].progress || 0;
    console.log(`📊 Found progress in state: ${progress}%`);
    return progress;
  };

  // Check if an episode is considered watched (progress > 90%)
  const isWatched = (animeId, episodeNumber) => {
    // Convert parameters to ensure consistent types
    const animeIdStr = String(animeId);
    const episodeNumberStr = String(episodeNumber);

    const progress = getProgress(animeIdStr, episodeNumberStr);
    const watched = progress > 90;
    console.log(`Episode ${episodeNumberStr} of anime ${animeIdStr} is ${watched ? 'watched' : 'not watched'} (${progress}%)`);
    return watched;
  };

  // Get all watched episodes for an anime
  const getWatchedEpisodes = (animeId) => {
    // Convert parameter to ensure consistent type
    const animeIdStr = String(animeId);

    if (!watchHistory[animeIdStr]) {
      console.log(`No watch history found for anime ${animeIdStr}`);
      return [];
    }

    const watchedEpisodes = Object.entries(watchHistory[animeIdStr])
      .filter(([_, data]) => data.progress > 90)
      .map(([episodeNumber]) => Number(episodeNumber));

    console.log(`Found ${watchedEpisodes.length} watched episodes for anime ${animeIdStr}:`, watchedEpisodes);
    return watchedEpisodes;
  };

  // Clear history for a specific anime
  const clearAnimeHistory = (animeId) => {
    // Convert parameter to ensure consistent type
    const animeIdStr = String(animeId);

    console.log(`Clearing watch history for anime ${animeIdStr}`);

    setWatchHistory(prev => {
      const newHistory = { ...prev };
      delete newHistory[animeIdStr];
      console.log('Updated watch history after clearing:', newHistory);
      return newHistory;
    });
  };

  // Clear all watch history
  const clearAllHistory = () => {
    console.log('Clearing all watch history');
    setWatchHistory({});
    localStorage.removeItem("watch-progress");
  };

  const value = {
    updateProgress,
    getProgress,
    isWatched,
    getWatchedEpisodes,
    clearAnimeHistory,
    clearAllHistory,
    watchHistory
  };

  return (
    <WatchHistoryContext.Provider value={value}>
      {children}
    </WatchHistoryContext.Provider>
  );
};

export default WatchHistoryContext;
