@import url("https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import './styles/vidstack.css';
@tailwind base;
@tailwind components;
@tailwind utilities;
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}
* {
  box-sizing: border-box;
  scroll-behavior: smooth;
  margin: 0;
  outline: none !important;
  border: none !important;
  scrollbar-width: none !important;
  letter-spacing: 0.025em;
  text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}
a,
button {
  outline: none !important;
  text-decoration: none;
  transition: all 0.3s ease;
  text-align: unset;
}
a,
button,
img {
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0) !important;
  -webkit-tap-highlight-color: transparent !important;
}

button,
img {
  user-select: none !important;
  -webkit-user-select: none !important;
  -khtml-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

input,
img {
  outline: none !important;
  border: none !important;
}
img {
  z-index: 0;
}

html,
body {
  width: 100%;
  background-color: black;
  font-family: Jost, "Open Sans", sans-serif !important;
  color: aliceblue;
  scroll-behavior: smooth !important;
}

::-webkit-scrollbar {
  display: none !important;
}

/* Custom scrollbar for comment section */
.custom-scrollbar::-webkit-scrollbar {
  display: block !important;
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

::selection {
  background-color: white;
  color: black;
}
.smooth {
  transition: all 0.3s ease;
}
.hero-gradient {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 40%, #000000 100%),
    linear-gradient(270deg, rgba(253, 253, 253, 0) 40%, rgba(0, 0, 0, 0.33) 70%);
}

.pp:active {
  transform: scale(0.98);
}

/* Image quality enhancements */
.image-rendering-high {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transform: translateZ(0);
  filter: brightness(1.03) contrast(1.05);
  transition: all 0.3s ease;
}

/* Text shadow for better readability on image backgrounds */
.text-shadow {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8), 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Custom cursor styles */
* {
  cursor: url('https://cdn.jsdelivr.net/gh/elite-col/files@main/custom%20cursor.ani'), auto !important;
}

/* Pointer cursor for interactive elements */
a, button, input, textarea, select, [role="button"], .cursor-pointer,
button:hover, a:hover, input:hover, textarea:hover, select:hover {
  cursor: url('https://cdn.jsdelivr.net/gh/elite-col/files@main/pointer.cur'), pointer !important;
}

/* Custom animations for loader */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}

/* Hide scrollbar for pagination containers */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* YouTube video responsive container */
.youtube-container {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  background-color: black; /* Ensure black background for any gaps */
}

/* YouTube iframe responsive styling */
.youtube-iframe {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Large desktop screens - ensure video fills the container */
@media (min-width: 1281px) {
  .youtube-iframe {
    width: 100%;
    height: 177.78%; /* 16:9 aspect ratio (9/16 = 0.5625, 1/0.5625 = 1.7778) */
    min-width: 177.78vh; /* Ensure the video is wide enough */
  }
}

/* Desktop and laptop screens */
@media (min-width: 769px) and (max-width: 1280px) {
  .youtube-iframe {
    width: 100%;
    height: 177.78%; /* 16:9 aspect ratio */
    min-width: 177.78vh;
  }
}

/* Tablets and smaller laptops */
@media (min-width: 641px) and (max-width: 768px) {
  .youtube-iframe {
    width: 180%; /* Slight zoom out for tablets */
    height: 100%;
  }
}

/* Medium phones */
@media (min-width: 481px) and (max-width: 640px) {
  .youtube-iframe {
    width: 250%; /* Less zoom out for medium screens */
    height: 100%;
  }
}

/* Small phones */
@media (min-width: 361px) and (max-width: 480px) {
  .youtube-iframe {
    width: 300%; /* More zoom out for small screens */
    height: 100%;
  }
}

/* Very small phones */
@media (max-width: 360px) {
  .youtube-iframe {
    width: 350%; /* Extra zoom out for very small screens */
    height: 100%;
  }
}