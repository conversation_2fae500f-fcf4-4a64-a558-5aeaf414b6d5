/**
 * Task verification utilities
 * These functions verify that users have actually completed the required actions
 * before allowing them to complete tasks and earn XP
 */

/**
 * Verify that the user has visited the site today
 * This is automatically verified by the user's presence on the site
 */
export const verifyVisitTask = async (userId) => {
  // This task is automatically verified by the user's presence on the site
  return { success: true };
};

/**
 * Verify that the user has watched an episode recently
 * @param {string} userId - The user's AniList ID
 * @param {Array<string>} completedTasks - Array of completed task IDs
 * @returns {Promise<{success: boolean, message?: string}>}
 */
export const verifyWatchEpisodeTask = async (userId, completedTasks = []) => {
  try {
    // First, check if this task is already in the completedTasks array
    // This ensures persistence across page reloads
    if (completedTasks.includes("watch-episode")) {
      console.log("Watch episode task already completed according to completedTasks array");
      return { success: true };
    }

    // Check for the watched-episode flag that we set in trackWatchEpisode
    const watchedEpisodeKey = `watched-episode-${userId}`;
    const watchedEpisodeTimestamp = localStorage.getItem(watchedEpisodeKey);

    if (!watchedEpisodeTimestamp) {
      // Also check the watch history as a fallback
      const watchHistoryKey = `watch-progress`;
      const watchHistoryJson = localStorage.getItem(watchHistoryKey);

      if (!watchHistoryJson) {
        // For development/testing, allow completing this task without verification
        if (process.env.NODE_ENV === 'development') {
          console.log("DEV MODE: Allowing watch episode task completion without verification");
          localStorage.setItem(watchedEpisodeKey, Date.now().toString());
          return { success: true };
        }

        return {
          success: false,
          message: "You need to watch an episode first"
        };
      }

      try {
        const watchHistory = JSON.parse(watchHistoryJson);

        // Check if there's any anime with progress > 10%
        const hasWatchedAnime = Object.values(watchHistory).some(animeData => {
          return Object.values(animeData).some(episodeData => {
            return episodeData.progress && episodeData.progress > 10;
          });
        });

        if (hasWatchedAnime) {
          // If we found watch history, set the flag for future checks
          localStorage.setItem(watchedEpisodeKey, Date.now().toString());
          return { success: true };
        }
      } catch (parseError) {
        console.error("Error parsing watch history:", parseError);
      }

      // For development/testing, allow completing this task without verification
      if (process.env.NODE_ENV === 'development') {
        console.log("DEV MODE: Allowing watch episode task completion without verification");
        localStorage.setItem(watchedEpisodeKey, Date.now().toString());
        return { success: true };
      }

      return {
        success: false,
        message: "You need to watch an episode first"
      };
    }

    // Check if the timestamp is within the last 24 hours
    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    if (parseInt(watchedEpisodeTimestamp) < oneDayAgo) {
      // For development/testing, allow completing this task without time verification
      if (process.env.NODE_ENV === 'development') {
        console.log("DEV MODE: Allowing watch episode task completion without time verification");
        localStorage.setItem(watchedEpisodeKey, Date.now().toString());
        return { success: true };
      }

      return {
        success: false,
        message: "You need to watch an episode in the last 24 hours"
      };
    }

    return { success: true };
  } catch (error) {
    console.error("Error verifying watch episode task:", error);

    // For development/testing, allow completing this task even if there's an error
    if (process.env.NODE_ENV === 'development') {
      console.log("DEV MODE: Allowing watch episode task completion despite error");
      return { success: true };
    }

    return {
      success: false,
      message: "Error verifying task completion"
    };
  }
};

/**
 * Verify that the user has added an anime to their list recently
 * @param {string} userId - The user's AniList ID
 * @param {Array<string>} completedTasks - Array of completed task IDs
 * @returns {Promise<{success: boolean, message?: string}>}
 */
export const verifyAddAnimeTask = async (userId, completedTasks = []) => {
  // First, check if this task is already in the completedTasks array
  if (completedTasks.includes("add-anime")) {
    console.log("Add anime task already completed according to completedTasks array");
    return { success: true };
  }
  try {
    // Get the user's AniList token
    const anilistStorage = localStorage.getItem('anilist');
    if (!anilistStorage) {
      return {
        success: false,
        message: "You need to be logged in to AniList"
      };
    }

    const anilistData = JSON.parse(anilistStorage);
    if (!anilistData.access_token) {
      return {
        success: false,
        message: "You need to be logged in to AniList"
      };
    }

    // Check if user has added an anime to their list in the last 24 hours
    // This would ideally use the AniList API to check for recent list additions
    // For now, we'll use a localStorage flag as a simple implementation

    const addedAnimeKey = `added-anime-${userId}`;
    const addedAnimeTimestamp = localStorage.getItem(addedAnimeKey);

    if (!addedAnimeTimestamp) {
      return {
        success: false,
        message: "You need to add an anime to your list first"
      };
    }

    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    if (parseInt(addedAnimeTimestamp) < oneDayAgo) {
      return {
        success: false,
        message: "You need to add an anime to your list in the last 24 hours"
      };
    }

    return { success: true };
  } catch (error) {
    console.error("Error verifying add anime task:", error);
    return {
      success: false,
      message: "Error verifying task completion"
    };
  }
};

/**
 * Verify that the user has rated an anime recently
 * @param {string} userId - The user's AniList ID
 * @param {Array<string>} completedTasks - Array of completed task IDs
 * @returns {Promise<{success: boolean, message?: string}>}
 */
export const verifyRateAnimeTask = async (userId, completedTasks = []) => {
  // First, check if this task is already in the completedTasks array
  if (completedTasks.includes("rate-anime")) {
    console.log("Rate anime task already completed according to completedTasks array");
    return { success: true };
  }
  try {
    // Similar to addAnime, this would ideally use the AniList API
    // For now, we'll use a localStorage flag

    const ratedAnimeKey = `rated-anime-${userId}`;
    const ratedAnimeTimestamp = localStorage.getItem(ratedAnimeKey);

    if (!ratedAnimeTimestamp) {
      return {
        success: false,
        message: "You need to rate an anime first"
      };
    }

    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    if (parseInt(ratedAnimeTimestamp) < oneDayAgo) {
      return {
        success: false,
        message: "You need to rate an anime in the last 24 hours"
      };
    }

    return { success: true };
  } catch (error) {
    console.error("Error verifying rate anime task:", error);
    return {
      success: false,
      message: "Error verifying task completion"
    };
  }
};

/**
 * Verify that the user has updated their progress on an anime recently
 * @param {string} userId - The user's AniList ID
 * @param {Array<string>} completedTasks - Array of completed task IDs
 * @returns {Promise<{success: boolean, message?: string}>}
 */
export const verifyUpdateProgressTask = async (userId, completedTasks = []) => {
  // First, check if this task is already in the completedTasks array
  if (completedTasks.includes("update-progress")) {
    console.log("Update progress task already completed according to completedTasks array");
    return { success: true };
  }
  try {
    // Similar to above, this would ideally use the AniList API
    // For now, we'll use a localStorage flag

    const updatedProgressKey = `updated-progress-${userId}`;
    const updatedProgressTimestamp = localStorage.getItem(updatedProgressKey);

    if (!updatedProgressTimestamp) {
      return {
        success: false,
        message: "You need to update your progress on an anime first"
      };
    }

    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    if (parseInt(updatedProgressTimestamp) < oneDayAgo) {
      return {
        success: false,
        message: "You need to update your progress in the last 24 hours"
      };
    }

    return { success: true };
  } catch (error) {
    console.error("Error verifying update progress task:", error);
    return {
      success: false,
      message: "Error verifying task completion"
    };
  }
};

/**
 * Verify that the user has explored genres recently
 * @param {string} userId - The user's AniList ID
 * @param {Array<string>} completedTasks - Array of completed task IDs
 * @returns {Promise<{success: boolean, message?: string}>}
 */
export const verifyExploreGenresTask = async (userId, completedTasks = []) => {
  // First, check if this task is already in the completedTasks array
  if (completedTasks.includes("explore-genres")) {
    console.log("Explore genres task already completed according to completedTasks array");
    return { success: true };
  }
  try {
    // Check if user has visited the explore page with a genre filter
    const exploredGenresKey = `explored-genres-${userId}`;
    const exploredGenresTimestamp = localStorage.getItem(exploredGenresKey);

    if (!exploredGenresTimestamp) {
      return {
        success: false,
        message: "You need to explore anime by genre first"
      };
    }

    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    if (parseInt(exploredGenresTimestamp) < oneDayAgo) {
      return {
        success: false,
        message: "You need to explore anime by genre in the last 24 hours"
      };
    }

    return { success: true };
  } catch (error) {
    console.error("Error verifying explore genres task:", error);
    return {
      success: false,
      message: "Error verifying task completion"
    };
  }
};

/**
 * Verify that the user has watched a seasonal anime recently
 * @param {string} userId - The user's AniList ID
 * @param {Array<string>} completedTasks - Array of completed task IDs
 * @returns {Promise<{success: boolean, message?: string}>}
 */
export const verifyWatchSeasonalTask = async (userId, completedTasks = []) => {
  // First, check if this task is already in the completedTasks array
  if (completedTasks.includes("watch-seasonal")) {
    console.log("Watch seasonal task already completed according to completedTasks array");
    return { success: true };
  }
  try {
    // This would ideally check if the user has watched a currently airing anime
    // For now, we'll use a localStorage flag

    const watchedSeasonalKey = `watched-seasonal-${userId}`;
    const watchedSeasonalTimestamp = localStorage.getItem(watchedSeasonalKey);

    if (!watchedSeasonalTimestamp) {
      return {
        success: false,
        message: "You need to watch a currently airing anime first"
      };
    }

    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    if (parseInt(watchedSeasonalTimestamp) < oneDayAgo) {
      return {
        success: false,
        message: "You need to watch a currently airing anime in the last 24 hours"
      };
    }

    return { success: true };
  } catch (error) {
    console.error("Error verifying watch seasonal task:", error);
    return {
      success: false,
      message: "Error verifying task completion"
    };
  }
};

/**
 * Verify that the user has completed all other tasks
 * @param {string} userId - The user's AniList ID
 * @param {Array<string>} completedTasks - Array of completed task IDs
 * @returns {Promise<{success: boolean, message?: string}>}
 */
export const verifyCompleteAllTask = async (userId, completedTasks) => {
  // Check if all regular tasks are completed
  const requiredTasks = [
    "visit",
    "watch-episode",
    "add-anime",
    "rate-anime",
    "update-progress",
    "explore-genres",
    "watch-seasonal"
  ];

  const allTasksCompleted = requiredTasks.every(taskId =>
    completedTasks.includes(taskId)
  );

  if (!allTasksCompleted) {
    return {
      success: false,
      message: "You need to complete all other tasks first"
    };
  }

  return { success: true };
};

/**
 * Check if a task is locked (already completed today)
 * @param {string} userId - The user's AniList ID
 * @param {string} taskId - The task ID
 * @returns {boolean} - Whether the task is locked
 */
export const isTaskLocked = (userId, taskId) => {
  if (!userId || !taskId) return false;

  // Check if there's a completion record for today
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const dateKey = today.toISOString().split('T')[0];

  // First check the task-completion record which has date information
  const completionKey = `task-completion-${userId}-${taskId}`;
  const completionRecord = localStorage.getItem(completionKey);

  if (completionRecord) {
    try {
      const record = JSON.parse(completionRecord);
      // If the record is from today, the task is locked
      if (record.dateKey === dateKey) {
        console.log(`Task ${taskId} is locked based on completion record`);
        return true;
      }
    } catch (error) {
      console.error(`Error parsing completion record for task ${taskId}:`, error);
    }
  }

  // Also check the task-completed flag
  // Note: We need to create a new Date object since setHours modifies the original
  const todayTimestamp = new Date(today).setHours(0, 0, 0, 0);
  const taskCompletedKey = `task-completed-${userId}-${taskId}-${todayTimestamp}`;
  const isCompleted = localStorage.getItem(taskCompletedKey) === 'true';

  if (isCompleted) {
    console.log(`Task ${taskId} is locked based on completion flag`);
    return true;
  }

  return false;
};

/**
 * Get the verification function for a specific task
 * @param {string} taskId - The task ID
 * @returns {Function} - The verification function for the task
 */
export const getVerificationFunction = (taskId) => {
  switch (taskId) {
    case "visit":
      return verifyVisitTask;
    case "watch-episode":
      return verifyWatchEpisodeTask;
    case "add-anime":
      return verifyAddAnimeTask;
    case "rate-anime":
      return verifyRateAnimeTask;
    case "update-progress":
      return verifyUpdateProgressTask;
    case "explore-genres":
      return verifyExploreGenresTask;
    case "watch-seasonal":
      return verifyWatchSeasonalTask;
    case "complete-all":
      return verifyCompleteAllTask;
    default:
      // Default to always succeeding for unknown tasks
      return async () => ({ success: true });
  }
};
