import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Zap,
  Rocket,
  Code,
  Search,
  Calendar,
  Star,
  X,
  Filter,
  ChevronDown,
  ChevronUp,
  ArrowLeft
} from "lucide-react";
import { Link } from "react-router-dom";

// Version history with changes
const changelogData = [
  {
    version: "1.5.0",
    date: "June 5, 2024",
    image: "https://64.media.tumblr.com/2abd888ef3c875f3b0bfe300c9bb46ff/c4f891fbbe1c2202-7d/s1280x1920/fddf1a33ab48ee901bce34b1767c610066b011b6.jpg", // Juju<PERSON> Kaisen - Gojo
    banner: "https://i.pinimg.com/originals/63/ed/0b/63ed0b6d8961b9544c2fd0c27d5ec081.gif", // <PERSON><PERSON><PERSON> banner
    highlight: "UI Redesign & Enhanced Mobile Experience",
    summary: "This update brings a refreshed UI with glassy elements, redesigned Schedule page, improved mobile navigation, and enhanced footer design.",
    categories: ["UI/UX", "Mobile", "Design"],
    changes: [
      {
        title: "Schedule Page Redesign",
        description: "Completely redesigned Schedule page with a modern list view, enhanced filtering, and a new hero section",
        type: "feature",
        icon: <Sparkles size={18} />
      },
      {
        title: "Glassy Mobile Navigation",
        description: "Redesigned mobile navigation with a floating glassy menu and improved accessibility",
        type: "improvement",
        icon: <Zap size={18} />
      },
      {
        title: "Enhanced Digital Clock",
        description: "Redesigned the LiveClock component with a modern digital appearance and improved readability",
        type: "improvement",
        icon: <Zap size={18} />
      },
      {
        title: "Glassy Footer Design",
        description: "Updated footer with a modern glass effect and subtle anime banner background",
        type: "improvement",
        icon: <Zap size={18} />
      },
      {
        title: "White UI Elements",
        description: "Changed UI accent colors from yellow to white for a cleaner, more consistent appearance",
        type: "improvement",
        icon: <Zap size={18} />
      }
    ]
  },
  {
    version: "1.4.0",
    date: "May 20, 2024",
    image: "https://images-wixmp-ed30a86b8c4ca887773594c2.wixmp.com/f/daac09b8-220f-4b3b-b482-cc25aaaa5f5d/dhmy9xh-9b3e9b7a-ecb4-4230-8a4b-4f4c2499749c.jpg/v1/fill/w_894,h_894,q_70,strp/violet_evergarden_icon_pfp_by_kronensegler123_dhmy9xh-pre.jpg?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ1cm46YXBwOjdlMGQxODg5ODIyNjQzNzNhNWYwZDQxNWVhMGQyNmUwIiwiaXNzIjoidXJuOmFwcDo3ZTBkMTg4OTgyMjY0MzczYTVmMGQ0MTVlYTBkMjZlMCIsIm9iaiI6W1t7ImhlaWdodCI6Ijw9MTI4MCIsInBhdGgiOiJcL2ZcL2RhYWMwOWI4LTIyMGYtNGIzYi1iNDgyLWNjMjVhYWFhNWY1ZFwvZGhteTl4aC05YjNlOWI3YS1lY2I0LTQyMzAtOGE0Yi00ZjRjMjQ5OTc0OWMuanBnIiwid2lkdGgiOiI8PTEyODAifV1dLCJhdWQiOlsidXJuOnNlcnZpY2U6aW1hZ2Uub3BlcmF0aW9ucyJdfQ.MBRLHkNTdzVbrNCwgOGVEU0OzDQPw-Wsgd86m5q3gvU", // Violet Evergarden
    banner: "https://i.pinimg.com/originals/1b/f0/73/1bf0732e21c80cf25bc7750a192cde91.gif", // Violet Evergarden banner
    highlight: "AniList Integration & UI Improvements",
    summary: "This update enhances the user experience with AniList integration for the hero slider and improves the positioning of anime posters.",
    categories: ["UI/UX", "Integration", "Feature"],
    changes: [
      {
        title: "AniList Integration in Hero",
        description: "The Add to List button in the hero slider now syncs with AniList instead of using local storage",
        type: "feature",
        icon: <Sparkles size={18} />
      },
      {
        title: "Improved Poster Positioning",
        description: "Adjusted anime poster positioning in the hero slider for better visual balance",
        type: "improvement",
        icon: <Zap size={18} />
      },
      {
        title: "Consistent Button Styling",
        description: "Made AniList sync button size match with the Watch Now button for consistent UI",
        type: "improvement",
        icon: <Zap size={18} />
      }
    ]
  },
  {
    version: "1.3.0",
    date: "July 10, 2023",
    image: "https://i.pinimg.com/originals/a7/7f/7d/a77f7d36556c2a22e219c2c1ece8e0a3.jpg", // Demon Slayer - Tanjiro
    banner: "https://wallpapercave.com/wp/wp6649889.jpg", // Demon Slayer banner
    highlight: "Reimagined Schedule Page",
    summary: "This update brings a completely redesigned Schedule page with a modern calendar view, enhanced filtering options, and improved mobile responsiveness.",
    categories: ["UI/UX", "Feature", "Mobile"],
    changes: [
      {
        title: "Modern Calendar View",
        description: "Completely redesigned Schedule page with an intuitive calendar grid layout",
        type: "feature",
        icon: <Sparkles size={18} />
      },
      {
        title: "Enhanced Filtering",
        description: "Added genre filtering, search functionality, and sorting options to the Schedule page",
        type: "feature",
        icon: <Search size={18} />
      },
      {
        title: "Mobile Optimization",
        description: "Improved mobile responsiveness for the Schedule page with a compact design",
        type: "improvement",
        icon: <Zap size={18} />
      },
      {
        title: "Piped API Integration",
        description: "Updated Anime Shorts feature to use Piped API for better video playback",
        type: "technical",
        icon: <Code size={18} />
      },
      {
        title: "Homepage Cleanup",
        description: "Removed Anime Shorts section from homepage for a cleaner layout",
        type: "improvement",
        icon: <Zap size={18} />
      }
    ]
  },
  {
    version: "1.2.0",
    date: "June 15, 2023",
    image: "https://i.pinimg.com/originals/e5/3d/f0/e53df0db7a0b9bea18a1c4be7fc35935.jpg", // Chainsaw Man - Denji
    banner: "https://wallpapercave.com/wp/wp11501349.jpg", // Chainsaw Man banner
    highlight: "Enhanced Visual Experience",
    summary: "This update focuses on improving the visual experience with anime logos, better mobile layouts, and high-quality artwork integration.",
    categories: ["UI/UX", "API", "Mobile"],
    changes: [
      {
        title: "Anime Logos",
        description: "Added anime logos to the hero slider for better brand recognition",
        type: "feature",
        icon: <Sparkles size={18} />
      },
      {
        title: "Mobile Layout",
        description: "Improved mobile layout for better visibility and usability on smaller screens",
        type: "improvement",
        icon: <Zap size={18} />
      },
      {
        title: "API Integration",
        description: "Integrated with TVDB and Fanart.tv APIs for high-quality artwork",
        type: "technical",
        icon: <Code size={18} />
      },
      {
        title: "Episode Tracking",
        description: "Added automatic episode count updates to keep information current",
        type: "feature",
        icon: <Calendar size={18} />
      },
      {
        title: "Bug Fixes",
        description: "Fixed studio display in anime info pages",
        type: "bugfix",
        icon: <Rocket size={18} />
      }
    ]
  },
  {
    version: "1.1.0",
    date: "May 20, 2023",
    image: "https://i.pinimg.com/originals/d7/5a/43/d75a439afbf46b9e5f9c7abe5cc40f0c.jpg", // Spy x Family - Anya
    banner: "https://wallpapercave.com/wp/wp10472356.png", // My Dress-Up Darling banner
    highlight: "Schedule & Pagination",
    summary: "This update introduces the anime schedule page and improves navigation with pagination for anime episodes.",
    categories: ["Feature", "UI/UX", "Search"],
    changes: [
      {
        title: "Anime Schedule",
        description: "Added anime schedule page to track upcoming releases",
        type: "feature",
        icon: <Calendar size={18} />
      },
      {
        title: "Episode Pagination",
        description: "Implemented pagination for anime episodes for easier navigation",
        type: "improvement",
        icon: <Zap size={18} />
      },
      {
        title: "Anime Relations",
        description: "Added anime relations (prequels, sequels) to info pages",
        type: "feature",
        icon: <Sparkles size={18} />
      },
      {
        title: "Search Improvements",
        description: "Improved search functionality with better results and filtering",
        type: "improvement",
        icon: <Search size={18} />
      },
      {
        title: "UI Bug Fixes",
        description: "Fixed various UI bugs and improved overall stability",
        type: "bugfix",
        icon: <Rocket size={18} />
      }
    ]
  },
  {
    version: "1.0.0",
    date: "April 10, 2023",
    image: "https://i.pinimg.com/originals/a9/d8/a4/a9d8a4faa9a4d95c47a7aaad8ce5d5b9.jpg", // Attack on Titan - Eren
    banner: "https://wallpapercave.com/wp/wp11501349.jpg", // Spy x Family banner
    highlight: "Initial Release",
    summary: "The first official release of AnimeHQ with basic anime browsing functionality, trending section, and watchlist features.",
    categories: ["Release", "Feature"],
    changes: [
      {
        title: "Initial Release",
        description: "First official release of AnimeHQ",
        type: "release",
        icon: <Rocket size={18} />
      },
      {
        title: "Anime Browsing",
        description: "Basic anime browsing functionality with categories and filters",
        type: "feature",
        icon: <Sparkles size={18} />
      },
      {
        title: "Trending Section",
        description: "Added trending anime section to showcase popular titles",
        type: "feature",
        icon: <Star size={18} />
      },
      {
        title: "Watchlist",
        description: "User watchlist feature to track favorite anime",
        type: "feature",
        icon: <Calendar size={18} />
      },
      {
        title: "Search Capabilities",
        description: "Basic search capabilities to find anime by title or genre",
        type: "feature",
        icon: <Search size={18} />
      }
    ]
  }
];

// Extract all unique categories from changelog data
const allCategories = Array.from(
  new Set(
    changelogData.flatMap(version => version.categories)
  )
).sort();

const Changelog = () => {
  // State for filters and expanded versions
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [expandedVersions, setExpandedVersions] = useState({});

  // Initialize expanded state for the first version
  useEffect(() => {
    const initialExpanded = {};
    if (changelogData.length > 0) {
      initialExpanded[changelogData[0].version] = true;
    }
    setExpandedVersions(initialExpanded);
  }, []);

  // Set page title
  useEffect(() => {
    document.title = "Changelog | AnimeHQ";
    return () => {
      document.title = "AnimeHQ"; // Reset title when component unmounts
    };
  }, []);

  // Toggle expanded state for a version
  const toggleExpanded = (version) => {
    setExpandedVersions(prev => ({
      ...prev,
      [version]: !prev[version]
    }));
  };

  // Toggle category filter
  const toggleCategory = (category) => {
    setSelectedCategories(prev =>
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  // Clear all filters
  const clearFilters = () => {
    setSelectedCategories([]);
    setSearchQuery("");
  };

  // Filter versions based on selected categories and search query
  const filteredVersions = changelogData.filter(version => {
    // If no categories selected and no search query, show all
    if (selectedCategories.length === 0 && !searchQuery) {
      return true;
    }

    // Filter by categories
    const matchesCategories = selectedCategories.length === 0 ||
      selectedCategories.some(cat => version.categories.includes(cat));

    // Filter by search query
    const query = searchQuery.toLowerCase();
    const matchesSearch = !searchQuery ||
      version.highlight.toLowerCase().includes(query) ||
      version.summary.toLowerCase().includes(query) ||
      version.changes.some(change =>
        change.title.toLowerCase().includes(query) ||
        change.description.toLowerCase().includes(query)
      );

    return matchesCategories && matchesSearch;
  });

  // Get change type color
  const getChangeTypeColor = (type) => {
    switch (type) {
      case 'feature':
        return 'bg-emerald-500/20 text-emerald-400 border-emerald-500/30';
      case 'improvement':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'bugfix':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'technical':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      case 'release':
        return 'bg-amber-500/20 text-amber-400 border-amber-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  return (
    <div className="min-h-screen pb-16">
      {/* Header */}
      <div className="relative overflow-hidden">
        {/* Background image with overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/80 via-black/70 to-black z-10"></div>
        <div className="absolute inset-0 bg-[url('https://giffiles.alphacoders.com/221/221132.gif')] bg-cover bg-center opacity-40"></div>

        {/* Content */}
        <div className="relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24">
          <div className="flex flex-col items-center text-center">
            <Link to="/" className="flex items-center text-gray-400 hover:text-white mb-6 transition-colors self-start">
              <ArrowLeft size={18} className="mr-2" />
              Back to Home
            </Link>

            <h1 className="text-4xl md:text-6xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 mb-4">
              Changelog
            </h1>

            <p className="text-gray-300 text-lg md:text-xl max-w-3xl">
              Track our journey as we continuously improve <span className="text-blue-400 font-medium">AnimeHQ</span> with new features,
              enhancements, and fixes to provide you with the best anime experience.
            </p>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="sticky top-0 z-30 bg-black/80 backdrop-blur-md border-b border-white/10 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
            {/* Search */}
            <div className="relative flex-1 w-full">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search updates..."
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-200"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" size={18} />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery("")}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white"
                >
                  <X size={16} />
                </button>
              )}
            </div>

            {/* Category filters */}
            <div className="flex flex-wrap gap-2 w-full md:w-auto">
              <div className="text-xs text-gray-400 mr-1 flex items-center">
                <Filter size={12} className="mr-1" /> Filter:
              </div>
              {allCategories.map(category => (
                <button
                  key={category}
                  onClick={() => toggleCategory(category)}
                  className={`px-3 py-1 rounded-full text-xs flex items-center gap-1.5 transition-all duration-300
                            ${selectedCategories.includes(category)
                      ? 'bg-blue-600 text-white font-medium'
                      : 'bg-white/5 text-gray-300 hover:bg-white/10'
                    }`}
                >
                  {selectedCategories.includes(category) ?
                    <div className="w-1.5 h-1.5 rounded-full bg-white"></div> :
                    <div className="w-1.5 h-1.5 rounded-full bg-gray-500"></div>
                  }
                  {category}
                </button>
              ))}

              {(selectedCategories.length > 0 || searchQuery) && (
                <button
                  onClick={clearFilters}
                  className="px-3 py-1 rounded-full text-xs bg-red-500/20 text-red-400 hover:bg-red-500/30
                            transition-all duration-300 flex items-center gap-1.5"
                >
                  <X size={12} />
                  Clear
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {filteredVersions.length > 0 ? (
          <div className="space-y-12">
            {filteredVersions.map((version, index) => (
              <div
                key={version.version}
                id={`version-${version.version}`}
                className="bg-white/5 rounded-xl overflow-hidden border border-white/10 hover:border-white/20 transition-all duration-300"
              >
                {/* Version header */}
                <div className="relative">
                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black via-black/80 to-transparent z-10"></div>

                  {/* Banner image */}
                  <img
                    src={version.banner}
                    alt={`Version ${version.version} banner`}
                    className="w-full h-48 object-cover object-center"
                  />

                  {/* Version info */}
                  <div className="absolute bottom-0 left-0 right-0 p-6 z-20">
                    <div className="flex items-center gap-4">
                      <img
                        src={version.image}
                        alt={`Version ${version.version} character`}
                        className="w-16 h-16 object-cover rounded-full border-2 border-white/30"
                      />
                      <div>
                        <div className="flex items-center gap-3 mb-1">
                          <div className="px-3 py-1 bg-blue-600/90 rounded-full text-white text-sm font-mono">
                            v{version.version}
                          </div>
                          <span className="text-gray-400 text-sm">{version.date}</span>
                          {index === 0 && (
                            <div className="px-2 py-0.5 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full text-white text-xs font-medium">
                              Latest
                            </div>
                          )}
                        </div>
                        <h2 className="text-2xl font-bold text-white">
                          {version.highlight}
                        </h2>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Version content */}
                <div className="p-6">
                  {/* Categories */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {version.categories.map(category => (
                      <span
                        key={category}
                        className="px-2 py-0.5 bg-white/5 text-gray-300 rounded-full text-xs cursor-pointer hover:bg-white/10"
                        onClick={() => toggleCategory(category)}
                      >
                        {category}
                      </span>
                    ))}
                  </div>

                  {/* Summary */}
                  <p className="text-gray-300 mb-6">{version.summary}</p>

                  {/* Expand/collapse button */}
                  <button
                    onClick={() => toggleExpanded(version.version)}
                    className="flex items-center gap-1.5 text-blue-400 hover:text-blue-300 transition-colors mb-6"
                  >
                    <div className="w-5 h-5 rounded-full bg-blue-500/20 flex items-center justify-center">
                      {expandedVersions[version.version] ? (
                        <ChevronUp size={14} />
                      ) : (
                        <ChevronDown size={14} />
                      )}
                    </div>
                    <span className="text-sm font-medium">
                      {expandedVersions[version.version] ? "Hide changes" : "View all changes"}
                    </span>
                  </button>

                  {/* Changes */}
                  {expandedVersions[version.version] && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6 animate-[fadeIn_0.3s_ease-in-out]">
                      {version.changes.map((change, i) => (
                        <div
                          key={i}
                          className={`p-4 rounded-lg border ${getChangeTypeColor(change.type)} hover:translate-y-[-2px] transition-all duration-300`}
                        >
                          <div className="flex items-start gap-3">
                            <div className={`p-2 rounded-full ${getChangeTypeColor(change.type)} flex-shrink-0`}>
                              {change.icon}
                            </div>
                            <div>
                              <h4 className="font-semibold text-white">{change.title}</h4>
                              <p className="text-gray-300 text-sm mt-1">{change.description}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-16 px-4">
            <div className="inline-block p-6 rounded-full bg-white/10 border border-white/20 mb-6">
              <Search size={40} className="text-white" />
            </div>
            <h3 className="text-2xl font-semibold mb-3 text-white">No updates match your filters</h3>
            <p className="text-gray-300 mb-8 max-w-md mx-auto">We couldn't find any changelog entries that match your current search criteria. Try adjusting your filters.</p>
            <button
              onClick={clearFilters}
              className="px-6 py-3 bg-white/80 text-black rounded-full hover:bg-white transition-all duration-300 font-medium"
            >
              Clear all filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Changelog;
