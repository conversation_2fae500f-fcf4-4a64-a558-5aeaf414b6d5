import { useTitlePreference, TITLE_PREFERENCES } from '@/context/TitlePreferenceContext';
import { Languages } from 'lucide-react';

const TitlePreferenceToggle = ({ className = '' }) => {
  const { titlePreference, toggleTitlePreference } = useTitlePreference();

  const isEnglish = titlePreference === TITLE_PREFERENCES.ENGLISH;

  return (
    <button
      onClick={toggleTitlePreference}
      className={`flex items-center gap-1.5 px-3 py-2 bg-white/10 hover:bg-white/15 rounded-lg text-sm transition-all duration-200 ${className}`}
      aria-label="Toggle between English and Romaji titles"
      title={`Currently showing ${isEnglish ? 'English' : 'Romaji'} titles. Click to toggle.`}
    >
      <Languages size={18} className={isEnglish ? "text-blue-400" : "text-yellow-400"} />
      <span className="hidden md:inline font-medium">
        {isEnglish ? 'EN' : 'JP'}
      </span>
    </button>
  );
};

export default TitlePreferenceToggle;
