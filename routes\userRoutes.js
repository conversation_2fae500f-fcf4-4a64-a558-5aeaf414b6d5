import express from 'express';
import axios from 'axios';
import UserProgress from '../models/UserProgress.js';
import { verifyAniListToken, checkUserMatch } from '../middleware/auth.js';

// Helper functions for achievements and ranks
async function checkStreakAchievements(userProgress, streak) {
  // Define streak milestones
  const streakMilestones = [3, 7, 14, 30, 60, 100, 365];

  // Check if current streak hits a milestone
  const milestone = streakMilestones.find(m => streak === m);

  if (milestone) {
    // Create achievement if it doesn't exist
    const achievementId = `streak-${milestone}`;
    const existingAchievement = userProgress.achievements?.find(a => a.id === achievementId);

    if (!existingAchievement) {
      // Initialize achievements array if it doesn't exist
      if (!userProgress.achievements) {
        userProgress.achievements = [];
      }

      // Add the achievement
      userProgress.achievements.push({
        id: achievementId,
        title: `${milestone} Day Streak!`,
        description: `You've visited the site for ${milestone} consecutive days!`,
        date: new Date(),
        type: 'streak'
      });

      // Add XP reward based on milestone
      const xpReward = milestone * 5;
      userProgress.xp += xpReward;

      console.log(`User earned ${milestone} day streak achievement! +${xpReward} XP`);
    }
  }
}

async function checkLikeMilestones(userProgress, likes) {
  // Define like milestones
  const likeMilestones = [10, 50, 100, 500, 1000];

  // Check if current likes hit a milestone
  const milestone = likeMilestones.find(m => likes === m);

  if (milestone) {
    // Create achievement if it doesn't exist
    const achievementId = `likes-${milestone}`;
    const existingAchievement = userProgress.achievements?.find(a => a.id === achievementId);

    if (!existingAchievement) {
      // Initialize achievements array if it doesn't exist
      if (!userProgress.achievements) {
        userProgress.achievements = [];
      }

      // Add the achievement
      userProgress.achievements.push({
        id: achievementId,
        title: `${milestone} Likes!`,
        description: `Your profile has received ${milestone} likes!`,
        date: new Date(),
        type: 'likes'
      });

      // Add XP reward based on milestone
      const xpReward = milestone;
      userProgress.xp += xpReward;

      console.log(`User earned ${milestone} likes achievement! +${xpReward} XP`);
    }
  }
}

async function updateRank(userProgress, level) {
  // Define ranks based on level ranges
  const ranks = [
    { name: 'Rookie', minLevel: 1 },
    { name: 'Anime Fan', minLevel: 5 },
    { name: 'Anime Enthusiast', minLevel: 10 },
    { name: 'Anime Devotee', minLevel: 15 },
    { name: 'Anime Veteran', minLevel: 20 },
    { name: 'Anime Expert', minLevel: 30 },
    { name: 'Anime Master', minLevel: 50 },
    { name: 'Anime Legend', minLevel: 75 },
    { name: 'Anime Sage', minLevel: 100 }
  ];

  // Find the highest rank the user qualifies for
  const newRank = ranks
    .filter(rank => level >= rank.minLevel)
    .reduce((highest, current) =>
      current.minLevel > highest.minLevel ? current : highest,
      ranks[0]
    );

  // Check if rank has changed
  if (newRank.name !== userProgress.rank) {
    const oldRank = userProgress.rank;
    userProgress.rank = newRank.name;

    // Add rank achievement
    if (!userProgress.achievements) {
      userProgress.achievements = [];
    }

    const achievementId = `rank-${newRank.name.toLowerCase().replace(/\s+/g, '-')}`;

    // Only add if this exact achievement doesn't exist
    if (!userProgress.achievements.some(a => a.id === achievementId)) {
      userProgress.achievements.push({
        id: achievementId,
        title: `Reached ${newRank.name} Rank!`,
        description: `You've achieved the rank of ${newRank.name}!`,
        date: new Date(),
        type: 'rank'
      });

      // Add XP reward for rank up
      const xpReward = newRank.minLevel * 10;
      userProgress.xp += xpReward;

      console.log(`User ranked up from ${oldRank} to ${newRank.name}! +${xpReward} XP`);
    }
  }
}

const router = express.Router();

/**
 * Get or create user progress
 * GET /api/users/:userId
 */
router.get('/users/:userId', async (req, res) => {
  try {
    let userProgress = await UserProgress.findOne({ anilistId: req.params.userId });

    // If user doesn't exist, create a new record
    if (!userProgress) {
      userProgress = new UserProgress({
        anilistId: req.params.userId
      });
      await userProgress.save();
    }

    res.json(userProgress);
  } catch (error) {
    console.error('Error fetching user progress:', error);
    res.status(500).json({ message: error.message });
  }
});

/**
 * Record a visit and update streak
 * POST /api/users/:userId/visits
 */
router.post('/users/:userId/visits', verifyAniListToken, checkUserMatch, async (req, res) => {
  try {
    // Find the user
    const userProgress = await UserProgress.findOne({ anilistId: req.params.userId });

    if (!userProgress) {
      return res.status(404).json({ message: 'User not found' });
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get yesterday's date
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    // Check if already visited today
    let xpEarned = 0;

    if (userProgress.lastVisit) {
      const lastVisitDate = new Date(userProgress.lastVisit);
      lastVisitDate.setHours(0, 0, 0, 0);

      if (lastVisitDate.getTime() === today.getTime()) {
        console.log(`User ${req.params.userId} already visited today. No XP awarded.`);

        return res.json({
          message: 'Already recorded a visit today',
          streak: userProgress.streak,
          lastVisit: userProgress.lastVisit,
          xpEarned: 0,
          dailyVisitAlreadyRecorded: true
        });
      }
    }

    // Calculate new streak
    let newStreak = userProgress.streak;

    if (userProgress.lastVisit) {
      const lastVisitDate = new Date(userProgress.lastVisit);
      lastVisitDate.setHours(0, 0, 0, 0);

      if (lastVisitDate.getTime() === yesterday.getTime()) {
        // Consecutive day, increment streak
        newStreak += 1;
      } else if (lastVisitDate.getTime() !== today.getTime()) {
        // Not consecutive and not today already, reset streak
        newStreak = 1;
      }
    } else {
      // First visit ever
      newStreak = 1;
    }

    // Update visit dates array
    let visitDates = userProgress.visitDates || [];
    visitDates.push(today);

    // Keep only the last 30 days of visits
    if (visitDates.length > 30) {
      visitDates = visitDates.slice(visitDates.length - 30);
    }

    // Update longest streak if current streak is longer
    const newLongestStreak = Math.max(userProgress.longestStreak || 0, newStreak);

    // Add XP for daily visit (10 XP)
    userProgress.xp = (userProgress.xp || 0) + 10;
    xpEarned = 10;

    // Update user progress
    userProgress.streak = newStreak;
    userProgress.longestStreak = newLongestStreak;
    userProgress.lastVisit = today;
    userProgress.visitDates = visitDates;

    // Check for streak achievements
    await checkStreakAchievements(userProgress, newStreak);

    // Auto-complete the daily visit task
    if (!userProgress.tasks) {
      userProgress.tasks = {
        completedTasks: [],
        lastReset: today
      };
    }

    // Initialize completedTasks array if it doesn't exist
    if (!userProgress.tasks.completedTasks) {
      userProgress.tasks.completedTasks = [];
    }

    // Check if tasks need to be reset (new day)
    if (!userProgress.tasks.lastReset ||
        new Date(userProgress.tasks.lastReset).getTime() < today.getTime()) {
      userProgress.tasks.completedTasks = [];
      userProgress.tasks.lastReset = today;
    }

    // Check if the visit task is already completed
    if (!userProgress.tasks.completedTasks.includes('visit')) {
      userProgress.tasks.completedTasks.push('visit');
    }

    // Calculate new level based on XP
    const oldLevel = userProgress.level;
    const newLevel = Math.max(1, Math.floor(Math.pow(userProgress.xp / 100, 0.4) * 1.5));

    // Check if user leveled up
    if (newLevel > oldLevel) {
      userProgress.level = newLevel;
      console.log(`User ${req.params.userId} leveled up to ${newLevel}!`);

      // Update rank based on new level
      await updateRank(userProgress, newLevel);
    }

    // Save all changes
    await userProgress.save();

    console.log(`Visit recorded for user ${req.params.userId}. Streak: ${newStreak}, XP earned: ${xpEarned}`);

    res.json({
      message: 'Visit recorded successfully',
      streak: newStreak,
      longestStreak: newLongestStreak,
      lastVisit: today,
      xp: userProgress.xp,
      level: userProgress.level,
      rank: userProgress.rank,
      xpEarned: xpEarned,
      levelUp: newLevel > oldLevel
    });
  } catch (error) {
    console.error('Error recording visit:', error);
    res.status(500).json({ message: error.message });
  }
});

/**
 * Get daily tasks
 * GET /api/users/:userId/tasks
 */
router.get('/users/:userId/tasks', verifyAniListToken, checkUserMatch, async (req, res) => {
  try {
    // Define all available tasks with their details
    const taskDefinitions = {
      'visit': {
        id: 'visit',
        title: 'Daily Visit',
        description: 'Visit the site today',
        xpReward: 10,
        autoComplete: true
      },
      'watch-episode': {
        id: 'watch-episode',
        title: 'Watch an Episode',
        description: 'Watch at least one anime episode today',
        xpReward: 15
      },
      'add-anime': {
        id: 'add-anime',
        title: 'Add to List',
        description: 'Add a new anime to your list',
        xpReward: 20
      },
      'rate-anime': {
        id: 'rate-anime',
        title: 'Rate an Anime',
        description: 'Rate an anime in your list',
        xpReward: 25
      },
      'update-progress': {
        id: 'update-progress',
        title: 'Update Progress',
        description: 'Update your progress on any anime',
        xpReward: 15
      },
      'explore-genres': {
        id: 'explore-genres',
        title: 'Explore Genres',
        description: 'Browse anime from a genre you don\'t usually watch',
        xpReward: 20
      },
      'watch-seasonal': {
        id: 'watch-seasonal',
        title: 'Watch Seasonal',
        description: 'Watch an episode from a currently airing anime',
        xpReward: 30
      },
      'complete-all': {
        id: 'complete-all',
        title: 'Complete All Tasks',
        description: 'Complete all other daily tasks',
        xpReward: 50,
        special: true,
        autoComplete: true
      }
    };

    // Find the user
    const userProgress = await UserProgress.findOne({ anilistId: req.params.userId });

    if (!userProgress) {
      // If user not found, create a new user record
      const newUserProgress = new UserProgress({
        anilistId: req.params.userId,
        tasks: {
          completedTasks: [],
          lastReset: new Date()
        }
      });

      await newUserProgress.save();
      console.log(`Created new user record for ${req.params.userId}`);

      // Return empty tasks
      const allTasks = Object.values(taskDefinitions).map(task => ({
        ...task,
        completed: false
      }));

      return res.json({
        tasks: allTasks,
        completedTasks: [],
        lastReset: newUserProgress.tasks.lastReset
      });
    }

    // Check if tasks need to be reset (new day)
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Initialize tasks if not already initialized
    if (!userProgress.tasks) {
      userProgress.tasks = {
        completedTasks: [],
        lastReset: today
      };
    }

    // Initialize completedTasks array if it doesn't exist
    if (!userProgress.tasks.completedTasks) {
      userProgress.tasks.completedTasks = [];
    }

    // Reset tasks if it's a new day
    let tasksUpdated = false;

    if (!userProgress.tasks.lastReset ||
        new Date(userProgress.tasks.lastReset).getTime() < today.getTime()) {
      console.log(`Resetting tasks for user ${req.params.userId} (new day)`);
      userProgress.tasks.completedTasks = [];
      userProgress.tasks.lastReset = today;
      tasksUpdated = true;

      // Auto-complete the visit task if the user has visited today
      if (userProgress.lastVisit) {
        const lastVisitDate = new Date(userProgress.lastVisit);
        lastVisitDate.setHours(0, 0, 0, 0);

        if (lastVisitDate.getTime() === today.getTime()) {
          userProgress.tasks.completedTasks.push('visit');
        }
      }
    }

    // Create the tasks array with completion status
    const allTasks = Object.values(taskDefinitions).map(task => ({
      ...task,
      completed: userProgress.tasks.completedTasks.includes(task.id)
    }));

    // Check if all regular tasks are completed
    const regularTasks = allTasks.filter(task => !task.special);
    const allRegularTasksCompleted = regularTasks.every(task => task.completed);

    // If all regular tasks are completed, mark the bonus task as completed
    if (allRegularTasksCompleted && !userProgress.tasks.completedTasks.includes('complete-all')) {
      userProgress.tasks.completedTasks.push('complete-all');
      userProgress.xp = (userProgress.xp || 0) + 50; // Add XP for completing all tasks
      tasksUpdated = true;

      // Update the complete-all task status
      const completeAllTask = allTasks.find(task => task.id === 'complete-all');
      if (completeAllTask) {
        completeAllTask.completed = true;
      }

      console.log(`All tasks completed by user ${req.params.userId}. Awarded bonus 50 XP.`);
    }

    // Save changes if needed
    if (tasksUpdated) {
      await userProgress.save();
    }

    res.json({
      tasks: allTasks,
      completedTasks: userProgress.tasks.completedTasks,
      lastReset: userProgress.tasks.lastReset
    });
  } catch (error) {
    console.error('Error fetching tasks:', error);

    // Return empty tasks in case of error
    const taskDefinitions = {
      'visit': {
        id: 'visit',
        title: 'Daily Visit',
        description: 'Visit the site today',
        xpReward: 10,
        autoComplete: true
      },
      'watch-episode': {
        id: 'watch-episode',
        title: 'Watch an Episode',
        description: 'Watch at least one anime episode today',
        xpReward: 15
      },
      'add-anime': {
        id: 'add-anime',
        title: 'Add to List',
        description: 'Add a new anime to your list',
        xpReward: 20
      },
      'rate-anime': {
        id: 'rate-anime',
        title: 'Rate an Anime',
        description: 'Rate an anime in your list',
        xpReward: 25
      },
      'update-progress': {
        id: 'update-progress',
        title: 'Update Progress',
        description: 'Update your progress on any anime',
        xpReward: 15
      },
      'explore-genres': {
        id: 'explore-genres',
        title: 'Explore Genres',
        description: 'Browse anime from a genre you don\'t usually watch',
        xpReward: 20
      },
      'watch-seasonal': {
        id: 'watch-seasonal',
        title: 'Watch Seasonal',
        description: 'Watch an episode from a currently airing anime',
        xpReward: 30
      },
      'complete-all': {
        id: 'complete-all',
        title: 'Complete All Tasks',
        description: 'Complete all other daily tasks',
        xpReward: 50,
        special: true,
        autoComplete: true
      }
    };

    const allTasks = Object.values(taskDefinitions).map(task => ({
      ...task,
      completed: false
    }));

    res.json({
      tasks: allTasks,
      completedTasks: [],
      lastReset: new Date(),
      error: error.message
    });
  }
});

/**
 * Complete a task
 * POST /api/users/:userId/tasks/:taskId/complete
 */
router.post('/users/:userId/tasks/:taskId/complete', verifyAniListToken, checkUserMatch, async (req, res) => {
  try {
    // Use findOne to get the user
    const userProgress = await UserProgress.findOne({ anilistId: req.params.userId });

    if (!userProgress) {
      return res.status(404).json({ message: 'User not found' });
    }

    const taskId = req.params.taskId;
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Define all available tasks with their details
    const taskDefinitions = {
      'visit': {
        id: 'visit',
        title: 'Daily Visit',
        description: 'Visit the site today',
        xpReward: 10,
        autoComplete: true // This task is auto-completed when the user visits
      },
      'watch-episode': {
        id: 'watch-episode',
        title: 'Watch an Episode',
        description: 'Watch at least one anime episode today',
        xpReward: 15
      },
      'add-anime': {
        id: 'add-anime',
        title: 'Add to List',
        description: 'Add a new anime to your list',
        xpReward: 20
      },
      'rate-anime': {
        id: 'rate-anime',
        title: 'Rate an Anime',
        description: 'Rate an anime in your list',
        xpReward: 25
      },
      'update-progress': {
        id: 'update-progress',
        title: 'Update Progress',
        description: 'Update your progress on any anime',
        xpReward: 15
      },
      'explore-genres': {
        id: 'explore-genres',
        title: 'Explore Genres',
        description: 'Browse anime from a genre you don\'t usually watch',
        xpReward: 20
      },
      'watch-seasonal': {
        id: 'watch-seasonal',
        title: 'Watch Seasonal',
        description: 'Watch an episode from a currently airing anime',
        xpReward: 30
      },
      'complete-all': {
        id: 'complete-all',
        title: 'Complete All Tasks',
        description: 'Complete all other daily tasks',
        xpReward: 50,
        special: true,
        autoComplete: true // This task is auto-completed when all other tasks are done
      }
    };

    // Check if task exists
    if (!taskDefinitions[taskId]) {
      return res.status(400).json({ message: 'Invalid task ID' });
    }

    // Initialize tasks if not already initialized
    if (!userProgress.tasks) {
      userProgress.tasks = {
        completedTasks: [],
        lastReset: today
      };
    }

    // Initialize completedTasks array if it doesn't exist
    if (!userProgress.tasks.completedTasks) {
      userProgress.tasks.completedTasks = [];
    }

    // Check if tasks need to be reset (new day)
    if (!userProgress.tasks.lastReset ||
        new Date(userProgress.tasks.lastReset).getTime() < today.getTime()) {
      console.log(`Resetting tasks for user ${req.params.userId} (new day)`);
      userProgress.tasks.completedTasks = [];
      userProgress.tasks.lastReset = today;

      // Auto-complete the visit task if the user has visited today
      if (userProgress.lastVisit) {
        const lastVisitDate = new Date(userProgress.lastVisit);
        lastVisitDate.setHours(0, 0, 0, 0);

        if (lastVisitDate.getTime() === today.getTime()) {
          userProgress.tasks.completedTasks.push('visit');
        }
      }
    }

    // Check if task is already completed
    if (userProgress.tasks.completedTasks.includes(taskId)) {
      console.log(`Task ${taskId} already completed by user ${req.params.userId}. No XP awarded.`);

      // Get all tasks with completion status
      const allTasks = Object.keys(taskDefinitions).map(id => ({
        ...taskDefinitions[id],
        completed: userProgress.tasks.completedTasks.includes(id)
      }));

      return res.json({
        message: 'Task already completed',
        completed: true,
        xpEarned: 0,
        taskAlreadyCompleted: true,
        tasks: allTasks
      });
    }

    // Special handling for auto-complete tasks
    if (taskDefinitions[taskId].autoComplete) {
      return res.status(400).json({
        message: `The "${taskDefinitions[taskId].title}" task is completed automatically`,
        completed: false,
        xpEarned: 0,
        autoComplete: true
      });
    }

    // Get XP reward for this task
    const xpReward = taskDefinitions[taskId].xpReward;

    // Add task to completed tasks
    userProgress.tasks.completedTasks.push(taskId);

    // Add XP
    userProgress.xp = (userProgress.xp || 0) + xpReward;

    console.log(`Task ${taskId} completed by user ${req.params.userId}. Awarded ${xpReward} XP.`);

    // Check if all regular tasks are completed
    const regularTaskIds = Object.keys(taskDefinitions).filter(id => !taskDefinitions[id].special);
    const allRegularTasksCompleted = regularTaskIds.every(task =>
      userProgress.tasks.completedTasks.includes(task)
    );

    // If all regular tasks are completed and bonus task is not, complete it automatically
    let bonusXp = 0;
    if (allRegularTasksCompleted && !userProgress.tasks.completedTasks.includes('complete-all')) {
      userProgress.tasks.completedTasks.push('complete-all');
      bonusXp = taskDefinitions['complete-all'].xpReward;
      userProgress.xp += bonusXp;
      console.log(`All tasks completed by user ${req.params.userId}. Awarded bonus ${bonusXp} XP.`);
    }

    // Calculate new level based on XP
    const oldLevel = userProgress.level;
    const newLevel = Math.max(1, Math.floor(Math.pow(userProgress.xp / 100, 0.4) * 1.5));

    // Check if user leveled up
    if (newLevel > oldLevel) {
      userProgress.level = newLevel;
      console.log(`User ${req.params.userId} leveled up to ${newLevel}!`);

      // Update rank based on new level
      await updateRank(userProgress, newLevel);
    }

    await userProgress.save();

    // Get updated list of all tasks with completion status
    const allTasks = Object.keys(taskDefinitions).map(id => ({
      ...taskDefinitions[id],
      completed: userProgress.tasks.completedTasks.includes(id)
    }));

    res.json({
      message: 'Task completed successfully',
      completedTasks: userProgress.tasks.completedTasks,
      xpEarned: xpReward,
      bonusXp: bonusXp,
      allTasksCompleted: allRegularTasksCompleted,
      levelUp: newLevel > oldLevel,
      newLevel: newLevel,
      tasks: allTasks
    });
  } catch (error) {
    console.error('Error completing task:', error);
    res.status(500).json({ message: error.message });
  }
});

/**
 * Add a like to a user's profile
 * POST /api/users/:userId/likes
 */
router.post('/users/:userId/likes', verifyAniListToken, async (req, res) => {
  try {
    const targetUserId = req.params.userId;
    const likingUserId = req.user.id.toString();

    // Prevent users from liking themselves
    if (targetUserId === likingUserId) {
      return res.status(400).json({ message: 'You cannot like your own profile' });
    }

    // Find the target user
    const userProgress = await UserProgress.findOne({ anilistId: targetUserId });

    if (!userProgress) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Initialize likedBy array if it doesn't exist
    if (!userProgress.likedBy) {
      userProgress.likedBy = [];
    }

    // Check if user has already liked this profile
    const alreadyLiked = userProgress.likedBy.includes(likingUserId);

    if (alreadyLiked) {
      console.log(`User ${likingUserId} already liked profile ${targetUserId}. No action taken.`);
      return res.json({
        message: 'You have already liked this profile',
        likes: userProgress.likes,
        alreadyLiked: true,
        hasLiked: true
      });
    }

    // Find the liking user to update their liked profiles
    const likingUserProgress = await UserProgress.findOne({ anilistId: likingUserId });

    // If the liking user doesn't exist in our database yet, create a record for them
    if (!likingUserProgress) {
      const newUserProgress = new UserProgress({
        anilistId: likingUserId,
        likedProfiles: [targetUserId]
      });

      await newUserProgress.save();
      console.log(`Created new user record for ${likingUserId}`);
    } else {
      // Initialize likedProfiles array if it doesn't exist
      if (!likingUserProgress.likedProfiles) {
        likingUserProgress.likedProfiles = [];
      }

      // Add target user to likedProfiles if not already there
      if (!likingUserProgress.likedProfiles.includes(targetUserId)) {
        likingUserProgress.likedProfiles.push(targetUserId);
        await likingUserProgress.save();
        console.log(`Updated liked profiles for user ${likingUserId}`);
      }
    }

    // Add like to target user
    userProgress.likes = (userProgress.likes || 0) + 1;
    userProgress.likedBy.push(likingUserId);

    // Add XP for receiving a like
    userProgress.xp = (userProgress.xp || 0) + 5;

    // Check for like milestones
    await checkLikeMilestones(userProgress, userProgress.likes);

    // Save the changes
    await userProgress.save();

    console.log(`User ${likingUserId} liked profile ${targetUserId}. New like count: ${userProgress.likes}`);

    res.json({
      message: 'Like added successfully',
      likes: userProgress.likes,
      alreadyLiked: false,
      hasLiked: true
    });
  } catch (error) {
    console.error('Error adding like:', error);
    res.status(500).json({ message: error.message });
  }
});

/**
 * Get user likes
 * GET /api/users/:userId/likes
 */
router.get('/users/:userId/likes', async (req, res) => {
  try {
    const userProgress = await UserProgress.findOne({ anilistId: req.params.userId });

    if (!userProgress) {
      // If user not found, return empty data with hasLiked: false
      return res.json({
        likes: 0,
        likedBy: [],
        likedProfiles: [],
        hasLiked: false
      });
    }

    // Get the authenticated user ID if available
    let currentUserId = null;
    let hasLiked = false;

    if (req.headers.authorization) {
      try {
        const token = req.headers.authorization.split(' ')[1];
        const response = await axios.post('https://graphql.anilist.co', {
          query: `{ Viewer { id } }`
        }, {
          headers: { 'Authorization': `Bearer ${token}` }
        });

        if (response.data.data.Viewer) {
          currentUserId = response.data.data.Viewer.id.toString();

          // Check if the current user has liked this profile
          if (userProgress.likedBy && userProgress.likedBy.length > 0) {
            hasLiked = userProgress.likedBy.includes(currentUserId);
          }

          console.log(`User ${currentUserId} has${hasLiked ? '' : ' not'} liked profile ${req.params.userId}`);
        }
      } catch (authError) {
        console.error('Error verifying token:', authError.message);
      }
    }

    // Ensure all properties exist
    const likes = userProgress.likes || 0;
    const likedBy = userProgress.likedBy || [];
    const likedProfiles = userProgress.likedProfiles || [];

    res.json({
      likes,
      likedBy,
      likedProfiles,
      hasLiked
    });
  } catch (error) {
    console.error('Error fetching likes:', error);

    // Return empty data with hasLiked: false in case of error
    res.json({
      likes: 0,
      likedBy: [],
      likedProfiles: [],
      hasLiked: false,
      error: error.message
    });
  }
});

/**
 * Get leaderboard data
 * GET /api/leaderboard
 */
router.get('/leaderboard', async (req, res) => {
  try {
    // Get query parameters for pagination and sorting
    const limit = parseInt(req.query.limit) || 10;
    const page = parseInt(req.query.page) || 1;
    const sortBy = req.query.sortBy || 'level'; // Default sort by level
    const skip = (page - 1) * limit;

    // Validate sortBy parameter
    const validSortFields = ['level', 'xp', 'streak', 'likes'];
    if (!validSortFields.includes(sortBy)) {
      return res.status(400).json({ message: 'Invalid sort field' });
    }

    // Create sort object
    const sort = { [sortBy]: -1 }; // -1 for descending order

    // Get total count for pagination
    const total = await UserProgress.countDocuments();

    // Get leaderboard data
    const leaderboard = await UserProgress.find({})
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('anilistId level xp rank streak likes lastVisit');

    // Calculate total pages
    const totalPages = Math.ceil(total / limit);

    res.json({
      leaderboard,
      pagination: {
        total,
        page,
        limit,
        totalPages
      }
    });
  } catch (error) {
    console.error('Error fetching leaderboard:', error);
    res.status(500).json({ message: error.message });
  }
});

export default router;
