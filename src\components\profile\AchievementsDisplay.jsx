import { useState } from "react";
import { useUserActivity } from "@/context/UserActivityContext";
import { Award, Gift, ChevronDown, ChevronUp } from "lucide-react";

const AchievementsDisplay = () => {
  const { achievements, rewards } = useUserActivity();
  const [expanded, setExpanded] = useState(false);
  
  // Sort achievements by date (newest first)
  const sortedAchievements = [...achievements].sort((a, b) => b.date - a.date);
  
  // Get the most recent achievements to display
  const recentAchievements = sortedAchievements.slice(0, expanded ? sortedAchievements.length : 3);
  
  // Check if there are more achievements to show
  const hasMoreAchievements = sortedAchievements.length > 3;
  
  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm text-white/80 flex items-center gap-1">
          <Award size={14} className="text-yellow-500" />
          Your Achievements
        </h3>
        <span className="text-xs text-white/60">{achievements.length} total</span>
      </div>
      
      {achievements.length === 0 ? (
        <div className="text-center py-4 text-white/60 text-sm bg-black/20 rounded-lg">
          No achievements yet. Keep using the site to earn rewards!
        </div>
      ) : (
        <div className="space-y-2">
          {recentAchievements.map((achievement, index) => (
            <div 
              key={achievement.id} 
              className="bg-black/30 border border-white/10 rounded-lg p-3 flex items-start gap-3"
            >
              <div className="bg-yellow-500/20 p-2 rounded-full">
                <Award size={16} className="text-yellow-500" />
              </div>
              <div className="flex-1">
                <h4 className="text-sm font-medium">{achievement.title}</h4>
                <p className="text-xs text-white/60 mt-0.5">{achievement.description}</p>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-[10px] text-white/40">
                    {new Date(achievement.date).toLocaleDateString()}
                  </span>
                  {achievement.type === 'streak' && (
                    <span className="text-[10px] bg-orange-500/20 text-orange-400 px-1.5 py-0.5 rounded-full">
                      Streak
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
          
          {hasMoreAchievements && (
            <button 
              className="w-full text-center text-xs text-white/60 hover:text-white py-1 flex items-center justify-center gap-1"
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? (
                <>
                  Show Less <ChevronUp size={14} />
                </>
              ) : (
                <>
                  Show More <ChevronDown size={14} />
                </>
              )}
            </button>
          )}
        </div>
      )}
      
      {/* Recent Rewards */}
      {rewards.length > 0 && (
        <div className="mt-4">
          <div className="flex items-center gap-1 mb-2">
            <Gift size={14} className="text-purple-400" />
            <h3 className="text-sm text-white/80">Recent Rewards</h3>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {rewards.slice(0, 3).map((reward) => (
              <div 
                key={reward.id}
                className="bg-black/30 border border-white/10 rounded-lg p-2 flex items-center gap-2"
              >
                <div className="bg-purple-500/20 p-1.5 rounded-full">
                  <Gift size={12} className="text-purple-400" />
                </div>
                <div>
                  <p className="text-xs">{reward.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AchievementsDisplay;
