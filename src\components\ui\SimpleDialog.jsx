import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";

// Version history with changes
const changelogData = [
  {
    version: "1.4.0",
    date: "May 20, 2024",
    changes: [
      "AniList integration for the hero slider's Add to List button",
      "Improved anime poster positioning in the hero slider",
      "Made AniList sync button size match with the Watch Now button for consistent UI"
    ]
  },
  {
    version: "1.3.0",
    date: "July 10, 2023",
    changes: [
      "Reimagined Schedule page with modern calendar view",
      "Added enhanced filtering options to Schedule page (genre, search, sorting)",
      "Improved mobile responsiveness for Schedule page",
      "Updated Anime Shorts feature to use Piped API for videos",
      "Removed Anime Shorts section from homepage for cleaner layout"
    ]
  },
  {
    version: "1.2.0",
    date: "June 15, 2023",
    changes: [
      "Added anime logos to the hero slider",
      "Improved mobile layout for better visibility",
      "Integrated with TVDB and Fanart.tv APIs for high-quality artwork",
      "Added automatic episode count updates",
      "Fixed studio display in anime info pages"
    ]
  },
  {
    version: "1.1.0",
    date: "May 20, 2023",
    changes: [
      "Added anime schedule page",
      "Implemented pagination for anime episodes",
      "Added anime relations (prequels, sequels) to info pages",
      "Improved search functionality",
      "Fixed various UI bugs"
    ]
  },
  {
    version: "1.0.0",
    date: "April 10, 2023",
    changes: [
      "Initial release",
      "Basic anime browsing functionality",
      "Trending anime section",
      "User watchlist feature",
      "Basic search capabilities"
    ]
  }
];

const SimpleDialog = () => {
  const [announcementVisible, setAnnouncementVisible] = useState(true);
  const location = useLocation();

  // Only show on homepage
  const isHomePage = location.pathname === "/";

  // Check localStorage on component mount
  useEffect(() => {
    // If not on homepage, don't bother checking
    if (!isHomePage) return;

    // Check if announcement was previously dismissed
    const isDismissed = localStorage.getItem("betaAnnouncementDismissed") === "true";
    if (isDismissed) {
      setAnnouncementVisible(false);
    }
  }, [isHomePage]);

  // Don't render if not on homepage or if dismissed
  if (!isHomePage || !announcementVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-black border-2 border-amber-500 p-6 rounded-lg max-w-md w-full mx-4">
        <div className="flex items-center gap-2 mb-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-amber-500"
          >
            <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path>
            <path d="M12 9v4"></path>
            <path d="M12 17h.01"></path>
          </svg>
          <h2 className="text-xl font-bold">Beta Version</h2>
        </div>

        <p className="text-gray-200 mb-6">
          This site is currently in Beta stage. You may encounter bugs and glitches while using the platform.
          We appreciate your patience and feedback as we work to improve your experience.
        </p>

        <div className="flex flex-col sm:flex-row gap-2 justify-end">
          <a
            href="/changelog"
            className="px-4 py-2 bg-white text-black font-medium rounded hover:bg-gray-100 inline-block text-center"
            onClick={() => {
              // Save dismissal to localStorage
              localStorage.setItem("betaAnnouncementDismissed", "true");
              setAnnouncementVisible(false);
            }}
          >
            View Changelog
          </a>
          <button
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            onClick={() => {
              // Save dismissal to localStorage
              localStorage.setItem("betaAnnouncementDismissed", "true");
              setAnnouncementVisible(false);
            }}
          >
            Got it!
          </button>
        </div>
      </div>
    </div>
  );
};

export default SimpleDialog;
