import { useState, useEffect } from "react";
import { getRecentEpisodes } from "@/api/aninow";
import useFetch from "@/hooks/useFetch";
import Image from "@/components/ui/Image";
import { Link } from "react-router-dom";
import { Play, Clock, CalendarClock, Filter, Search } from "lucide-react";
import AgeRating from "@/components/ui/AgeRating";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";


const RecentEpisodesPage = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // Fetch recent episodes
  const { data: recentEpisodes, isLoading } = useFetch({
    queryKey: ["recent-episodes", page],
    queryFn: () => getRecentEpisodes({ page }),
    onSuccess: (data) => {
      if (data.length < 20) {
        setHasMore(false);
      }
    },
    placeholderData: [],
  });

  // Filter and search episodes
  const filteredEpisodes = recentEpisodes.filter((episode) => {
    // Handle title which might be an object with english/romaji properties or a string
    const title = typeof episode?.title === 'string'
      ? episode?.title
      : (episode?.title?.english || episode?.title?.romaji || '');

    const matchesSearch = title.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = filterType === "all" || episode?.type === filterType;
    return matchesSearch && matchesType;
  });

  // Load more episodes
  const loadMore = () => {
    if (hasMore && !isLoading) {
      setPage((prev) => prev + 1);
    }
  };

  return (
    <>
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex flex-col gap-6 mb-8">
          <div className="flex items-center gap-2">
            <div className="bg-black/40 backdrop-blur-sm p-2 rounded-md border border-white/10">
              <CalendarClock size={20} className="text-white" />
            </div>
            <h1 className="text-2xl md:text-3xl font-bold">Recent Episodes</h1>
          </div>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-grow">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="Search episodes..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-black/40 backdrop-blur-sm border-white/10 focus-visible:ring-white/20"
              />
            </div>
            <div className="flex items-center gap-2">
              <Filter size={18} className="text-gray-400" />
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-[180px] bg-black/40 backdrop-blur-sm border-white/10">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent className="bg-black/80 backdrop-blur-md border-white/10">
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="TV">TV</SelectItem>
                  <SelectItem value="MOVIE">Movie</SelectItem>
                  <SelectItem value="OVA">OVA</SelectItem>
                  <SelectItem value="ONA">ONA</SelectItem>
                  <SelectItem value="SPECIAL">Special</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Episodes Grid */}
        {isLoading && page === 1 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {Array.from({ length: 20 }).map((_, index) => (
              <div key={index} className="aspect-video bg-white/5 animate-pulse rounded-xl border border-white/10"></div>
            ))}
          </div>
        ) : filteredEpisodes.length > 0 ? (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {filteredEpisodes.map((episode) => (
                <EpisodeCard key={`${episode.id}-${episode.currentEpisode}`} episode={episode} />
              ))}
            </div>

            {/* Load More Button */}
            {hasMore && (
              <div className="flex justify-center mt-8">
                <Button
                  onClick={loadMore}
                  disabled={isLoading}
                  className="bg-black/40 backdrop-blur-sm hover:bg-black/60 text-white border border-white/10 hover:border-white/20"
                >
                  {isLoading ? "Loading..." : "Load More"}
                </Button>
              </div>
            )}
          </>
        ) : (
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <div className="text-xl font-medium mb-2">No episodes found</div>
            <p className="text-gray-400 mb-4">Try adjusting your search or filters</p>
            <Button
              onClick={() => {
                setSearchQuery("");
                setFilterType("all");
              }}
              className="bg-black/40 backdrop-blur-sm hover:bg-black/60 text-white border border-white/10 hover:border-white/20"
            >
              Reset Filters
            </Button>
          </div>
        )}
      </div>
    </>
  );
};

const EpisodeCard = ({ episode }) => {
  return (
    <Link
      to={episode?.id ? `/watch/anime/${episode?.id}?ep=${episode?.currentEpisode}` : ""}
      className="group block relative overflow-hidden rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300 shadow-lg"
    >
      {/* Card Container */}
      <div className="relative aspect-video w-full">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src={episode?.coverImage}
            quality="high"
            className="object-cover w-full h-full group-hover:scale-110 transition-all duration-500"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-black/20 backdrop-blur-[2px] opacity-70 group-hover:opacity-80 transition-opacity duration-300"></div>
        </div>

        {/* Content */}
        <div className="absolute inset-0 flex flex-col justify-between p-3">
          {/* Top Section */}
          <div className="flex justify-between items-start">
            {/* Episode Number */}
            <div className="bg-black/60 backdrop-blur-sm text-white px-2 py-1 rounded-md text-sm font-bold border border-white/10 flex items-center gap-1">
              <span>EP {episode?.currentEpisode}</span>
            </div>

            {/* Date */}
            <div className="bg-black/60 backdrop-blur-sm text-white px-2 py-1 rounded-md text-xs border border-white/10 flex items-center gap-1">
              <Clock size={10} />
              <span>{new Date(episode?.updatedAt).toLocaleDateString()}</span>
            </div>
          </div>

          {/* Bottom Section */}
          <div>
            {/* Title */}
            <h3 className="text-base font-bold text-white line-clamp-1 mb-1">
              {episode?.title?.english || episode?.title?.romaji || episode?.title}
            </h3>

            {/* Status Indicator */}
            {episode?.status && (
              <div className="flex items-center gap-1.5 mb-1">
                <span className={`relative flex h-2 w-2 ${
                  episode?.status === "RELEASING" ? "animate-pulse bg-green-500" :
                  episode?.status === "FINISHED" ? "bg-blue-500" :
                  episode?.status === "NOT_YET_RELEASED" ? "bg-yellow-500" :
                  episode?.status === "CANCELLED" ? "bg-red-500" :
                  "bg-gray-500"
                } rounded-full`}>
                  {episode?.status === "RELEASING" && (
                    <span className="absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75 animate-ping"></span>
                  )}
                </span>
                <span className="text-xs text-white/80">
                  {episode?.status === "RELEASING" ? "Airing" :
                   episode?.status === "FINISHED" ? "Completed" :
                   episode?.status === "NOT_YET_RELEASED" ? "Coming Soon" :
                   episode?.status === "CANCELLED" ? "Cancelled" :
                   "Unknown"}
                </span>
              </div>
            )}

            {/* Genres */}
            {episode?.genres && episode?.genres.length > 0 && (
              <div className="flex flex-wrap gap-1">
                <span className="text-xs px-2 py-0.5 bg-black/60 backdrop-blur-sm text-white rounded-full border border-white/10">
                  {episode?.genres[0]}
                </span>
                {episode?.genres[1] && (
                  <span className="text-xs px-2 py-0.5 bg-black/60 backdrop-blur-sm text-white rounded-full border border-white/10">
                    {episode?.genres[1]}
                  </span>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Play Button - Appears on hover */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="bg-white text-black font-bold px-4 py-2 rounded-full flex items-center gap-2 transform-gpu scale-90 group-hover:scale-100 transition-all duration-300 shadow-[0_0_15px_rgba(255,255,255,0.5)]">
            <Play fill="black" size={16} />
            <span>Watch Now</span>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default RecentEpisodesPage;
