import React from 'react';
import { AlertCircle } from 'lucide-react';

/**
 * AgeRating component for displaying age rating tags on anime covers
 * @param {Object} props
 * @param {boolean} props.isAdult - Whether the content is for adults only (18+)
 * @param {Array} props.genres - Array of genre objects with name property
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.position - Position of the tag (default: 'top-2 left-2')
 * @param {boolean} props.compact - Whether to use compact mode for small screens
 * @returns {JSX.Element}
 */
const AgeRating = ({
  isAdult,
  genres = [],
  className = "",
  position = "top-2 left-2",
  compact = false
}) => {
  // Determine rating based on available data
  let rating = "PG";
  let color = "bg-green-600";

  // Check for adult content first (highest priority)
  if (isAdult) {
    rating = "18+";
    color = "bg-red-600";
  }
  // Check for mature content based on genres
  else if (
    genres?.some(genre =>
      typeof genre === 'string'
        ? ['Horror', 'Ecchi', 'Thriller'].includes(genre)
        : ['Horror', 'Ecchi', 'Thriller'].includes(genre.name)
    )
  ) {
    rating = "17+";
    color = "bg-orange-600";
  }
  // Check for teen content
  else if (
    genres?.some(genre =>
      typeof genre === 'string'
        ? ['Action', 'Violence', 'Drama'].includes(genre)
        : ['Action', 'Violence', 'Drama'].includes(genre.name)
    )
  ) {
    rating = "13+";
    color = "bg-yellow-600";
  }

  // Base classes for the tag
  const baseClasses = `absolute ${position} ${color} text-white font-bold rounded flex items-center z-10`;

  // Responsive classes based on compact mode
  const responsiveClasses = compact
    ? "text-[8px] px-1 py-0.5 gap-0.5" // Smaller for mobile/compact views
    : "text-[10px] xs:text-xs px-1 py-0.5 xs:px-1.5 xs:py-0.5 gap-0.5 xs:gap-1"; // Responsive sizing

  return (
    <div className={`${baseClasses} ${responsiveClasses} ${className}`}>
      {isAdult && <AlertCircle size={compact ? 8 : 10} className="xs:w-3 xs:h-3" />}
      {rating}
    </div>
  );
};

export default AgeRating;
