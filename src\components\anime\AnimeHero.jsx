import useFetch from "@/hooks/useFetch";
import { getTrendingAnime } from "@/api/anilist";
import { getAnimeLogo } from "@/api/animeLogo";
import Image from "../ui/Image";
import { PlayIcon, ChevronLeft, ChevronRight, Volume2, VolumeX } from "lucide-react";
import { Link } from "react-router-dom";
import { useState, useEffect, useCallback, useRef } from "react";
import AgeRating from "@/components/ui/AgeRating";

// Import Embla Carousel
import useEmblaCarousel from "embla-carousel-react";

const AnimeHero = () => {
  const [slide, setSlide] = useState(0);
  const [animeLogos, setAnimeLogos] = useState({});
  const [muted, setMuted] = useState(true);
  const [isScrolling, setIsScrolling] = useState(false);
  const videoRefs = useRef({});
  const fadeInterval = useRef(null);
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    align: "start",
    skipSnaps: false,
    draggable: true
  });

  const { data } = useFetch({
    key: ["trending-anime-hero"],
    fun: async () => {
      return (await getTrendingAnime()) || null;
    },
    placeholderData: [],
  });

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const scrollTo = useCallback((index) => {
    if (emblaApi) emblaApi.scrollTo(index);
  }, [emblaApi]);



  // Function to fade audio volume
  const fadeAudio = useCallback((videoElement, targetVolume, duration = 500, callback) => {
    if (!videoElement || videoElement.muted) return;

    // Clear any existing fade interval
    if (fadeInterval.current) {
      clearInterval(fadeInterval.current);
    }

    const startVolume = videoElement.volume;
    const volumeChange = targetVolume - startVolume;
    const steps = 20; // Number of steps for smooth fading
    const stepTime = duration / steps;
    let currentStep = 0;

    fadeInterval.current = setInterval(() => {
      currentStep++;
      const progress = currentStep / steps;
      videoElement.volume = startVolume + volumeChange * progress;

      if (currentStep >= steps) {
        clearInterval(fadeInterval.current);
        videoElement.volume = targetVolume;
        if (callback) callback();
      }
    }, stepTime);
  }, []);

  // Function to toggle video mute state with audio fading
  const toggleMute = useCallback(() => {
    const newMutedState = !muted;
    setMuted(newMutedState);

    // Apply audio fading for HTML5 videos
    const currentAnime = data?.[slide];
    if (currentAnime?.id && videoRefs.current[currentAnime.id]) {
      const video = videoRefs.current[currentAnime.id];

      if (newMutedState) {
        // Fade out before muting
        fadeAudio(video, 0, 300, () => {
          video.muted = true;
        });
      } else {
        // Unmute and fade in
        video.muted = false;
        video.volume = 0;
        fadeAudio(video, 1, 500);
      }
    }
  }, [muted, data, slide, fadeAudio]);

  // Function to handle video loading
  const handleVideoLoad = useCallback((animeId) => {
    // If this is the current slide and not scrolling, play the video
    if (data && data[slide]?.id === animeId && !isScrolling) {
      const video = videoRefs.current[animeId];
      if (video) {
        video.play().catch(e => console.log("Video play error:", e));
      }
    }
  }, [data, slide, isScrolling]);

  // Function to create YouTube embed URL from trailer data
  const getTrailerUrl = useCallback((trailer, isMuted = true) => {
    if (!trailer || !trailer.id) return null;

    if (trailer.site === "youtube") {
      // Add parameters for autoplay, no controls, looping, and dynamic mute state
      // controls=0 hides all controls
      // disablekb=1 disables keyboard controls
      // iv_load_policy=3 hides annotations
      const baseUrl = trailer.embedUrl || `https://www.youtube.com/embed/${trailer.id}`;
      return `${baseUrl}?autoplay=1&controls=0&disablekb=1&iv_load_policy=3&loop=1&mute=${isMuted ? '1' : '0'}&playlist=${trailer.id}&modestbranding=1&showinfo=0&rel=0&enablejsapi=1`;
    }

    return null;
  }, []);

  // Fetch anime logos when data changes
  useEffect(() => {
    const fetchAnimeLogos = async () => {
      if (!data || !data.length) return;

      const logoPromises = data.map(async (anime) => {
        if (!anime?.id) return null;

        try {
          const logoData = await getAnimeLogo(anime.id);
          return { id: anime.id, logoData };
        } catch (error) {
          console.error(`Error fetching logo for anime ID ${anime.id}:`, error);
          return { id: anime.id, logoData: null };
        }
      });

      const logoResults = await Promise.all(logoPromises);

      // Create a map of anime IDs to their logo data
      const logoMap = {};
      logoResults.forEach(result => {
        if (result && result.id) {
          logoMap[result.id] = result.logoData;
        }
      });

      console.log("Anime logos map:", logoMap);
      setAnimeLogos(logoMap);
    };

    fetchAnimeLogos();
  }, [data]);



  // Create ref to track scroll position
  const lastScrollY = useRef(0);

  // Set up scroll detection to pause videos when scrolling
  useEffect(() => {
    // Simple function to pause all videos
    const pauseAllVideos = () => {
      // Pause HTML5 videos
      Object.keys(videoRefs.current).forEach(id => {
        const video = videoRefs.current[id];
        if (video && video.tagName === 'VIDEO' && !video.paused) {
          video.pause();
        }
      });

      // Pause YouTube videos by updating iframe src
      if (data && data[slide]?.trailer?.site === "youtube") {
        const iframe = document.querySelector(`iframe[src*="youtube.com/embed"]`);
        if (iframe) {
          const currentSrc = iframe.src;
          if (currentSrc.includes('autoplay=1')) {
            iframe.src = currentSrc.replace('autoplay=1', 'autoplay=0');
          }
        }
      }
    };

    // Simple function to play current video
    const playCurrentVideo = () => {
      if (!data || !data[slide]) return;

      const currentId = data[slide].id;
      const video = videoRefs.current[currentId];

      // Play HTML5 video
      if (video && video.tagName === 'VIDEO') {
        video.play().catch(e => console.log("Video play error:", e));
      }

      // Play YouTube video
      if (data[slide].trailer?.site === "youtube") {
        const iframe = document.querySelector(`iframe[src*="youtube.com/embed"]`);
        if (iframe) {
          const currentSrc = iframe.src;
          if (currentSrc.includes('autoplay=0')) {
            iframe.src = currentSrc.replace('autoplay=0', 'autoplay=1');
          }
        }
      }
    };

    // Direct scroll handler - simpler and more reliable
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // If scrolled more than 100px, pause all videos
      if (currentScrollY > 100) {
        pauseAllVideos();
        setIsScrolling(true);
      } else {
        // If scrolled back to top, resume playing
        playCurrentVideo();
        setIsScrolling(false);
      }

      lastScrollY.current = currentScrollY;
    };

    // Add scroll listener
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Initial check
    setTimeout(handleScroll, 500);

    // Clean up
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [slide, data, muted]);

  // Set up autoplay and slide change handling
  useEffect(() => {
    if (!emblaApi) return;

    // Set up autoplay
    const autoplayInterval = setInterval(() => {
      if (emblaApi.canScrollNext()) {
        emblaApi.scrollNext();
      } else {
        emblaApi.scrollTo(0);
      }
    }, 8000); // Longer interval to allow trailer viewing

    // Set up slide change handler
    const onSelect = () => {
      const newSlide = emblaApi.selectedScrollSnap();

      // Fade out audio and pause all videos when changing slides
      Object.keys(videoRefs.current).forEach(id => {
        const video = videoRefs.current[id];
        if (video) {
          if (!video.muted && video.volume > 0) {
            // Fade out audio quickly before pausing
            fadeAudio(video, 0, 150, () => {
              video.pause();
            });
          } else {
            video.pause();
          }
        }
      });

      setSlide(newSlide);
    };

    emblaApi.on("select", onSelect);

    // Initial slide
    setSlide(emblaApi.selectedScrollSnap());

    return () => {
      clearInterval(autoplayInterval);
      emblaApi.off("select", onSelect);
    };
  }, [emblaApi, fadeAudio]);

  // Handle video playback when slide changes
  useEffect(() => {
    if (!data || data.length === 0) return;

    // Pause all videos first with audio fade
    Object.keys(videoRefs.current).forEach(id => {
      const video = videoRefs.current[id];
      if (video) {
        if (!video.muted && video.volume > 0) {
          fadeAudio(video, 0, 150, () => {
            video.pause();
          });
        } else {
          video.pause();
        }
      }
    });

    // Play the current slide's video if it exists and we're not scrolling
    const currentAnime = data[slide];
    if (currentAnime?.id && videoRefs.current[currentAnime.id] && !isScrolling) {
      const video = videoRefs.current[currentAnime.id];
      if (video) {
        video.play().catch(e => console.log("Video play error:", e));

        // Fade in audio if not muted
        if (!muted && video) {
          video.volume = 0;
          fadeAudio(video, 1, 500);
        }
      }
    }
  }, [slide, data, isScrolling, muted, fadeAudio]);

  return (
    <div className="w-full flex flex-col gap-2 relative">
      <div className="relative">
        <div ref={emblaRef} className="w-full overflow-hidden rounded-xl lg:rounded-2xl aspect-[1.2/1] xs:aspect-[1.5/1] sm:aspect-[1.8/1] md:aspect-[2.2/1] bg-black relative">
          <div className="flex w-full h-full">
            {data?.length
              ? data?.map((anime) => {
                  return (
                    <div
                      key={anime?.id}
                      className="flex w-full h-full shrink-0 relative overflow-hidden"
                    >
                      {/* Lighter gradient overlays for better visibility */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black via-black/60 to-transparent z-10"></div>
                      <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/50 to-transparent z-10"></div>
                      <div className="absolute inset-0 bg-black/20 z-10"></div>

                      {/* Trailer Video or Banner Image */}
                      <div className="absolute inset-0 w-full h-full">
                        {anime?.trailer && slide === data.indexOf(anime) ? (
                          <div className="w-full h-full relative">
                            {/* HTML5 Video for trailers with direct URLs */}
                            {anime.trailer.url && anime.trailer.url.endsWith('.mp4') && (
                              <video
                                ref={el => videoRefs.current[anime.id] = el}
                                src={anime.trailer.url}
                                className="w-full h-full object-cover"
                                autoPlay
                                loop
                                playsInline
                                muted={muted}
                                onLoadedData={() => handleVideoLoad(anime.id)}
                              />
                            )}

                            {/* YouTube iframe for YouTube trailers - Improved responsiveness for all devices */}
                            {anime.trailer.site === "youtube" && (
                              <div className="absolute inset-0 w-full h-full overflow-hidden youtube-container">
                                <iframe
                                  key={`${anime.trailer.id}-${muted ? 'muted' : 'unmuted'}`}
                                  src={getTrailerUrl(anime.trailer, muted)}
                                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                  allowFullScreen
                                  title={`${anime.title} trailer`}
                                  className="youtube-iframe"
                                  style={{
                                    border: 0,
                                    pointerEvents: 'none'
                                  }}
                                ></iframe>
                              </div>
                            )}

                            {/* Mute/Unmute button */}
                            <button
                              onClick={toggleMute}
                              className="absolute bottom-4 right-4 z-30 bg-black/50 p-2 rounded-full hover:bg-black/70 transition-colors"
                            >
                              {muted ? <VolumeX size={20} /> : <Volume2 size={20} />}
                            </button>
                          </div>
                        ) : (
                          <Image
                            src={anime?.images?.bannerLarge || anime?.images?.banner || anime?.images?.cover}
                            quality="high"
                            className="w-full h-full object-cover"
                          />
                        )}
                      </div>

                      <AgeRating
                        isAdult={anime?.isAdult}
                        genres={anime?.genres}
                        position="top-4 right-4 sm:top-6 sm:right-6"
                        className="rounded-md z-20"
                        compact={false}
                      />

                      {/* Content Container - Left side (adjusted for centered poster) */}
                      <div className="absolute z-20 flex flex-col justify-center h-full left-0 p-4 md:p-8 lg:p-10 w-full md:w-[45%] lg:w-[40%]">
                        {/* Trending Tag */}
                        <div className="flex items-center gap-2 mb-2 md:mb-3">
                          <div className="bg-red-600 text-white text-xs font-bold px-2 py-1 rounded tracking-wider">
                            #1 TRENDING NOW
                          </div>
                        </div>

                        {/* Anime Logo or Title */}
                        {animeLogos[anime?.id]?.full_url ? (
                          <div className="h-16 md:h-20 lg:h-24 mb-2 md:mb-3 flex items-center">
                            <img
                              src={animeLogos[anime?.id]?.full_url}
                              alt={anime?.title}
                              className="max-h-full max-w-full object-contain"
                              style={{ filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.8))' }}
                            />
                          </div>
                        ) : (
                          <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-2 md:mb-3 line-clamp-2"
                              style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.8)' }}>
                            {anime?.title}
                          </h1>
                        )}

                        {/* Debug info - remove in production */}
                        <div className="hidden">
                          Logo available: {animeLogos[anime?.id] ? 'Yes' : 'No'},
                          Anime ID: {anime?.id}
                        </div>

                        {/* Metadata */}
                        <div className="flex flex-wrap items-center gap-2 text-sm md:text-base text-gray-200 mb-2 md:mb-3">
                          <span>{anime?.release_date}</span>
                          <span className="inline-block w-1 h-1 bg-gray-400 rounded-full"></span>
                          <span>{anime?.episodes} Episodes</span>
                          <span className="inline-block w-1 h-1 bg-gray-400 rounded-full"></span>
                          <span>{anime?.status || "RELEASING"}</span>
                        </div>

                        {/* Description */}
                        <p className="text-sm md:text-base text-gray-300 line-clamp-2 md:line-clamp-3 mb-4 md:mb-6 max-w-[90%]">
                          {anime?.description?.replace(/<[^>]*>/g, "")}
                        </p>

                        {/* Action Buttons */}
                        <div className="flex items-center gap-3">
                          <Link
                            to={`/anime/${anime?.id}`}
                            className="bg-white text-black px-4 py-2 md:px-5 md:py-2.5 rounded-lg flex items-center gap-2 hover:bg-white/90 text-sm md:text-base font-medium transition-colors"
                          >
                            <PlayIcon size={18} /> Watch Now
                          </Link>
                        </div>
                      </div>

                      {/* Anime Cover - Positioned even more to the right */}
                      <div className="hidden md:block absolute right-[5%] lg:right-[8%] top-1/2 -translate-y-1/2 z-20 w-[200px] lg:w-[240px] aspect-[1/1.45]">
                        <div className="w-full h-full rounded-lg overflow-hidden shadow-2xl transform transition-transform hover:scale-105">
                          <Image
                            src={anime?.images?.coverLarge || anime?.images?.coverMedium || anime?.images?.coverSmall}
                            quality="high"
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </div>
                    </div>
                  );
                })
              : Array.from({ length: 1 }).map((_, i) => (
                  <div
                    key={i}
                    className="flex w-full h-full shrink-0 bg-white/5 animate-pulse"
                  ></div>
                ))}
          </div>
        </div>

        {/* Navigation buttons */}
        <button
          className="absolute left-3 top-1/2 -translate-y-1/2 bg-black/60 hover:bg-black/80 text-white p-2 sm:p-3 rounded-full z-20 shadow-lg transition-colors"
          onClick={scrollPrev}
          aria-label="Previous slide"
        >
          <ChevronLeft size={20} className="sm:w-6 sm:h-6" />
        </button>
        <button
          className="absolute right-3 top-1/2 -translate-y-1/2 bg-black/60 hover:bg-black/80 text-white p-2 sm:p-3 rounded-full z-20 shadow-lg transition-colors"
          onClick={scrollNext}
          aria-label="Next slide"
        >
          <ChevronRight size={20} className="sm:w-6 sm:h-6" />
        </button>
      </div>

      {/* Redesigned Indicator dots - with tighter spacing for mobile */}
      {data?.length > 0 && emblaApi && (
        <div className="flex justify-center mt-3 sm:mt-4 relative">
          <div className="flex items-center gap-[2px] sm:gap-1 px-2 sm:px-4 py-1 sm:py-1.5 bg-black/40 backdrop-blur-sm rounded-full border border-white/10 max-w-full overflow-x-auto scrollbar-hide">
            {data.map((_, i) => (
              <button
                key={i}
                onClick={() => scrollTo(i)}
                className={`relative transition-all duration-300 ${
                  slide === i
                    ? "w-5 sm:w-8 h-1.5 sm:h-2 bg-gradient-to-r from-blue-500 to-purple-500"
                    : "w-1.5 sm:w-2 h-1.5 sm:h-2 bg-white/30 hover:bg-white/50"
                } rounded-full overflow-hidden mx-[1px] sm:mx-0 flex-shrink-0`}
                aria-label={`Go to slide ${i + 1}`}
              >
                {slide === i && (
                  <span className="absolute inset-0 bg-white/30 animate-pulse rounded-full"></span>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AnimeHero;
