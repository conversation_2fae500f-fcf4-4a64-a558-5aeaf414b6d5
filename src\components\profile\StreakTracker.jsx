import { useUserActivity } from "@/context/UserActivityContext";
import { Flame, Award, Gift } from "lucide-react";

const StreakTracker = () => {
  const { streak, longestStreak, rank, getRecentVisits } = useUserActivity();
  const recentVisits = getRecentVisits();

  // Calculate next streak milestone
  const getNextMilestone = () => {
    const milestones = [3, 7, 14, 30, 60, 90, 180, 365];
    for (const milestone of milestones) {
      if (streak < milestone) {
        return {
          days: milestone,
          remaining: milestone - streak
        };
      }
    }
    return { days: 365, remaining: 365 - (streak % 365) };
  };

  const nextMilestone = getNextMilestone();

  // Calculate streak rewards
  const getStreakReward = () => {
    if (streak < 3) return "15 XP";
    if (streak < 7) return "35 XP";
    if (streak < 14) return "70 XP";
    if (streak < 30) return "150 XP";
    if (streak < 60) return "300 XP";
    if (streak < 90) return "450 XP";
    if (streak < 180) return "900 XP";
    return "1825 XP";
  };

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm text-white/80 flex items-center gap-1">
          <Flame size={14} className="text-orange-500" />
          Current Streak
        </h3>
        <span className="text-xs bg-orange-500/20 text-orange-400 px-2 py-0.5 rounded-full">
          {streak} days
        </span>
      </div>

      {/* Streak Flames */}
      <div className="flex justify-between items-center mb-4">
        {Array.from({ length: 10 }, (_, i) => i + 1).map((day) => (
          <div key={day} className="flex flex-col items-center">
            <div className={`w-6 h-6 flex items-center justify-center ${day <= streak ? 'text-orange-500' : 'text-gray-600'}`}>
              <Flame size={20} className={day <= streak ? 'fill-orange-500' : 'fill-gray-800'} />
            </div>
            <span className="text-xs mt-1">{day}</span>
            <span className="text-[10px] text-white/60">days</span>
          </div>
        ))}
      </div>

      {/* Next Milestone */}
      <div className="bg-black/30 rounded-lg p-3 mb-3">
        <div className="flex justify-between items-center mb-1">
          <div className="flex items-center gap-1">
            <Award size={14} className="text-yellow-500" />
            <h4 className="text-xs font-medium">Next Milestone</h4>
          </div>
          <span className="text-xs text-white/60">{nextMilestone.days} days</span>
        </div>
        <div className="w-full h-1.5 bg-black/50 rounded-full overflow-hidden">
          <div
            className="h-full bg-yellow-500 rounded-full"
            style={{ width: `${(streak / nextMilestone.days) * 100}%` }}
          ></div>
        </div>
        <div className="flex justify-between items-center mt-1">
          <span className="text-[10px] text-white/60">{nextMilestone.remaining} days remaining</span>
          <div className="flex items-center gap-1">
            <Gift size={12} className="text-purple-400" />
            <span className="text-[10px] text-purple-400">{getStreakReward()}</span>
          </div>
        </div>
      </div>

      {/* Streak Stats */}
      <div className="flex justify-between mt-4">
        <div className="text-center">
          <h4 className="text-xs text-white/60">Longest Streak</h4>
          <p className="text-lg font-bold">{longestStreak} days</p>
        </div>

        <div className="text-center">
          <h4 className="text-xs text-white/60">Current Rank</h4>
          <p className="text-lg font-bold">{rank}</p>
        </div>
      </div>
    </div>
  );
};

export default StreakTracker;
