import React, { memo } from 'react';
import { Link } from 'react-router-dom';
import Image from '../ui/Image';
import { Star } from 'lucide-react';
import AddToList from '../AddToList';
import AgeRating from '@/components/ui/AgeRating';

const FeaturedCollection = ({ title, data, description }) => {
  // Handle empty or loading state
  if (!data || data.length === 0) {
    return (
      <div className="w-full flex flex-col gap-4">
        <div className="flex flex-col gap-1">
          <div className="h-8 w-48 bg-white/10 animate-pulse rounded"></div>
          <div className="h-4 w-96 bg-white/5 animate-pulse rounded"></div>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="flex flex-col gap-2">
              <div className="aspect-[1/1.45] bg-white/10 animate-pulse rounded-xl"></div>
              <div className="h-4 w-full bg-white/5 animate-pulse rounded"></div>
              <div className="h-4 w-2/3 bg-white/5 animate-pulse rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col gap-4">
      <div className="flex flex-col gap-1">
        <h2 className="text-xl lg:text-2xl font-medium">{title}</h2>
        {description && (
          <p className="text-sm text-gray-400">{description}</p>
        )}
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-2 sm:gap-3">
        {data.slice(0, 16).map((anime, index) => (
          <div
            key={index}
            className="flex flex-col gap-1 group"
          >
            <div className="flex w-full aspect-[1/1.45] bg-white/10 lg:bg-white/5 rounded-lg relative overflow-hidden group-hover:scale-105 transition-transform duration-300 shadow-md">
              <Link
                to={anime?.id ? `/anime/${anime?.id}` : ""}
                className="size-full relative"
              >
                <Image
                  src={anime?.images?.coverLarge || anime?.images?.coverMedium || anime?.images?.coverSmall}
                  quality="high"
                  className="!object-cover !w-full !h-full"
                  loading="lazy"
                />
                <AgeRating isAdult={anime?.isAdult} genres={anime?.genres} position="top-1 left-1" compact={true} />
              </Link>
              <span className="flex flex-col gap-1 absolute items-end text-[10px] right-1 top-1 z-10">
                {Number(anime?.rating) > 0 && (
                  <span className="bg-black/80 p-[.15rem] px-1 gap-0.5 rounded-md flex items-center shadow-lg">
                    <Star fill="gold" color="gold" size={10} />
                    {Number(anime?.rating)?.toFixed(1) || "n/a"}
                  </span>
                )}
                {/* Removed AddToList */}
              </span>
            </div>
            <Link
              to={anime?.id ? `/anime/${anime?.id}` : ""}
              className="flex w-full flex-col px-0.5 mt-1"
            >
              <div className="flex w-full !line-clamp-1 text-[11px] sm:text-xs font-medium !leading-tight tracking-wider">
                {anime?.title}
              </div>
              <div className="flex w-full text-[9px] sm:text-[10px] text-gray-400">
                <span className="uppercase">{anime?.type}</span>
                {anime?.release_date && (
                  <>
                    <span className="mx-1">•</span>
                    <span>{anime?.release_date?.slice(-4)}</span>
                  </>
                )}
              </div>
            </Link>
          </div>
        ))}
      </div>

      {/* View All Link */}
      <div className="flex justify-end">
        <Link
          to="/explore"
          className="text-sm text-primary hover:text-primary/80 transition-colors"
        >
          View All
        </Link>
      </div>
    </div>
  );
};

export default memo(FeaturedCollection);
