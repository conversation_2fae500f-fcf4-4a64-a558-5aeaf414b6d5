import { useEffect, useMemo, useState, useRef } from "react";
import { Link, useSearchParams } from "react-router-dom";
import { debounce } from "lodash";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { exploreAnime } from "@/api/anilist";
import useFetch from "@/hooks/useFetch";
import { useTitlePreference, TITLE_PREFERENCES } from "@/context/TitlePreferenceContext";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Loader2,
  Star,
  Trash2,
  Search,
  X,
  Filter,
  SlidersHorizontal,
} from "lucide-react";
import Image from "@/components/ui/Image";
import AgeRating from "@/components/ui/AgeRating";

// Anime filter options
const anime_options = {
  genres: [
    { name: "Any", value: "any" },
    { name: "Action", value: "Action" },
    { name: "Adventure", value: "Adventure" },
    { name: "Comedy", value: "Comedy" },
    { name: "Drama", value: "Drama" },
    { name: "Fantasy", value: "Fantasy" },
    { name: "Horror", value: "Horror" },
    { name: "Mystery", value: "Mystery" },
    { name: "Romance", value: "Romance" },
    { name: "Sci-Fi", value: "Sci-Fi" },
    { name: "Slice of Life", value: "Slice of Life" },
    { name: "Sports", value: "Sports" },
    { name: "Supernatural", value: "Supernatural" },
    { name: "Thriller", value: "Thriller" },
  ],
  studios: [
    { name: "Any", value: "any" },
    { name: "Madhouse", value: "11" },
    { name: "Kyoto Animation", value: "2" },
    { name: "Studio Ghibli", value: "21" },
    { name: "Bones", value: "4" },
    { name: "Wit Studio", value: "858" },
    { name: "MAPPA", value: "569" },
    { name: "Toei Animation", value: "18" },
    { name: "A-1 Pictures", value: "561" },
    { name: "Ufotable", value: "43" },
    { name: "Production I.G", value: "10" },
    { name: "Sunrise", value: "14" },
    { name: "Trigger", value: "803" },
    { name: "J.C.Staff", value: "7" },
    { name: "P.A. Works", value: "132" },
    { name: "Studio Pierrot", value: "1" },
    { name: "CloverWorks", value: "1023" },
    { name: "Shaft", value: "44" },
  ],
  years: [
    { name: "Any", value: "any" },
    { name: "2024", value: "2024" },
    { name: "2023", value: "2023" },
    { name: "2022", value: "2022" },
    { name: "2021", value: "2021" },
    { name: "2020", value: "2020" },
    { name: "2019", value: "2019" },
    { name: "2018", value: "2018" },
    { name: "2017", value: "2017" },
    { name: "2016", value: "2016" },
    { name: "2015", value: "2015" },
    { name: "2010-2014", value: "2010" },
    { name: "2000-2009", value: "2000" },
    { name: "1990-1999", value: "1990" },
    { name: "1980-1989", value: "1980" },
  ],
  sorts: [
    { name: "Popularity", value: "POPULARITY_DESC" },
    { name: "Trending", value: "TRENDING_DESC" },
    { name: "Average Score", value: "SCORE_DESC" },
    { name: "Favorites", value: "FAVOURITES_DESC" },
    { name: "Date (Newest)", value: "START_DATE_DESC" },
    { name: "Date (Oldest)", value: "START_DATE" },
  ],
};

const AnimeExploreRedesign = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [body, setBody] = useState({});
  const [searchQuery, setSearchQuery] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  // No view mode toggle anymore - always grid view
  const searchInputRef = useRef(null);
  const { titlePreference } = useTitlePreference();

  const genre = searchParams.get("genre") || "any";
  const year = searchParams.get("year") || "any";
  const studio = searchParams.get("studio") || "any";
  const sort = searchParams.get("sort") || "POPULARITY_DESC";
  const page = parseInt(searchParams.get("page") || 1);
  const query = searchParams.get("query") || "";

  const { data, isLoading } = useFetch({
    key: [`anime-explore${JSON.stringify(body)}`],
    fun: async () => await exploreAnime(body),
  });

  const filters = useMemo(
    () => [
      {
        name: "GENRE",
        options: anime_options.genres,
      },
      { name: "STUDIO", options: anime_options.studios },
      { name: "YEAR", options: anime_options.years },
      {
        name: "SORT",
        options: anime_options.sorts,
      },
    ],
    []
  );

  const updateBody = debounce(() => {
    setBody({
      page,
      genre,
      year,
      studio,
      sort,
      query,
    });
  }, 500);

  const { results, pageInfo } = useMemo(
    () => ({
      results: data?.results || [],
      pageInfo: data?.pageInfo || {
        total: 0,
        currentPage: 1,
        lastPage: 1,
        hasNextPage: false,
      },
    }),
    [data]
  );

  // Initialize search query from URL
  useEffect(() => {
    setSearchQuery(query);
  }, [query]);

  useEffect(() => {
    updateBody();
    return () => updateBody.cancel();
  }, [genre, year, studio, sort, page, query]);

  // Handle search form submission
  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim() !== query) {
      setSearchParams((params) => {
        if (searchQuery.trim()) {
          params.set("query", searchQuery.trim());
        } else {
          params.delete("query");
        }
        params.set("page", 1); // Reset to first page on new search
        return params;
      });
    }
  };

  // Clear search
  const clearSearch = () => {
    setSearchQuery("");
    setSearchParams((params) => {
      params.delete("query");
      params.set("page", 1);
      return params;
    });
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  // Reset all filters
  const resetFilters = () => {
    setSearchParams((params) => {
      params.delete("query");
      params.delete("genre");
      params.delete("year");
      params.delete("studio");
      params.delete("sort");
      params.set("page", 1);
      return params;
    });
    setSearchQuery("");
  };

  const goToPage = (page) => {
    window.scrollTo({ top: 0, behavior: "smooth" });
    if (page >= 1 && page <= pageInfo.lastPage) {
      setSearchParams((params) => {
        params.set("page", page);
        return params;
      });
    }
  };



  return (
    <div className="w-full py-6 px-4 sm:px-6 flex flex-col gap-6">
      {/* Header with title and search */}
      <div className="flex flex-col md:flex-row justify-between gap-4 md:items-center">
        <h1 className="text-2xl md:text-3xl font-bold tracking-tight">
          Explore Anime
        </h1>

        {/* Search form */}
        <form onSubmit={handleSearch} className="flex items-center gap-2 w-full md:max-w-md">
          <div className="relative flex-1">
            <input
              ref={searchInputRef}
              type="text"
              placeholder="Search anime..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-white/5 border border-white/10 rounded-lg py-2.5 pl-10 pr-8 focus:outline-none focus:ring-1 focus:ring-white/30 transition-all"
            />
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-white/50" size={18} />
            {searchQuery && (
              <button
                type="button"
                onClick={clearSearch}
                className="absolute right-2 top-1/2 -translate-y-1/2 text-white/50 hover:text-white"
              >
                <X size={16} />
              </button>
            )}
          </div>
          <button
            type="submit"
            className="bg-white/10 hover:bg-white/20 text-white rounded-lg p-2.5 transition-colors"
          >
            <Search size={20} />
          </button>
        </form>
      </div>

      {/* Toolbar with filters toggle and view options */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
              showFilters ? 'bg-white/20 text-white' : 'bg-white/5 text-white/70 hover:bg-white/10 hover:text-white'
            }`}
          >
            <SlidersHorizontal size={18} />
            <span className="hidden sm:inline">Filters</span>
            {(genre !== "any" || studio !== "any" || year !== "any" || sort !== "POPULARITY_DESC") && (
              <span className="bg-white/20 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {(genre !== "any" ? 1 : 0) + (studio !== "any" ? 1 : 0) + (year !== "any" ? 1 : 0) + (sort !== "POPULARITY_DESC" ? 1 : 0)}
              </span>
            )}
          </button>

          {query && (
            <div className="flex items-center gap-1 bg-white/10 px-3 py-2 rounded-lg text-sm">
              <span className="hidden sm:inline">Search:</span> "{query}"
              <button
                onClick={clearSearch}
                className="ml-1 text-white/70 hover:text-white"
                title="Clear search"
              >
                <X size={14} />
              </button>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          <button
            onClick={resetFilters}
            className="bg-white/5 hover:bg-white/10 text-white/70 hover:text-white p-2 rounded-lg transition-colors"
            title="Reset all filters"
          >
            <Trash2 size={20} />
          </button>
        </div>
      </div>

      {/* Filters section - collapsible */}
      {showFilters && (
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4 animate-in fade-in-50 duration-300">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {filters.map(({ name, options }) => (
              <div key={name} className="flex flex-col gap-1.5">
                <label className="text-sm font-medium text-white/70">{name}</label>
                <Select
                  onValueChange={(v) => {
                    setSearchParams((params) => {
                      params.set(name.toLowerCase(), v);
                      params.set("page", 1);
                      return params;
                    });
                  }}
                  value={searchParams.get(name.toLowerCase()) || options[0]?.value}
                >
                  <SelectTrigger className="w-full bg-black/50 border border-white/10 rounded-lg text-white">
                    <SelectValue placeholder={options.find(o => o.value === (searchParams.get(name.toLowerCase()) || options[0]?.value))?.name} />
                  </SelectTrigger>
                  <SelectContent className="bg-black/90 backdrop-blur-md border border-white/10 rounded-lg text-white">
                    {options.map((o) => (
                      <SelectItem key={o.value} value={o.value} className="hover:bg-white/10">
                        {o.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Results count and status */}
      <div className="text-sm text-white/70">
        {!isLoading && (
          <span>
            Showing {results.length} results
            {pageInfo.total > 0 ? ` of ${pageInfo.total}` : ""}
            {genre !== "any" && ` in ${anime_options.genres.find(g => g.value === genre)?.name}`}
            {studio !== "any" && ` by ${anime_options.studios.find(s => s.value === studio)?.name}`}
            {year !== "any" && ` from ${anime_options.years.find(y => y.value === year)?.name}`}
          </span>
        )}
      </div>

      {/* Results grid - Matching trending page layout */}
      <div className="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {isLoading ? (
          <div className="col-span-full flex items-center justify-center py-20">
            <div className="flex flex-col items-center gap-2">
              <Loader2 className="animate-spin text-white" size={30} />
              <span className="text-white/70">Loading anime...</span>
            </div>
          </div>
        ) : results?.length ? (
          results.map((anime, index) => (
            <GridCard key={`anime-${anime?.id}`} anime={anime} titlePreference={titlePreference} index={index} />
          ))
        ) : (
          <div className="col-span-full flex flex-col items-center justify-center py-20 text-center">
            <div className="bg-white/5 rounded-full p-5 mb-4">
              <Search className="text-white/30" size={40} />
            </div>
            <h3 className="text-xl font-medium mb-2">No Results Found</h3>
            <p className="text-white/70 max-w-md mb-4">
              We couldn't find any anime matching your search criteria. Try adjusting your filters or search terms.
            </p>
            <button
              onClick={resetFilters}
              className="bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Reset Filters
            </button>
          </div>
        )}
      </div>

      {/* Pagination */}
      {!isLoading && results.length > 0 && (
        <div className="flex justify-center items-center mt-6">
          <div className="bg-white/5 flex rounded-lg overflow-hidden border border-white/10">
            <button
              onClick={() => goToPage(1)}
              disabled={page === 1}
              className={`p-2 w-10 flex items-center justify-center ${
                page === 1
                  ? "opacity-50 cursor-not-allowed"
                  : "hover:bg-white/10 text-white"
              }`}
              aria-label="First page"
            >
              <ChevronsLeft size={18} />
            </button>
            <button
              onClick={() => goToPage(page - 1)}
              disabled={page === 1}
              className={`p-2 w-10 flex items-center justify-center ${
                page === 1
                  ? "opacity-50 cursor-not-allowed"
                  : "hover:bg-white/10 text-white"
              }`}
              aria-label="Previous page"
            >
              <ChevronLeft size={18} />
            </button>

            <div className="bg-white/10 px-4 py-2 flex items-center justify-center min-w-[100px]">
              <span className="text-sm">
                Page {page} of {pageInfo.lastPage || 1}
              </span>
            </div>

            <button
              onClick={() => goToPage(page + 1)}
              disabled={page >= pageInfo.lastPage}
              className={`p-2 w-10 flex items-center justify-center ${
                page >= pageInfo.lastPage
                  ? "opacity-50 cursor-not-allowed"
                  : "hover:bg-white/10 text-white"
              }`}
              aria-label="Next page"
            >
              <ChevronRight size={18} />
            </button>
            <button
              onClick={() => goToPage(pageInfo.lastPage)}
              disabled={page >= pageInfo.lastPage}
              className={`p-2 w-10 flex items-center justify-center ${
                page >= pageInfo.lastPage
                  ? "opacity-50 cursor-not-allowed"
                  : "hover:bg-white/10 text-white"
              }`}
              aria-label="Last page"
            >
              <ChevronsRight size={18} />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Grid card component matching the trending page style exactly
const GridCard = ({ anime, titlePreference, index }) => (
  <div
    className="rounded-xl overflow-hidden relative group hover:bg-black/60 transition-all duration-500 border border-white/10 hover:border-white/20 shadow-lg hover:shadow-xl"
  >
    {/* Background banner image */}
    <div className="absolute inset-0 z-0 overflow-hidden">
      <Image
        src={anime?.images?.bannerLarge || anime?.images?.bannerSmall || anime?.images?.coverLarge}
        className="w-full h-full object-cover opacity-20 group-hover:opacity-30 transition-opacity duration-500 scale-110 group-hover:scale-125 transition-transform"
        quality="medium"
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/95 via-black/80 to-black/60"></div>
    </div>

    {/* Large faded rank number */}
    <div className="absolute -right-5 top-1/2 -translate-y-1/2 text-[150px] font-black text-white/5 group-hover:text-white/15 transition-all duration-500 z-0 select-none opacity-70 group-hover:opacity-100 group-hover:-translate-x-2">
      {index + 1}
    </div>

    <Link to={`/anime/${anime?.id}`} className="block relative z-10">
      <div className="flex p-4">
        {/* Anime poster */}
        <div className="w-24 h-32 sm:w-28 sm:h-36 flex-shrink-0 relative overflow-hidden rounded-md shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105">
          <Image
            src={anime?.images?.coverLarge || anime?.images?.coverMedium}
            className="w-full h-full object-cover"
            quality="high"
          />
          <AgeRating isAdult={anime?.isAdult} genres={anime?.genres} position="top-1 left-1" compact={true} />
        </div>

        {/* Anime details */}
        <div className="flex-1 pl-4 flex flex-col">
          {/* Rank number (small, visible one) */}
          <div className="absolute top-3 right-4 z-10">
            <div className="bg-black/40 backdrop-blur-sm px-2 py-1 rounded-lg text-2xl font-bold text-white shadow-md border border-white/10 group-hover:bg-black/60 group-hover:border-white/20 transition-all duration-300">
              #{index + 1}
            </div>
          </div>

          {/* Title */}
          <h3 className="text-base sm:text-lg font-semibold line-clamp-2 mb-2 pr-10 text-white drop-shadow-md group-hover:text-primary-100 transition-colors duration-300">
            {titlePreference === TITLE_PREFERENCES.ENGLISH
              ? (anime?.title || anime?.titleRomaji || "Unknown Title")
              : (anime?.titleRomaji || anime?.title || "Unknown Title")}
          </h3>

          {/* Stats */}
          <div className="flex flex-col gap-1.5 text-xs text-white/80 mt-auto">
            <div className="flex items-center">
              <span className="bg-white/10 backdrop-blur-sm px-2 py-0.5 rounded-sm mr-2 text-white/90 group-hover:bg-white/15 transition-colors duration-300">{anime?.type || "TV"}</span>
            </div>

            <div className="flex items-center gap-3 mt-1">
              {anime?.episodes && (
                <span className="flex items-center bg-black/30 px-2 py-0.5 rounded-sm">
                  <span className="mr-1">{anime.episodes}</span>
                  <span>Eps</span>
                </span>
              )}

              {Number(anime?.rating) > 0 && (
                <span className="flex items-center bg-black/30 px-2 py-0.5 rounded-sm">
                  <Star size={12} className="mr-1 text-yellow-400" fill="currentColor" />
                  <span>{Number(anime?.rating)?.toFixed(1)}</span>
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </Link>
  </div>
);



export default AnimeExploreRedesign;
