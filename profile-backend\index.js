import express from 'express';
import cors from 'cors';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import os from 'os';
import connectDB from './db/connection.js';
import userProfileRoutes from './routes/userProfileRoutes.js';

// Set NODE_ENV to development if not set
process.env.NODE_ENV = process.env.NODE_ENV || 'development';
console.log(`Running in ${process.env.NODE_ENV} mode`);

// ES modules don't have __dirname, so we need to create it
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3004; // Using a different port than the main backend

// Enable CORS with specific options to allow requests from any origin
app.use(cors({
  origin: '*', // Allow all origins
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'], // Allow these HTTP methods
  allowedHeaders: ['Content-Type', 'Authorization'], // Allow these headers
  credentials: true // Allow cookies
}));

// Parse JSON bodies
app.use(express.json());

// Connect to MongoDB with retry logic
let dbConnection = null;
const initializeDatabase = async () => {
  try {
    dbConnection = await connectDB();
    if (dbConnection) {
      console.log('MongoDB connected successfully');
    } else {
      console.log('MongoDB connection failed, will retry...');
      // Schedule a retry
      setTimeout(initializeDatabase, 5000);
    }
  } catch (err) {
    console.error('MongoDB connection error:', err);
    // Schedule a retry
    setTimeout(initializeDatabase, 5000);
  }
};

// Start the database connection process
initializeDatabase();

// Use routes
app.use('/api', userProfileRoutes);

// Add a simple test endpoint
app.get('/api/test', (_, res) => {
  res.json({ status: 'ok', message: 'Profile backend is running' });
});

// Function to get local IP addresses
const getLocalIPs = () => {
  const nets = os.networkInterfaces();
  const results = {};

  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (net.family === 'IPv4' && !net.internal) {
        if (!results[name]) {
          results[name] = [];
        }
        results[name].push(net.address);
      }
    }
  }
  return results;
};

// Start the server
app.listen(PORT, () => {
  console.log(`Profile backend running on port ${PORT}`);
  console.log(`Test the server with: http://localhost:${PORT}/api/test`);

  // Try to get and display local IP addresses
  try {
    const ips = getLocalIPs();
    console.log('Available on network at:');
    for (const [_, addresses] of Object.entries(ips)) {
      for (const addr of addresses) {
        console.log(`  http://${addr}:${PORT}/api/test`);
      }
    }
  } catch (error) {
    console.log('Could not determine network addresses');
  }
});
