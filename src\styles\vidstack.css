/* Custom styles for Vidstack player */

.media-player {
  --media-brand: rgb(59, 130, 246);
  --media-focus-ring-color: rgb(59, 130, 246);
  --media-control-background: rgba(0, 0, 0, 0.7);
  --media-control-hover-background: rgba(0, 0, 0, 0.8);
  --media-control-active-background: rgba(0, 0, 0, 0.9);
  --media-control-foreground: white;
  --media-control-hover-foreground: white;
  --media-control-active-foreground: white;
  --media-focus-ring-color: rgb(59, 130, 246);
  --media-tooltip-background: rgba(0, 0, 0, 0.9);
  --media-tooltip-foreground: white;
  --media-menu-background: rgba(0, 0, 0, 0.9);
  --media-menu-foreground: white;
  --media-menu-item-hover-background: rgba(59, 130, 246, 0.5);
  --media-menu-item-hover-foreground: white;
  --media-menu-item-active-background: rgb(59, 130, 246);
  --media-menu-item-active-foreground: white;
  --media-time-slider-track-background: rgba(255, 255, 255, 0.2);
  --media-time-slider-track-fill-background: rgb(59, 130, 246);
  --media-time-slider-thumb-background: white;
  --media-volume-slider-track-background: rgba(255, 255, 255, 0.2);
  --media-volume-slider-track-fill-background: rgb(59, 130, 246);
  --media-volume-slider-thumb-background: white;
  --media-buffering-indicator-color: rgb(59, 130, 246);
  --media-error-background: rgba(0, 0, 0, 0.7);
  --media-error-foreground: white;
  cursor: pointer; /* Always show pointer cursor over the player */
}

/* Improve control bar visibility */
.media-player .vds-control-bar {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  padding: 8px;
}

/* Make time slider more visible */
.media-player .vds-time-slider {
  height: 6px;
}

.media-player .vds-time-slider:hover {
  height: 8px;
}

/* Improve settings menu appearance */
.media-player .vds-menu {
  border-radius: 8px;
  overflow: hidden;
}

.media-player .vds-menu-items {
  padding: 8px;
}

/* Improve quality menu appearance */
.media-player .vds-quality-menu-item {
  padding: 8px 12px;
  border-radius: 4px;
}

/* Improve poster appearance */
.media-player .vds-poster {
  object-fit: cover;
}

/* Improve fullscreen button appearance */
.media-player .vds-fullscreen-button {
  margin-left: auto;
}

/* Only keep the button-specific styles */

/* Ensure custom buttons are always clickable */
.vds-media-player button[style*="pointer-events: auto"],
.media-player button[style*="pointer-events: auto"],
#player-container button[style*="pointer-events: auto"] {
  pointer-events: auto !important;
  z-index: 10000 !important;
}

/* Specific fix for SUB/DUB toggle button */
.vds-media-player button[class*="rounded-full"],
.media-player button[class*="rounded-full"] {
  pointer-events: auto !important;
  z-index: 10000 !important;
}
