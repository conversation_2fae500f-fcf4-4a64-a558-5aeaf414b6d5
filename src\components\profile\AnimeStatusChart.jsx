import { useState, useEffect } from "react";
import { useAniList } from "@/hooks/useAniList";

const AnimeStatusChart = () => {
  const { user, getUserAnimeList } = useAniList();
  const [statusData, setStatusData] = useState([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const fetchStatusData = async () => {
      if (!user?.id) return;
      
      try {
        setLoading(true);
        const lists = await getUserAnimeList();
        
        if (!lists || !lists.length) {
          setLoading(false);
          return;
        }
        
        // Initialize status counts
        const statusCounts = {
          CURRENT: 0,
          COMPLETED: 0,
          PLANNING: 0,
          PAUSED: 0,
          DROPPED: 0,
          REPEATING: 0
        };
        
        // Count anime by status
        lists.forEach(list => {
          if (list.status && statusCounts.hasOwnProperty(list.status)) {
            statusCounts[list.status] = list.entries?.length || 0;
          }
        });
        
        // Convert to array format for the chart
        const data = Object.entries(statusCounts).map(([status, count]) => ({
          status,
          count,
          color: getStatusColor(status),
          label: getStatusLabel(status)
        }));
        
        setStatusData(data);
      } catch (error) {
        console.error("Error fetching anime status data:", error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchStatusData();
  }, [user, getUserAnimeList]);
  
  // Calculate total for percentages
  const total = statusData.reduce((sum, item) => sum + item.count, 0);
  
  return (
    <div className="w-full">
      <h3 className="text-sm text-white/80 mb-4">Your Anime Status</h3>
      
      {loading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin w-6 h-6 border-2 border-white/20 border-t-white rounded-full"></div>
        </div>
      ) : statusData.length === 0 ? (
        <div className="text-center py-4 text-white/60 text-sm">
          No anime data available. Add anime to your list to see your status distribution.
        </div>
      ) : (
        <div className="space-y-4">
          {/* Pie chart visualization */}
          <div className="relative w-40 h-40 mx-auto">
            <svg viewBox="0 0 100 100" className="w-full h-full">
              {renderPieChart(statusData, total)}
              {/* Center circle for donut chart effect */}
              <circle cx="50" cy="50" r="30" fill="#111" />
              {/* Total count in center */}
              <text x="50" y="50" textAnchor="middle" dominantBaseline="middle" fill="white" fontSize="12" fontWeight="bold">
                {total}
              </text>
              <text x="50" y="62" textAnchor="middle" dominantBaseline="middle" fill="white" fontSize="6" opacity="0.7">
                TOTAL ANIME
              </text>
            </svg>
          </div>
          
          {/* Legend */}
          <div className="grid grid-cols-2 gap-2">
            {statusData.filter(item => item.count > 0).map((item, index) => (
              <div key={index} className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: item.color }}></div>
                <div className="flex items-center justify-between w-full">
                  <span className="text-xs text-white/80">{item.label}</span>
                  <span className="text-xs text-white/60">{item.count} ({Math.round((item.count / total) * 100)}%)</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Helper function to render pie chart segments
const renderPieChart = (data, total) => {
  if (total === 0) return null;
  
  let segments = [];
  let currentAngle = 0;
  
  data.forEach((item, index) => {
    if (item.count === 0) return;
    
    // Calculate segment angle
    const angle = (item.count / total) * 360;
    
    // Calculate SVG arc path
    const startX = 50 + 40 * Math.cos((currentAngle - 90) * Math.PI / 180);
    const startY = 50 + 40 * Math.sin((currentAngle - 90) * Math.PI / 180);
    const endX = 50 + 40 * Math.cos((currentAngle + angle - 90) * Math.PI / 180);
    const endY = 50 + 40 * Math.sin((currentAngle + angle - 90) * Math.PI / 180);
    
    // Determine if the arc should be drawn as a large arc (> 180 degrees)
    const largeArcFlag = angle > 180 ? 1 : 0;
    
    // Create SVG path
    const path = `
      M 50 50
      L ${startX} ${startY}
      A 40 40 0 ${largeArcFlag} 1 ${endX} ${endY}
      Z
    `;
    
    segments.push(
      <path 
        key={index}
        d={path}
        fill={item.color}
        stroke="#111"
        strokeWidth="1"
      />
    );
    
    currentAngle += angle;
  });
  
  return segments;
};

// Helper function to get color for each status
const getStatusColor = (status) => {
  const colors = {
    CURRENT: "#3498db",    // Blue
    COMPLETED: "#2ecc71",  // Green
    PLANNING: "#9b59b6",   // Purple
    PAUSED: "#f39c12",     // Orange
    DROPPED: "#e74c3c",    // Red
    REPEATING: "#1abc9c"   // Teal
  };
  
  return colors[status] || "#95a5a6";
};

// Helper function to get user-friendly label for each status
const getStatusLabel = (status) => {
  const labels = {
    CURRENT: "Watching",
    COMPLETED: "Completed",
    PLANNING: "Plan to Watch",
    PAUSED: "On Hold",
    DROPPED: "Dropped",
    REPEATING: "Rewatching"
  };
  
  return labels[status] || status;
};

export default AnimeStatusChart;
