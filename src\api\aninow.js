import axios from "axios";

// API endpoints
const EPISODES_API = 'https://papi.aninow.wtf/episodes/';
const SOURCE_API = 'https://papi.aninow.wtf/source';
const RECENT_API = 'https://papi.aninow.wtf/recent';

/**
 * Fetches episode data including images for an anime by its AniList ID
 * @param {number|string} anilistId - The AniList ID of the anime
 * @returns {Promise<Array>} - Array of episode objects with images
 */
export const getAnimeEpisodes = async (anilistId) => {
  try {
    const response = await axios.get(`${EPISODES_API}${anilistId}`);

    // Handle different response formats
    let episodesList = [];

    if (Array.isArray(response.data)) {
      episodesList = response.data;
    } else if (typeof response.data === 'object' && response.data.episodes) {
      episodesList = response.data.episodes;
    }

    return episodesList;
  } catch (error) {
    console.error("Error fetching episode data:", error);
    return [];
  }
};

/**
 * Fetches video source for an anime episode
 * @param {number|string} animeId - The AniList ID of the anime
 * @param {number|string} watchId - The watch ID of the episode
 * @param {number|string} episodeNumber - The episode number
 * @param {boolean} isDub - Whether to fetch dubbed version
 * @returns {Promise<Object>} - Object containing the m3u8 URL and skip information
 */

/**
 * Fetches recent episodes from the AniNow API
 * @returns {Promise<Array>} - Array of recent episode objects
 */
export const getRecentEpisodes = async () => {
  try {
    const response = await axios.get(RECENT_API);

    // Return the data array directly
    if (Array.isArray(response.data)) {
      return response.data;
    }

    // If the response is not an array, return an empty array
    console.error("Unexpected response format from recent episodes API:", response.data);
    return [];
  } catch (error) {
    console.error("Error fetching recent episodes:", error);
    return [];
  }
};

export const getAnimeSource = async (animeId, watchId, episodeNumber, isDub = false) => {
  try {
    const sourceUrl = `${SOURCE_API}?id=${animeId}&watchId=${watchId}&num=${episodeNumber}${isDub ? '&type=dub' : ''}`;
    console.log('Requesting source from:', sourceUrl);
    const response = await axios.get(sourceUrl);

    // Log the raw response for debugging
    console.log('Raw API response:', response.data);

    // Initialize result object with default values
    const result = {
      url: '',
      skipIntro: null,
      skipOutro: null,
      number: episodeNumber
    };

    // Handle different response formats
    if (typeof response.data === 'string') {
      // Old API format - direct m3u8 URL in response
      result.url = response.data;
    } else if (typeof response.data === 'object') {
      // New API format - JSON object with sources and skip info

      // Extract the m3u8 URL from the new API structure
      console.log('Analyzing response structure:', {
        hasSourcesProperty: 'sources' in response.data,
        sourcesType: typeof response.data.sources,
        hasSkipProperty: 'skip' in response.data,
        responseKeys: Object.keys(response.data)
      });

      // Based on the JSON example you provided:
      // {
      //   "sources": "https://papi.aninow.wtf/https://vault-05.padoruupado.ru/stream/05/08/04f7f5cb5c20bf1834d37b22d918aefca98d146d264ce5cb3d3f30fddab6/uwu.m3u8",
      //   "skip": {
      //     "op": {
      //       "startTime": 28.783,
      //       "endTime": 118.783
      //     },
      //     "ed": {
      //       "startTime": 1387.996,
      //       "endTime": 1500
      //     }
      //   },
      //   "number": 1
      // }

      // Direct check for the exact structure from your example
      if (response.data.sources && typeof response.data.sources === 'string') {
        // New API format where sources is the direct m3u8 URL
        console.log('Found sources as string:', response.data.sources);
        result.url = response.data.sources;
      }
      // Fallback checks for other possible structures
      else if (response.data.sources && Array.isArray(response.data.sources)) {
        // Alternative format where sources might be an array
        console.log('Found sources as array');
        const hlsSource = response.data.sources.find(s => s.url && s.url.includes('.m3u8'));
        if (hlsSource) {
          result.url = hlsSource.url;
        }
      } else if (response.data.source) {
        // Try "source" (singular) property
        console.log('Found source property:', response.data.source);
        result.url = response.data.source;
      } else if (response.data.url) {
        // Fallback to url property
        console.log('Found url property:', response.data.url);
        result.url = response.data.url;
      } else if (response.data.data && response.data.data.url) {
        // Nested data structure
        console.log('Found nested data.url property');
        result.url = response.data.data.url;
      } else {
        // Last resort: try to find any property that looks like a URL
        console.log('Searching for URL-like properties in response');
        for (const key in response.data) {
          const value = response.data[key];
          if (typeof value === 'string' &&
              (value.includes('.m3u8') || value.includes('http') || value.includes('https'))) {
            console.log(`Found URL-like property in ${key}:`, value);
            result.url = value;
            break;
          }
        }
      }

      // Extract skip intro/outro timestamps if available
      if (response.data.skip && response.data.skip.op) {
        result.skipIntro = {
          start: response.data.skip.op.startTime,
          end: response.data.skip.op.endTime
        };
      }

      if (response.data.skip && response.data.skip.ed) {
        result.skipOutro = {
          start: response.data.skip.ed.startTime,
          end: response.data.skip.ed.endTime
        };
      }

      // Extract additional metadata if available
      if (response.data.number) {
        result.number = response.data.number;
      }
    }

    // Make sure URL is properly formatted
    if (result.url && typeof result.url === 'string' && !result.url.startsWith('http')) {
      result.url = `https:${result.url}`;
    }

    console.log('Processed source data:', result);
    return result;
  } catch (error) {
    console.error("Error fetching video source:", error);
    throw new Error("Failed to load video source");
  }
};
