# AnimeHQ Profile Backend

This is a backend server for AnimeHQ profile features, including level, streak, likes, and daily tasks.

## Setup

1. Install dependencies:
   ```
   npm install
   ```

2. Create a `.env` file with your MongoDB connection string:
   ```
   MONGODB_URI=mongodb://localhost:27017/animehq-profiles
   ```

3. Start the server:
   ```
   npm start
   ```

   Or for development with auto-restart:
   ```
   npm run dev
   ```

## API Endpoints

### User Profile

```
GET /api/profile/:userId
```
Get user profile data

```
PUT /api/profile/:userId
```
Update user profile data

### Streak and Visits

```
POST /api/profile/:userId/visit
```
Record a visit and update streak

### XP and Level

```
POST /api/profile/:userId/xp
```
Add XP to user profile

### Likes

```
POST /api/profile/:userId/like
```
Add a like to user profile

### Daily Tasks

```
GET /api/profile/:userId/tasks
```
Get daily tasks

```
POST /api/profile/:userId/tasks/:taskId/complete
```
Complete a task

### Achievements

```
POST /api/profile/:userId/achievements
```
Unlock an achievement

### Profile Images

```
PUT /api/profile/:userId/picture
```
Update profile picture

```
PUT /api/profile/:userId/banner
```
Update profile banner

## Notes

- This backend is separate from the main AnimeHQ backend
- It runs on port 3004 by default
- It uses MongoDB to store user profile data
- It includes fallback to localStorage if the API is unavailable
