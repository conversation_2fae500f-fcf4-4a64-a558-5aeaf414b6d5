import React, { memo } from 'react';
import { <PERSON> } from 'react-router-dom';
import Image from '@/components/ui/Image';
import { Star, Play, ChevronRight, Trophy, Award } from 'lucide-react';
import AgeRating from '@/components/ui/AgeRating';
import { Carousel, CarouselContent, CarouselItem } from "@/components/ui/carousel";
import { Button } from '@/components/ui/button';

// Memoize the AnimeCard component to prevent unnecessary re-renders
const AnimeCard = memo(({ anime, index }) => {
  return (
    <div className="flex size-full flex-col group cursor-pointer">
      {/* Card Container */}
      <div className="relative w-full h-full transition-all duration-300 group-hover:scale-[1.03]">
        {/* Main Card */}
        <div className="relative w-full aspect-[1/1.45] rounded-xl overflow-hidden shadow-lg border border-white/10">
          {/* Background Image */}
          <div className="absolute inset-0 w-full h-full">
            <Image
              src={anime?.images?.coverLarge || anime?.images?.coverMedium || anime?.images?.coverSmall}
              quality="high"
              className="!object-cover !w-full !h-full transition-all duration-500 group-hover:scale-110"
              loading="lazy"
            />
          </div>

          {/* Simplified Overlay - removed backdrop-blur for better performance */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-black/20 opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>

          {/* Content Container */}
          <Link
            to={anime?.id ? `/anime/${anime?.id}` : ""}
            className="absolute inset-0 flex flex-col justify-between p-3 z-10"
          >
            {/* Top Section */}
            <div className="flex flex-col items-start gap-1">
              {/* Age Rating */}
              <AgeRating isAdult={anime?.isAdult} genres={anime?.genres} compact={true} />

              {/* Rank Badge */}
              <div className="absolute top-2 right-2 bg-black/60 text-white px-2 py-1 rounded-md text-xs border border-white/10 flex items-center gap-1">
                <Trophy size={10} className="text-white" />
                <span>#{index + 3}</span>
              </div>
            </div>

            {/* Bottom Section */}
            <div className="mt-auto">
              {/* Title */}
              <h3 className="text-sm font-medium line-clamp-2 text-white mb-1">
                {anime?.title}
              </h3>

              {/* Status Indicator - With breathing effect for Airing */}
              {anime?.status && (
                <div className="flex items-center gap-1.5 mb-1">
                  <span className={`relative flex h-2 w-2 ${
                    anime?.status === "RELEASING" ? "bg-green-500" :
                    anime?.status === "FINISHED" ? "bg-blue-500" :
                    anime?.status === "NOT_YET_RELEASED" ? "bg-yellow-500" :
                    anime?.status === "CANCELLED" ? "bg-red-500" :
                    "bg-gray-500"
                  } rounded-full`}>
                    {anime?.status === "RELEASING" && (
                      <span className="absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75 animate-ping"></span>
                    )}
                  </span>
                  <span className="text-[10px] text-white/80">
                    {anime?.status === "RELEASING" ? "Airing" :
                     anime?.status === "FINISHED" ? "Completed" :
                     anime?.status === "NOT_YET_RELEASED" ? "Coming Soon" :
                     anime?.status === "CANCELLED" ? "Cancelled" :
                     "Unknown"}
                  </span>
                </div>
              )}

              {/* Metadata */}
              <div className="flex items-center gap-2 text-[10px] text-white/70">
                <span className="uppercase">{anime?.type}</span>
                {anime?.release_date && (
                  <>
                    <span className="mx-0.5">•</span>
                    <span>{anime?.release_date?.slice(-4)}</span>
                  </>
                )}
              </div>

              {/* Rating */}
              {Number(anime?.rating) > 0 && (
                <div className="absolute bottom-3 right-3 flex items-center gap-1 bg-black/60 text-white px-2 py-1 rounded-md text-xs border border-white/10">
                  <Star fill="white" stroke="white" size={10} />
                  <span>{Number(anime?.rating)?.toFixed(1)}</span>
                </div>
              )}
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
});

// Memoize the FeaturedAnimeCard component
const FeaturedAnimeCard = memo(({ anime, index }) => {
  return (
    <Link
      to={anime?.id ? `/anime/${anime?.id}` : ""}
      className="group relative overflow-hidden rounded-xl"
    >
      {/* Background image */}
      <div className="absolute inset-0 z-0">
        <Image
          src={anime?.images?.bannerLarge || anime?.images?.bannerSmall || anime?.images?.coverLarge}
          className="!object-cover !w-full !h-full group-hover:scale-105 transition-all duration-500"
          quality="high"
          loading="lazy"
        />
        {/* Simplified overlay - removed backdrop-blur */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-black/20 opacity-80"></div>
      </div>

      <div className="relative z-10 flex flex-col h-[180px] p-4 justify-end">
        {/* Rank badge */}
        <div className="absolute top-3 left-3 bg-black/60 text-white font-bold text-sm px-3 py-1.5 rounded-md flex items-center gap-1.5 border border-white/10">
          <Trophy size={14} className="text-white" />
          <span className="text-white">#{index + 1}</span>
        </div>

        {/* Rating */}
        {Number(anime?.rating) > 0 && (
          <div className="absolute top-3 right-3 bg-black/60 px-3 py-1.5 rounded-md flex items-center gap-1.5 border border-white/10">
            <Star fill="white" stroke="white" size={14} />
            <span className="text-sm font-bold text-white">{Number(anime?.rating)?.toFixed(1)}</span>
          </div>
        )}

        {/* Title and info */}
        <h3 className="text-lg font-bold text-white group-hover:text-white/90 transition-colors duration-300 line-clamp-1">{anime?.title}</h3>

        {/* Status Indicator - With breathing effect for Airing */}
        {anime?.status && (
          <div className="flex items-center gap-2 mt-1 mb-1">
            <span className={`relative flex h-2.5 w-2.5 ${
              anime?.status === "RELEASING" ? "bg-green-500" :
              anime?.status === "FINISHED" ? "bg-blue-500" :
              anime?.status === "NOT_YET_RELEASED" ? "bg-yellow-500" :
              anime?.status === "CANCELLED" ? "bg-red-500" :
              "bg-gray-500"
            } rounded-full`}>
              {anime?.status === "RELEASING" && (
                <span className="absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75 animate-ping"></span>
              )}
            </span>
            <span className="text-sm text-white/80">
              {anime?.status === "RELEASING" ? "Airing" :
               anime?.status === "FINISHED" ? "Completed" :
               anime?.status === "NOT_YET_RELEASED" ? "Coming Soon" :
               anime?.status === "CANCELLED" ? "Cancelled" :
               "Unknown"}
            </span>
          </div>
        )}

        <div className="flex items-center text-sm text-gray-300 mt-1">
          <span className="uppercase">{anime?.type}</span>
          {anime?.episodes && (
            <>
              <span className="mx-1">•</span>
              <span>{anime?.episodes} Episodes</span>
            </>
          )}
          {anime?.release_date && (
            <>
              <span className="mx-1">•</span>
              <span>{anime?.release_date?.slice(-4)}</span>
            </>
          )}
        </div>

        {/* Genres */}
        {anime?.genres && anime.genres.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {anime.genres.slice(0, 3).map((genre, i) => (
              <span key={i} className="text-xs px-2 py-0.5 bg-white/20 text-white rounded-full">
                {typeof genre === 'string' ? genre : ''}
              </span>
            ))}
          </div>
        )}

        {/* Watch button */}
        <div className="absolute bottom-4 right-4 bg-white text-black font-bold px-4 py-2 rounded-full flex items-center gap-2 opacity-0 group-hover:opacity-100 transform-gpu scale-90 group-hover:scale-100 transition-all duration-300">
          <Play fill="black" size={16} />
          <span>Watch</span>
        </div>
      </div>
    </Link>
  );
});

const TopRatedAnime = memo(({ data }) => {
  // Handle empty or loading state
  if (!data || data.length === 0) {
    return (
      <div className="w-full flex flex-col gap-4">
        <div className="flex items-center gap-2 mb-3">
          <div className="bg-white/20 p-1.5 rounded-md animate-pulse">
            <Award size={16} className="text-white/50" />
          </div>
          <div className="h-6 w-48 bg-white/10 animate-pulse rounded"></div>
          <div className="flex-grow"></div>
          <div className="h-4 w-16 bg-white/10 animate-pulse rounded"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Array.from({ length: 2 }).map((_, index) => (
            <div key={index} className="flex bg-white/5 rounded-xl h-[180px] animate-pulse"></div>
          ))}
        </div>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="flex flex-col gap-2">
              <div className="aspect-[1/1.45] bg-white/10 animate-pulse rounded-xl"></div>
              <div className="h-4 w-full bg-white/5 animate-pulse rounded"></div>
              <div className="h-4 w-2/3 bg-white/5 animate-pulse rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Split the data - featured (top 2) and the rest
  const featuredAnime = data.slice(0, 2);
  const remainingAnime = data.slice(2, 8);

  return (
    <div className="w-full flex flex-col gap-6">
      {/* Header with award icon - simplified */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <div className="bg-black/40 p-2 rounded-md border border-white/10">
            <Award size={16} className="text-white" />
          </div>
          <h2 className="text-xl lg:text-2xl font-semibold">Top Rated Anime</h2>
        </div>
        <Link
          to="/explore?sort=SCORE_DESC"
          className="bg-black/40 hover:bg-black/60 text-white px-3 py-1.5 rounded-md text-sm transition-all duration-200 flex items-center gap-2 border border-white/10 hover:border-white/20"
        >
          <span>View All</span>
          <ChevronRight size={14} />
        </Link>
      </div>

      {/* Featured top 2 anime - using memoized component */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {featuredAnime.map((anime, index) => (
          <FeaturedAnimeCard key={index} anime={anime} index={index} />
        ))}
      </div>

      {/* Remaining top anime in a grid - using memoized component */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
        {remainingAnime.map((anime, index) => (
          <AnimeCard key={index} anime={anime} index={index} />
        ))}
      </div>
    </div>
  );
});

// Export the memoized component
export default TopRatedAnime;
