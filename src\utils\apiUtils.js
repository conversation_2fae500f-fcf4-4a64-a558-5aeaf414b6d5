/**
 * Utility functions for API requests, including rate limit handling
 */

import axios from "axios";
import { toast } from "sonner";

// Cache for API responses
const apiCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

// Rate limit tracking
let isRateLimited = false;
let rateLimitResetTime = null;

// Event listeners for rate limit changes
const rateLimitListeners = new Set();

/**
 * Makes a GraphQL request to AniList with retry logic for rate limiting
 * @param {Object} options - Request options
 * @param {string} options.query - GraphQL query
 * @param {Object} options.variables - GraphQL variables
 * @param {string} options.token - Auth token
 * @param {string} options.cacheKey - Key for caching (optional)
 * @param {number} options.cacheTTL - Cache TTL in ms (optional, defaults to 5 minutes)
 * @param {number} options.maxRetries - Maximum number of retries (default: 3)
 * @param {number} options.initialDelay - Initial delay in ms (default: 1000)
 * @returns {Promise<Object>} - Response data
 */
export const makeAniListRequest = async ({
  query,
  variables = {},
  token,
  cacheKey = null,
  cacheTTL = CACHE_TTL,
  maxRetries = 3,
  initialDelay = 1000,
}) => {
  // Check cache if cacheKey is provided
  if (cacheKey) {
    const cachedData = apiCache.get(cacheKey);
    if (cachedData && cachedData.expiry > Date.now()) {
      console.log(`Using cached data for key: ${cacheKey}`);
      return cachedData.data;
    } else if (cachedData) {
      console.log(`Cache expired for key: ${cacheKey}`);
      apiCache.delete(cacheKey);
    }
  }

  let retries = 0;
  let delay = initialDelay;

  while (retries <= maxRetries) {
    try {
      const headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
      };

      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await axios.post(
        "https://graphql.anilist.co",
        {
          query,
          variables,
        },
        {
          headers,
          timeout: 15000, // 15 second timeout
        }
      );

      // Check for GraphQL errors
      if (response.data.errors) {
        const error = response.data.errors[0];

        // Check if it's a rate limit error
        if (error.status === 429 || error.message?.includes("rate limit")) {
          // Set rate limit status
          setRateLimitStatus(true, delay * 2);

          if (retries < maxRetries) {
            retries++;
            console.log(`Rate limited by AniList API. Retrying in ${delay}ms (${retries}/${maxRetries})`);

            if (retries === 1) {
              // Only show toast on first retry to avoid spamming
              toast.warning("AniList API rate limit reached. Retrying...");
            }

            await new Promise(resolve => setTimeout(resolve, delay));
            delay *= 2; // Exponential backoff
            continue;
          } else {
            console.error("Max retries reached for rate limit");
            toast.error("AniList API rate limit reached. Please try again later.");
            throw new Error("Rate limit exceeded after max retries");
          }
        }

        // Other GraphQL errors
        throw new Error(error.message || "GraphQL Error");
      }

      // If we got a successful response after retries, reset rate limit status
      if (retries > 0) {
        setRateLimitStatus(false);
      }

      // Cache the successful response if cacheKey is provided
      if (cacheKey) {
        apiCache.set(cacheKey, {
          data: response.data,
          expiry: Date.now() + cacheTTL,
        });
        console.log(`Cached response for key: ${cacheKey}`);
      }

      return response.data;
    } catch (error) {
      // Handle network errors or other axios errors
      if (error.response?.status === 429 || error.message?.includes("rate limit")) {
        // Set rate limit status
        setRateLimitStatus(true, delay * 2);

        if (retries < maxRetries) {
          retries++;
          console.log(`Rate limited by AniList API. Retrying in ${delay}ms (${retries}/${maxRetries})`);

          if (retries === 1) {
            toast.warning("AniList API rate limit reached. Retrying...");
          }

          await new Promise(resolve => setTimeout(resolve, delay));
          delay *= 2; // Exponential backoff
          continue;
        } else {
          console.error("Max retries reached for rate limit");
          toast.error("AniList API rate limit reached. Please try again later.");
          throw new Error("Rate limit exceeded after max retries");
        }
      }

      // For other errors, throw immediately
      throw error;
    }
  }
};

/**
 * Clears the API cache
 * @param {string} key - Specific cache key to clear (optional, clears all if not provided)
 */
export const clearApiCache = (key = null) => {
  if (key) {
    apiCache.delete(key);
    console.log(`Cleared cache for key: ${key}`);
  } else {
    apiCache.clear();
    console.log("Cleared entire API cache");
  }
};

/**
 * Gets the current size of the API cache
 * @returns {number} - Number of cached items
 */
export const getApiCacheSize = () => {
  return apiCache.size;
};

/**
 * Check if the API is currently rate limited
 * @returns {boolean} - Whether the API is rate limited
 */
export const isApiRateLimited = () => {
  return isRateLimited;
};

/**
 * Get the estimated time until rate limit reset
 * @returns {number|null} - Milliseconds until reset or null if not rate limited
 */
export const getRateLimitResetTime = () => {
  if (!rateLimitResetTime) return null;
  return Math.max(0, rateLimitResetTime - Date.now());
};

/**
 * Subscribe to rate limit status changes
 * @param {Function} callback - Function to call when rate limit status changes
 * @returns {Function} - Unsubscribe function
 */
export const subscribeToRateLimitChanges = (callback) => {
  rateLimitListeners.add(callback);

  // Call immediately with current status
  callback(isRateLimited);

  // Return unsubscribe function
  return () => {
    rateLimitListeners.delete(callback);
  };
};

/**
 * Set the rate limit status and notify listeners
 * @param {boolean} limited - Whether the API is rate limited
 * @param {number} resetDelay - Delay in ms until the rate limit resets
 */
const setRateLimitStatus = (limited, resetDelay = null) => {
  const changed = isRateLimited !== limited;
  isRateLimited = limited;

  if (resetDelay) {
    rateLimitResetTime = Date.now() + resetDelay;
  } else if (!limited) {
    rateLimitResetTime = null;
  }

  if (changed) {
    // Notify all listeners
    rateLimitListeners.forEach(listener => {
      try {
        listener(isRateLimited);
      } catch (error) {
        console.error("Error in rate limit listener:", error);
      }
    });
  }
};
