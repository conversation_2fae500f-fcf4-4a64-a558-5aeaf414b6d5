import { getAnimeDetails } from "@/api/anilist";
import { getAnimeEpisodes } from "@/api/aninow";
import Strip from "@/components/home/<USER>";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import Image from "@/components/ui/Image";
import VidstackPlayer from "@/components/watch/VidstackPlayer";
import CommentList from "@/components/comments/CommentList";
import { useMainContext } from "@/hooks/useContexts";
import useFetch from "@/hooks/useFetch";
import { ArrowLeft, ArrowRight, Building, Calendar, ChevronRight, Clock, Dot, Info as InfoIcon, Loader2, MessageSquare, Search, Star, X } from "lucide-react";
import { useWatchHistory } from "@/contexts/WatchHistoryContext";
import { useEffect, useMemo, useRef, useState, memo } from "react";
import { Link, useParams, useSearchParams } from "react-router-dom";

const Watch = () => {
  const { setSrc } = useMainContext();
  const { id } = useParams();
  const [query, setQuery] = useSearchParams();
  const [bg, setBg] = useState("");
  const [apiEpisodes, setApiEpisodes] = useState([]);

  // Get current episode number
  const ep = useMemo(() => {
    return Number(query.get("ep") || 1);
  }, [query]);
  const { data: info } = useFetch({
    key: ["anime-info", id],
    fun: async () => {
      return (await getAnimeDetails(id)) || {};
    },
  });


  useEffect(() => {
    setSrc(`anime/${id}`);
  }, [id, setSrc]);

  // Fetch episodes from API with auto-refresh capability
  useEffect(() => {
    if (!id) return;

    const fetchEpisodes = async () => {
      try {
        const episodesList = await getAnimeEpisodes(id);

        // Only update if we have new data and it's different from current data
        if (episodesList && episodesList.length > 0) {
          // Check if we have new episodes
          if (apiEpisodes.length === 0 || episodesList.length !== apiEpisodes.length) {
            console.log(`[Episode Update] Found ${episodesList.length} episodes for anime ${id}`);

            // Check if this is a new episode being added (not just initial load)
            if (apiEpisodes.length > 0 && episodesList.length > apiEpisodes.length) {
              // We have new episodes!
              setNewEpisodesAdded(true);
              console.log(`[New Episodes] ${episodesList.length - apiEpisodes.length} new episodes added!`);

              // Show notification if supported
              if ("Notification" in window && Notification.permission === "granted") {
                new Notification(`New Episode Available`, {
                  body: `${info?.title} has ${episodesList.length - apiEpisodes.length} new episode(s)!`,
                  icon: info?.images?.coverSmall || '/favicon.ico'
                });
              }
            }

            // Update the episode list
            setApiEpisodes(episodesList);

            // Store the current count for future comparison
            previousEpisodeCount.current = episodesList.length;
          }
        }
      } catch (error) {
        console.error("Error fetching episode images:", error);
      }
    };

    // Initial fetch
    fetchEpisodes();

    // Set up auto-refresh for currently airing anime
    let refreshInterval;

    if (info?.status === "RELEASING") {
      console.log("[Auto-refresh] Setting up episode refresh for currently airing anime");

      // Check for new episodes every 30 minutes (1800000 ms)
      // This is a reasonable interval that won't overload the API
      refreshInterval = setInterval(fetchEpisodes, 1800000);
    }

    // Clean up interval on unmount or when anime changes
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [id, info?.status, apiEpisodes.length]);
  // Generate episodes array based on info.episodes or API data
  const episodes = useMemo(() => {
    // If we have API episodes with images, use those
    if (apiEpisodes.length > 0) {
      return apiEpisodes.map(ep => ({
        number: ep.number,
        title: ep.title || `Episode ${ep.number}`,
        description: info?.title || "",
        image: ep.image || info?.images?.bannerSmall || "",
        watchId: ep.watchId
      }));
    }

    // Fallback to generating episodes from info
    if (!info?.episodes) return [];
    return Array.from({ length: info.episodes }, (_, i) => ({
      number: i + 1,
      title: `Episode ${i + 1}`,
      description: info?.title || "",
      image: info?.images?.bannerSmall || ""
    }));
  }, [info, apiEpisodes]);

  const details = useMemo(() => {
    // Find the current episode object
    const currentEpisode = episodes.find(e => Number(e.number) === Number(ep));
    const episodeTitle = currentEpisode?.title || `Episode ${ep}`;

    // Set document title with episode title
    document.title = info?.title?.length
      ? `${info.title} - ${episodeTitle}`
      : "CineHQ";

    return {
      id: Number(info?.id || id),
      title: info?.title,
      tagline: info?.tagline,
      image: info?.images?.coverSmall,
      poster: info?.images?.bannerSmall,
      type: info?.type,
      totalEpisodes: apiEpisodes.length > 0 ? apiEpisodes.length : info?.episodes
    };
  }, [info, episodes, ep, apiEpisodes]);
  return (
    <>
      {/* Enhanced background with multiple layers for depth and visual interest */}
      <div className="top-0 left-0 fixed w-screen h-screen pointer-events-none z-0">
        {/* Main background image with blur effect */}
        <div className="absolute inset-0 blur-3xl brightness-[0.3] opacity-80">
          <Image
            src={
              bg?.length
                ? bg
                : window.innerWidth > 700
                ? info?.images?.bannerLarge
                : info?.images?.coverSmall
            }
          />
        </div>

        {/* Gradient overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/80 via-black/60 to-black/90"></div>

        {/* Subtle animated gradient for visual interest */}
        <div className="absolute inset-0 bg-gradient-to-tr from-blue-900/10 via-purple-900/5 to-transparent opacity-40"></div>
      </div>
      <div className="w-full relative z-10 pb-4 flex flex-col">
        <div className="flex w-full items-center px-1 py-3">
          <div className="flex items-center gap-2 text-sm lg:text-[.9rem]">
            <Link
              to={"/"}
              className="hover:underline hover:text-white text-gray-200 smooth pp shrink-0"
            >
              Home
            </Link>
            -
            <Link
              to={`/explore`}
              className="capitalize hover:underline hover:text-white text-gray-200 pp smooth shrink-0"
            >
              Anime
            </Link>
            -
            <div className="smooth pp text-white font-medium !line-clamp-1">
              {info?.title || "Loading..."}
            </div>
          </div>
        </div>
        <div className="flex w-full flex-col gap-10 lg:gap-16">
          {/* Main content area with player and episodes */}
          <div className="flex-col flex lg:flex-row w-full gap-4 lg:h-[calc(100vh-10rem)] xl:h-[calc(100vh-12rem)] 2xl:h-[calc(100vh-14rem)] max-w-[1600px] mx-auto">
            {/* Episodes list */}
            <Episodes info={info} setBg={setBg} apiEpisodes={apiEpisodes} />

            {/* Player container with enhanced styling */}
            <div className="flex flex-col w-full lg:w-[72%] xl:w-[75%] gap-2 h-full">
              <div className="w-full h-full rounded-xl overflow-hidden !select-none shadow-xl xl:px-0 xl:py-2 2xl:py-4">
                {/* Vidstack player with AniNow API */}
                <VidstackPlayer
                  details={details}
                  episode={String(ep)} // Ensure episode is passed as a string
                  episodes={episodes}
                  onEpisodeChange={(newEpisode) => {
                    // Update URL without page reload
                    const newQuery = new URLSearchParams(window.location.search);
                    newQuery.set("ep", newEpisode);
                    setQuery(newQuery);

                    // Find the episode title
                    const episodeObj = episodes.find(e => Number(e.number) === Number(newEpisode));
                    const episodeTitle = episodeObj?.title || `Episode ${newEpisode}`;

                    // Update document title
                    document.title = info?.title ? `${info.title} - ${episodeTitle}` : "AnimeHQ";

                    // Scroll to top for better UX
                    window.scrollTo(0, 0);
                  }}
                />
              </div>

              {/* Episode navigation buttons for mobile */}
              <div className="flex justify-between items-center gap-2 lg:hidden mt-2">
                {Number(ep) > 1 ? (
                  <button
                    onClick={() => {
                      const prevEp = Number(ep) - 1;
                      const newQuery = new URLSearchParams(window.location.search);
                      newQuery.set("ep", prevEp);
                      setQuery(newQuery);

                      // Find the episode title
                      const episodeObj = episodes.find(e => Number(e.number) === Number(prevEp));
                      const episodeTitle = episodeObj?.title || `Episode ${prevEp}`;

                      // Update document title
                      document.title = info?.title ? `${info.title} - ${episodeTitle}` : "AnimeHQ";
                    }}
                    className="flex items-center gap-1 bg-black/40 backdrop-blur-sm hover:bg-black/60 px-3 py-2 rounded-lg border border-white/10 transition-colors"
                  >
                    <ArrowLeft size={16} />
                    <span className="text-sm">Previous</span>
                  </button>
                ) : (
                  <div className="flex-1"></div>
                )}

                <div className="bg-black/40 backdrop-blur-sm px-3 py-1.5 rounded-lg border border-white/10">
                  <span className="text-sm">
                    {episodes.find(e => Number(e.number) === Number(ep))?.title || `Episode ${ep}`}
                  </span>
                </div>

                {(apiEpisodes.length > 0 ? Number(ep) < apiEpisodes.length : info?.episodes && Number(ep) < info.episodes) ? (
                  <button
                    onClick={() => {
                      const nextEp = Number(ep) + 1;
                      const newQuery = new URLSearchParams(window.location.search);
                      newQuery.set("ep", nextEp);
                      setQuery(newQuery);

                      // Find the episode title
                      const episodeObj = episodes.find(e => Number(e.number) === Number(nextEp));
                      const episodeTitle = episodeObj?.title || `Episode ${nextEp}`;

                      // Update document title
                      document.title = info?.title ? `${info.title} - ${episodeTitle}` : "AnimeHQ";
                    }}
                    className="flex items-center gap-1 bg-black/40 backdrop-blur-sm hover:bg-black/60 px-3 py-2 rounded-lg border border-white/10 transition-colors"
                  >
                    <span className="text-sm">Next</span>
                    <ArrowRight size={16} />
                  </button>
                ) : (
                  <div className="flex-1"></div>
                )}
              </div>


            </div>
          </div>

          {/* Anime Relations Section - Single Line Glassy Carousel */}
          {info?.relations && <AnimeSeasons relations={info.relations} />}

          {/* Anime info section */}
          <Info info={info} />

          {/* Comments Section - Redesigned with modern styling */}
          <div className="flex w-full flex-col gap-4">
            <div className="flex items-center justify-between pb-3">
              <div className="flex items-center gap-3">
                <div className="bg-gradient-to-br from-blue-600 to-blue-400 p-2.5 rounded-xl shadow-lg">
                  <MessageSquare size={22} className="text-white drop-shadow-md" />
                </div>
                <div className="flex flex-col">
                  <h2 className="text-xl lg:text-2xl font-medium">Episode Discussion</h2>
                  <p className="text-xs text-gray-400">
                    Share your thoughts about {episodes.find(e => Number(e.number) === Number(ep))?.title || `Episode ${ep}`}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="text-sm bg-black/40 backdrop-blur-sm border border-white/10 px-4 py-1.5 rounded-full shadow-md">
                  <span>{episodes.find(e => Number(e.number) === Number(ep))?.title || `Episode ${ep}`}</span>
                </div>
              </div>
            </div>

            {/* Comments container with enhanced styling */}
            <div className="bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-5 flex flex-col h-[600px] shadow-xl">
              <CommentList
                animeId={id}
                episodeNumber={ep}
                animeTitle={info?.title}
                animeBanner={info?.images?.bannerLarge || info?.images?.coverLarge}
              />
            </div>
          </div>



          {/* Recommendations Section - Original Style */}
          {info?.recommendations?.length ? (
            <div className="flex w-full flex-col gap-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="bg-gradient-to-br from-purple-600 to-pink-400 p-2.5 rounded-xl shadow-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white drop-shadow-md">
                      <path d="M12 2L5 12l7 10 7-10z"></path>
                    </svg>
                  </div>
                  <div className="flex flex-col">
                    <h2 className="text-xl lg:text-2xl font-medium">You May Also Like</h2>
                    <p className="text-xs text-gray-400">Similar anime based on your current watch</p>
                  </div>
                </div>
                <Link
                  to="/explore"
                  className="text-sm bg-white/10 hover:bg-white/20 px-3 py-1.5 rounded-lg transition-colors flex items-center gap-1.5"
                >
                  Explore More <ChevronRight size={16} />
                </Link>
              </div>

              <div className="bg-black/30 backdrop-blur-sm rounded-xl p-4 border border-white/5">
                <Strip title={""} data={info?.recommendations} />
              </div>
            </div>
          ) : null}
        </div>
      </div>
    </>
  );
};

export default Watch;

// Memoized Episode Item Component for List View - One Piece Style
const EpisodeListItem = memo(({
  episode,
  isActive,
  isWatched,
  progress,
  onClick
}) => {
  return (
    <button
      key={episode.number}
      onClick={onClick}
      className={`group flex w-full smooth overflow-hidden transition-all duration-300 mb-1 rounded-lg ${
        isActive
          ? "bg-gradient-to-r from-white/20 to-white/10 backdrop-blur-sm border-l-4 border-l-white/80"
          : isWatched
            ? "bg-black/40 border-l-4 border-l-green-500"
            : "bg-black/40 backdrop-blur-sm hover:bg-black/60"
      }`}
      title={`Episode ${episode.number}: ${episode.title}`}
    >
      {/* Thumbnail with overlay */}
      <div className="flex h-[5.5rem] w-[8rem] shrink-0 overflow-hidden relative">
        <Image
          src={episode?.image}
          className={`object-cover transition-transform duration-500 ${isActive ? 'scale-110' : 'group-hover:scale-110'}`}
        />

        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent"></div>

        {/* Episode number badge */}
        <div className="absolute bottom-2 left-2 flex items-center">
          <div className={`flex items-center justify-center min-w-[2rem] h-7 px-2 rounded-sm ${
            isActive
              ? 'bg-white/80 backdrop-blur-sm text-black'
              : isWatched
                ? 'bg-green-500 text-white'
                : 'bg-black/80 text-white'
          }`}>
            <span className="text-xs font-bold">EP {episode?.number}</span>
          </div>
        </div>

        {/* Status overlay - shows "Watching" for active, "Watched" for watched, or nothing */}
        <div className={`absolute inset-0 flex items-center justify-center ${
          (isActive || isWatched) ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
        } transition-all duration-300`}>
          <div className={`backdrop-blur-sm px-3 py-1.5 rounded-md ${
            isActive
              ? 'bg-white/20 border border-white/30 text-white'
              : isWatched
                ? 'bg-green-500/20 border border-green-500/30 text-green-300'
                : 'bg-black/70 border border-white/10 text-white'
          }`}>
            <span className="text-sm font-medium">
              {isActive ? 'Watching' : isWatched ? 'Watched' : 'Watch'}
            </span>
          </div>
        </div>
      </div>

      {/* Episode info */}
      <div className="flex flex-col justify-center py-2 px-3 w-full">
        <div className="flex w-full !line-clamp-1 text-sm font-medium tracking-wide text-blue-100 group-hover:text-blue-200 transition-colors duration-300">
          {episode?.title || `Episode ${episode?.number}`}
        </div>

        {/* Date display */}
        <div className="flex justify-between items-center mt-1">
          {/* Air date - use a placeholder date based on episode number */}
          <div className="text-[10px] text-gray-400">
            {new Date(1999, 9, 20 + (episode.number - 1) * 7).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric'
            })}
          </div>

          {/* Progress indicator */}
          {progress > 0 && !isWatched && (
            <span className="text-[10px] text-white bg-white/20 backdrop-blur-sm px-1.5 py-0.5 rounded-full">
              {progress}%
            </span>
          )}
          {isWatched && (
            <span className="text-[10px] text-green-300 bg-green-500/20 px-1.5 py-0.5 rounded-full flex items-center gap-0.5">
              <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
              Watched
            </span>
          )}
        </div>

        {/* Progress bar - hidden on mobile */}
        <div className="hidden sm:block h-1 rounded-full w-full overflow-hidden bg-white/10 relative mt-2">
          <div
            className={`h-full rounded-full absolute left-0 top-0 ${
              isWatched ? 'bg-green-500' :
              isActive ? 'bg-white/80 backdrop-blur-sm' :
              progress > 0 ? 'bg-white/70 backdrop-blur-sm' :
              'bg-white/20'
            }`}
            style={{
              width: isWatched ? '100%' :
                     progress > 0 ? `${progress}%` :
                     isActive ? '5%' :
                     '0%'
            }}
          ></div>
        </div>
      </div>
    </button>
  );
});

// Memoized Episode Item Component for Grid View - One Piece Style
const EpisodeGridItem = memo(({
  episode,
  isActive,
  isWatched,
  progress,
  onClick
}) => {
  return (
    <button
      key={episode.number}
      onClick={onClick}
      className={`group flex flex-col w-full smooth overflow-hidden transition-all duration-300 rounded-md ${
        isActive
          ? "bg-black/60 border-t-4 border-t-white/80"
          : isWatched
            ? "bg-black/60 border-t-4 border-t-green-500"
            : "bg-black/60 hover:bg-black/80"
      }`}
      title={`Episode ${episode.number}: ${episode.title}`}
    >
      {/* Thumbnail with overlay */}
      <div className="flex w-full aspect-video overflow-hidden relative">
        <Image
          src={episode?.image}
          className={`object-cover transition-transform duration-500 ${isActive ? 'scale-110' : 'group-hover:scale-110'}`}
        />

        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-black/20"></div>

        {/* Episode number badge */}
        <div className="absolute bottom-2 left-2 flex items-center">
          <div className={`flex items-center justify-center min-w-[2rem] h-7 px-2 rounded-sm ${
            isActive
              ? 'bg-white/80 backdrop-blur-sm text-black'
              : isWatched
                ? 'bg-green-500 text-white'
                : 'bg-black/80 text-white'
          }`}>
            <span className="text-xs font-bold">EP {episode?.number}</span>
          </div>
        </div>

        {/* Status overlay - shows "Watching" for active, "Watched" for watched, or nothing */}
        <div className={`absolute inset-0 flex items-center justify-center ${
          (isActive || isWatched) ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
        } transition-all duration-300`}>
          <div className={`backdrop-blur-sm px-3 py-1.5 rounded-md ${
            isActive
              ? 'bg-white/20 border border-white/30 text-white'
              : isWatched
                ? 'bg-green-500/20 border border-green-500/30 text-green-300'
                : 'bg-black/70 border border-white/10 text-white'
          }`}>
            <span className="text-sm font-medium">
              {isActive ? 'Watching' : isWatched ? 'Watched' : 'Watch'}
            </span>
          </div>
        </div>
      </div>

      {/* Episode info */}
      <div className="flex flex-col p-2.5 w-full">
        <div className="flex w-full !line-clamp-1 text-xs font-medium tracking-wide text-blue-100 group-hover:text-blue-200 transition-colors duration-300">
          {episode?.title || `Episode ${episode?.number}`}
        </div>

        {/* Date display */}
        <div className="flex justify-between items-center mt-1">
          {/* Air date - use a placeholder date based on episode number */}
          <div className="text-[10px] text-gray-400">
            {new Date(1999, 9, 20 + (episode.number - 1) * 7).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric'
            })}
          </div>

          {/* Progress indicator */}
          {progress > 0 && !isWatched && (
            <span className="text-[10px] text-white bg-white/20 backdrop-blur-sm px-1.5 py-0.5 rounded-full">
              {progress}%
            </span>
          )}
          {isWatched && (
            <span className="text-[10px] text-green-300 bg-green-500/20 px-1.5 py-0.5 rounded-full flex items-center gap-0.5">
              <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
              Watched
            </span>
          )}
        </div>

        {/* Progress bar - hidden on mobile */}
        <div className="hidden sm:block h-1 rounded-full w-full overflow-hidden bg-white/10 relative mt-2">
          <div
            className={`h-full rounded-full absolute left-0 top-0 ${
              isWatched ? 'bg-green-500' :
              isActive ? 'bg-white/80 backdrop-blur-sm' :
              progress > 0 ? 'bg-white/70 backdrop-blur-sm' :
              'bg-white/20'
            }`}
            style={{
              width: isWatched ? '100%' :
                     progress > 0 ? `${progress}%` :
                     isActive ? '5%' :
                     '0%'
            }}
          ></div>
        </div>
      </div>
    </button>
  );
});

const Episodes = ({ info, setBg, apiEpisodes }) => {
  const { setSrc } = useMainContext();
  const { id } = useParams();
  const [query, setQuery] = useSearchParams();
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const searchInputRef = useRef(null);
  const loading = apiEpisodes.length === 0;
  const [viewMode, setViewMode] = useState("list"); // "list" or "grid"
  const { getProgress, isWatched } = useWatchHistory();

  // Episode range selection
  const ITEMS_PER_RANGE = 50; // Show 50 episodes per range
  const [selectedRange, setSelectedRange] = useState(0); // 0 = first range (1-50)
  const [isRangeDropdownOpen, setIsRangeDropdownOpen] = useState(false);
  const episodesContainerRef = useRef(null);
  const rangeDropdownRef = useRef(null);

  // State for tracking episodes (not used in this design but kept for future use)
  const [, setNewEpisodesAdded] = useState(false);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (rangeDropdownRef.current && !rangeDropdownRef.current.contains(event.target)) {
        setIsRangeDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Generate episodes array based on info.episodes or API data
  const episodes = useMemo(() => {
    // If we have API episodes with images, use those
    if (apiEpisodes.length > 0) {
      return apiEpisodes.map(ep => ({
        number: ep.number,
        title: ep.title || `Episode ${ep.number}`,
        description: info?.title || "",
        image: ep.image || info?.images?.bannerSmall || "",
        watchId: ep.watchId
      }));
    }

    // Fallback to generating episodes from info
    if (!info?.episodes) return [];
    return Array.from({ length: info.episodes }, (_, i) => ({
      number: i + 1,
      title: `Episode ${i + 1}`,
      description: info?.title || "",
      image: info?.images?.bannerSmall || ""
    }));
  }, [info, apiEpisodes]);

  // Calculate total ranges
  const totalRanges = useMemo(() => {
    return Math.ceil(episodes.length / ITEMS_PER_RANGE);
  }, [episodes.length, ITEMS_PER_RANGE]);

  // Filter episodes based on search query and selected range
  const filteredEpisodes = useMemo(() => {
    // First filter by search query
    const filtered = !searchQuery.trim()
      ? episodes
      : episodes.filter(episode => {
          const episodeNumber = episode.number.toString();
          const episodeTitle = episode.title.toLowerCase();
          const searchLower = searchQuery.toLowerCase();

          return episodeNumber.includes(searchQuery) ||
                episodeTitle.includes(searchLower);
        });

    // If searching, return all matching episodes
    if (searchQuery.trim()) return filtered;

    // Otherwise, return episodes for the selected range
    const startIndex = selectedRange * ITEMS_PER_RANGE;
    const endIndex = Math.min(startIndex + ITEMS_PER_RANGE, episodes.length);

    return filtered.slice(startIndex, endIndex);
  }, [episodes, searchQuery, selectedRange, ITEMS_PER_RANGE]);

  // Reset selected range when changing anime or search query
  useEffect(() => {
    setSelectedRange(0);

    // Also reset the new episodes indicator when changing anime
    if (info?.id) {
      setNewEpisodesAdded(false);
    }
  }, [info?.id, searchQuery]);

  const ep = useMemo(() => {
    return Number(query.get("ep") || 1);
  }, [query]);

  useEffect(() => {
    setSrc(`anime/${id}/${ep}`);
    if (info?.images?.bannerSmall) setBg(info.images.bannerSmall);
  }, [ep, id, setSrc, info]);

  // Clear search when anime changes
  useEffect(() => {
    setSearchQuery("");
  }, [info?.id]);

  // Add keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Check if Ctrl+F or Command+F is pressed to focus search
      if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault(); // Prevent browser's default search
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }

      // Check if Escape is pressed to clear search when focused
      if (e.key === 'Escape' && document.activeElement === searchInputRef.current && searchQuery) {
        e.preventDefault();
        setSearchQuery('');
        searchInputRef.current.blur();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [searchQuery]);

  return (
    <div className="flex flex-col order-last lg:order-first w-full h-[30rem] lg:h-full lg:w-[28%] xl:w-[25%] bg-black/60 overflow-hidden rounded-xl">
      {/* Header with sticky positioning */}
      <div className="sticky top-0 z-10 bg-black/80 backdrop-blur-md px-3 py-3 flex flex-col gap-2 rounded-t-xl">
        {/* Next Episode Countdown - Only show for currently airing anime */}
        {info?.status === "RELEASING" && info?.nextAiringEpisode && (
          <div className="mb-2 w-full">
            <NextEpisodeCountdown nextAiringEpisode={info.nextAiringEpisode} />
          </div>
        )}
        {/* Title and episode range selector */}
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <div className="text-xl font-medium tracking-wide">Episodes</div>
            <div
              ref={rangeDropdownRef}
              className="relative"
            >
              <div
                className="bg-black text-sm px-3 py-1.5 font-medium text-white flex items-center gap-1 cursor-pointer rounded-md"
                onClick={() => setIsRangeDropdownOpen(!isRangeDropdownOpen)}
              >
                <span>EPS {selectedRange * ITEMS_PER_RANGE + 1} - </span>
                <span>{Math.min((selectedRange + 1) * ITEMS_PER_RANGE, episodes.length)}</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-1">
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              </div>

              {/* Dropdown menu */}
              {isRangeDropdownOpen && (
                <div className="absolute top-full left-0 z-50 w-40 mt-1 bg-black/90 shadow-lg max-h-80 overflow-y-auto rounded-md">
                  {Array.from({ length: totalRanges }).map((_, index) => {
                    const start = index * ITEMS_PER_RANGE + 1;
                    const end = Math.min((index + 1) * ITEMS_PER_RANGE, episodes.length);
                    return (
                      <div
                        key={index}
                        className={`px-3 py-1.5 cursor-pointer hover:bg-white/20 ${selectedRange === index ? 'bg-white/20' : ''}`}
                        onClick={() => {
                          setSelectedRange(index);
                          setIsRangeDropdownOpen(false);
                        }}
                      >
                        EPS {start} - {end}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>

          {/* View toggle buttons */}
          <div className="flex gap-2">
            <button
              onClick={() => setViewMode("grid")}
              className={`p-2 transition-all duration-300 rounded-md ${viewMode === "grid" ? "bg-black/60 text-white" : "text-gray-400 hover:text-white"}`}
              title="Grid view"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="3" y="3" width="7" height="7"></rect>
                <rect x="14" y="3" width="7" height="7"></rect>
                <rect x="14" y="14" width="7" height="7"></rect>
                <rect x="3" y="14" width="7" height="7"></rect>
              </svg>
            </button>
            <button
              onClick={() => setViewMode("list")}
              className={`p-2 transition-all duration-300 rounded-md ${viewMode === "list" ? "bg-black/60 text-white" : "text-gray-400 hover:text-white"}`}
              title="List view"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="8" y1="6" x2="21" y2="6"></line>
                <line x1="8" y1="12" x2="21" y2="12"></line>
                <line x1="8" y1="18" x2="21" y2="18"></line>
                <line x1="3" y1="6" x2="3.01" y2="6"></line>
                <line x1="3" y1="12" x2="3.01" y2="12"></line>
                <line x1="3" y1="18" x2="3.01" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>

        {/* Search input */}
        <div className="w-full">
          <div className={`flex items-center w-full bg-black/60 ${isSearchFocused ? 'border-l-4 border-l-white/80' : 'border-l-4 border-l-transparent'} overflow-hidden transition-all duration-300 rounded-md`}>
            <div className="flex items-center justify-center pl-3">
              <Search size={16} className={`${isSearchFocused ? 'text-white' : 'text-gray-400'} transition-colors duration-300`} />
            </div>
            <input
              ref={searchInputRef}
              type="text"
              placeholder="Filter episodes..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onFocus={() => setIsSearchFocused(true)}
              onBlur={() => setIsSearchFocused(false)}
              className="w-full bg-transparent py-2 px-2.5 text-sm outline-none placeholder:text-gray-500"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="pr-3 group relative"
                title="Clear search"
              >
                <X size={16} className="text-gray-400 group-hover:text-white transition-colors duration-300" />
              </button>
            )}
          </div>
        </div>

        {/* Show filtered count if searching */}
        {searchQuery && filteredEpisodes.length > 0 && (
          <div className="text-xs text-gray-300 bg-black/60 px-3 py-1 inline-flex items-center self-start border-l-4 border-l-white/80 rounded-r-md">
            Found: {filteredEpisodes.length} of {episodes.length} episodes
          </div>
        )}

        {/* Range info - hidden since we now use dropdown */}
        {false && !searchQuery && totalRanges > 1 && (
          <div className="text-xs text-gray-300 bg-black/60 px-3 py-1 inline-flex items-center self-start">
            Range {selectedRange + 1} of {totalRanges}
          </div>
        )}
      </div>

      {/* Episodes list with custom scrollbar - matches player height */}
      <div className="flex-1 overflow-hidden relative">
        <div className="h-full overflow-y-auto custom-scrollbar p-0">
          {/* Subtle gradient overlay at the top for smooth transition */}
          <div className="absolute top-0 left-0 right-0 h-6 bg-gradient-to-b from-black/40 to-transparent pointer-events-none z-10"></div>
          {loading ? (
            <div className="flex items-center justify-center h-40">
              <div className="flex flex-col items-center gap-3">
                <div className="relative">
                  <div className="w-10 h-10 border-4 border-blue-500/30 border-t-blue-500 rounded-full animate-spin"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-6 h-6 bg-black rounded-full"></div>
                  </div>
                </div>
                <div className="text-sm text-blue-200 font-medium">Loading episodes...</div>
              </div>
            </div>
          ) : !episodes?.length ? (
            <div className="flex items-center justify-center h-40">
              <div className="flex flex-col items-center gap-3">
                <div className="relative">
                  <div className="w-10 h-10 border-4 border-blue-500/30 border-t-blue-500 rounded-full animate-spin"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-6 h-6 bg-black rounded-full"></div>
                  </div>
                </div>
                <div className="text-sm text-blue-200 font-medium">Loading episodes...</div>
              </div>
            </div>
          ) : filteredEpisodes.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-40 text-center">
              <div className="text-gray-300 mb-4 bg-black/40 px-4 py-2 rounded-lg border border-white/10">No episodes found</div>
              <button
                onClick={() => setSearchQuery('')}
                className="text-sm bg-gradient-to-r from-white/30 to-white/10 hover:from-white/40 hover:to-white/20 px-4 py-2 rounded-lg transition-all duration-300 border border-white/30 hover:border-white/50 shadow-md hover:shadow-lg"
              >
                Clear search
              </button>
            </div>
          ) : (
            <>
              {/* List View */}
              {viewMode === "list" && (
                <div className="grid grid-cols-1 gap-0" ref={episodesContainerRef}>
                  {filteredEpisodes.map((e) => {
                    const isActive = Number(ep) === Number(e?.number);
                    // Always use string IDs for consistency
                    const animeIdStr = String(id);
                    const episodeNumStr = String(e?.number);

                    // Get episode status without excessive logging
                    const episodeWatched = isWatched(animeIdStr, episodeNumStr);
                    const progress = getProgress(animeIdStr, episodeNumStr);

                    return (
                      <EpisodeListItem
                        key={e.number}
                        episode={e}
                        isActive={isActive}
                        isWatched={episodeWatched}
                        progress={progress}
                        onClick={() => {
                          // Create a new URLSearchParams object to avoid reference issues
                          const newQuery = new URLSearchParams(window.location.search);
                          newQuery.set("ep", e?.number);
                          setQuery(newQuery);

                          // Scroll to top of page for better UX
                          window.scrollTo(0, 0);
                        }}
                      />
                    );
                  })}
                </div>
              )}

              {/* Grid View */}
              {viewMode === "grid" && (
                <div className="grid grid-cols-2 gap-0.5">
                  {filteredEpisodes.map((e) => {
                    const isActive = Number(ep) === Number(e?.number);
                    // Always use string IDs for consistency
                    const animeIdStr = String(id);
                    const episodeNumStr = String(e?.number);

                    // Get episode status without excessive logging
                    const episodeWatched = isWatched(animeIdStr, episodeNumStr);
                    const progress = getProgress(animeIdStr, episodeNumStr);

                    return (
                      <EpisodeGridItem
                        key={e.number}
                        episode={e}
                        isActive={isActive}
                        isWatched={episodeWatched}
                        progress={progress}
                        onClick={() => {
                          // Create a new URLSearchParams object to avoid reference issues
                          const newQuery = new URLSearchParams(window.location.search);
                          newQuery.set("ep", e?.number);
                          setQuery(newQuery);

                          // Scroll to top of page for better UX
                          window.scrollTo(0, 0);
                        }}
                      />
                    );
                  })}
                </div>
              )}

              {/* Episode range selector - only show if not searching and has multiple ranges */}
              {!searchQuery && totalRanges > 1 && (
                <div className="flex justify-center items-center mt-4 mb-2">
                  <div
                    className="bg-black text-white px-3 py-1.5 flex items-center justify-between cursor-pointer w-40 rounded-md"
                    onClick={() => {
                      // Cycle through ranges
                      setSelectedRange(prev => (prev + 1) % totalRanges);
                    }}
                  >
                    <span className="text-sm font-medium">
                      EPS {selectedRange * ITEMS_PER_RANGE + 1} - {Math.min((selectedRange + 1) * ITEMS_PER_RANGE, episodes.length)}
                    </span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-1">
                      <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Custom CSS for scrollbar */}
      <style jsx>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 10px;
          margin: 2px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.3));
          border-radius: 10px;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.5));
        }
        .custom-scrollbar {
          scrollbar-width: thin;
          scrollbar-color: rgba(255, 255, 255, 0.5) rgba(0, 0, 0, 0.2);
        }
      `}</style>
    </div>
  );
};
// Next Episode Countdown Component - Mobile Optimized
const NextEpisodeCountdown = memo(({ nextAiringEpisode }) => {
  const [timeRemaining, setTimeRemaining] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  useEffect(() => {
    if (!nextAiringEpisode) return;

    const calculateTimeRemaining = () => {
      const now = Math.floor(Date.now() / 1000); // Current time in seconds
      const airingAt = nextAiringEpisode.airingAt; // Airing time in seconds
      const totalSeconds = airingAt - now;

      if (totalSeconds <= 0) {
        return { days: 0, hours: 0, minutes: 0, seconds: 0 };
      }

      // Calculate days, hours, minutes, seconds
      const days = Math.floor(totalSeconds / (60 * 60 * 24));
      const hours = Math.floor((totalSeconds % (60 * 60 * 24)) / (60 * 60));
      const minutes = Math.floor((totalSeconds % (60 * 60)) / 60);
      const seconds = Math.floor(totalSeconds % 60);

      return { days, hours, minutes, seconds };
    };

    // Initial calculation
    setTimeRemaining(calculateTimeRemaining());

    // Update countdown every second
    const timer = setInterval(() => {
      const remaining = calculateTimeRemaining();
      setTimeRemaining(remaining);

      // Clear interval when countdown reaches zero
      if (remaining.days === 0 && remaining.hours === 0 &&
          remaining.minutes === 0 && remaining.seconds === 0) {
        clearInterval(timer);
      }
    }, 1000);

    // Cleanup on unmount
    return () => clearInterval(timer);
  }, [nextAiringEpisode]);

  // Early return if no data
  if (!nextAiringEpisode) return null;

  // Format the date for display - optimized for mobile
  const formatAiringDate = () => {
    const date = new Date(nextAiringEpisode.airingAt * 1000);

    // Use shorter format on small screens
    if (window.innerWidth < 640) {
      return date.toLocaleString('en-US', {
        weekday: 'short', // Short weekday name
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    }

    // Full format on larger screens
    return date.toLocaleString('en-US', {
      weekday: 'long',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  return (
    <div className="flex flex-col gap-1 sm:gap-1.5 p-1.5 sm:p-2 bg-gradient-to-r from-blue-900/30 to-purple-900/20 backdrop-blur-sm border border-blue-500/20 rounded-lg">
      {/* Header row - more compact on mobile */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-0">
        <div className="flex items-center gap-1.5">
          <Clock size={12} className="text-blue-400" />
          <div className="font-medium text-blue-100 text-[10px] sm:text-xs flex items-center gap-1">
            Next: <span className="bg-blue-500/30 text-[10px] sm:text-xs px-1.5 py-0.5 rounded">EP {nextAiringEpisode.episode}</span>
          </div>
        </div>
        <div className="text-[10px] sm:text-xs text-blue-200">
          {formatAiringDate()}
        </div>
      </div>

      {/* Countdown grid - smaller on mobile */}
      <div className="grid grid-cols-4 gap-1 w-full">
        <div className="flex flex-col items-center bg-black/40 rounded p-0.5 sm:p-1">
          <span className="text-xs sm:text-sm font-bold text-white">{timeRemaining.days}</span>
          <span className="text-[8px] sm:text-[9px] text-blue-200">days</span>
        </div>
        <div className="flex flex-col items-center bg-black/40 rounded p-0.5 sm:p-1">
          <span className="text-xs sm:text-sm font-bold text-white">{timeRemaining.hours}</span>
          <span className="text-[8px] sm:text-[9px] text-blue-200">hrs</span>
        </div>
        <div className="flex flex-col items-center bg-black/40 rounded p-0.5 sm:p-1">
          <span className="text-xs sm:text-sm font-bold text-white">{timeRemaining.minutes}</span>
          <span className="text-[8px] sm:text-[9px] text-blue-200">min</span>
        </div>
        <div className="flex flex-col items-center bg-black/40 rounded p-0.5 sm:p-1">
          <span className="text-xs sm:text-sm font-bold text-white">{timeRemaining.seconds}</span>
          <span className="text-[8px] sm:text-[9px] text-blue-200">sec</span>
        </div>
      </div>
    </div>
  );
});

// AnimeSeasons Component - Memoized and Mobile Optimized
const AnimeSeasons = memo(({ relations }) => {
  // Filter relevant relations
  const relevantRelations = relations.filter(relation =>
    ['PREQUEL', 'SEQUEL', 'ALTERNATIVE', 'SIDE_STORY', 'PARENT', 'SPIN_OFF'].includes(relation.relationType)
  );

  // Only render if there are relevant relations
  if (relevantRelations.length === 0) return null;

  // Reference to the carousel container
  const carouselRef = useRef(null);

  // Scroll handler functions
  const scrollLeft = () => {
    if (carouselRef.current) {
      // Scroll less on mobile for better control
      const scrollAmount = window.innerWidth < 640 ? -120 : -200;
      carouselRef.current.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (carouselRef.current) {
      // Scroll less on mobile for better control
      const scrollAmount = window.innerWidth < 640 ? 120 : 200;
      carouselRef.current.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  return (
    <div className="w-full mb-6">
      {/* Minimal header */}
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-base font-bold text-white uppercase">SEASONS</h2>

        {/* Arrow controls */}
        <div className="flex items-center gap-1">
          <button
            className="w-7 h-7 bg-white/5 backdrop-blur-sm border border-white/10 flex items-center justify-center rounded-full"
            onClick={scrollLeft}
            aria-label="Scroll left"
          >
            <ArrowLeft size={14} />
          </button>
          <button
            className="w-7 h-7 bg-white/5 backdrop-blur-sm border border-white/10 flex items-center justify-center rounded-full"
            onClick={scrollRight}
            aria-label="Scroll right"
          >
            <ArrowRight size={14} />
          </button>
        </div>
      </div>

      {/* Single line glassy carousel - optimized for mobile */}
      <div className="relative">
        {/* Main carousel container */}
        <div
          id="relations-carousel"
          ref={carouselRef}
          className="flex overflow-x-auto hide-scrollbar pb-2 gap-2 sm:gap-3 snap-x snap-mandatory"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {relevantRelations.map((relation, index) => (
            <Link
              key={index}
              to={`/anime/${relation.id}`}
              className="group relative flex-shrink-0 w-[140px] sm:w-[180px] snap-start transform transition-all duration-300 hover:-translate-y-1 hover:z-10 active:scale-95 touch-manipulation"
            >
              {/* Glassy card - smaller height on mobile */}
              <div className="relative h-[200px] sm:h-[240px] overflow-hidden rounded-md bg-white/5 backdrop-blur-sm border border-white/10 group-hover:border-white/40 group-hover:shadow-[0_0_15px_rgba(255,255,255,0.15)] transition-all duration-300">
                {/* Image - smaller height on mobile */}
                <div className="relative h-[100px] sm:h-[140px] overflow-hidden">
                  <Image
                    src={relation.images?.coverLarge || relation.images?.bannerLarge}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                    quality="low" // Lower quality on mobile for better performance
                    loading="lazy" // Lazy load images for better performance
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent"></div>

                  {/* Relation type badge - smaller on mobile */}
                  <div className="absolute top-1 sm:top-2 left-1 sm:left-2 bg-black/40 backdrop-blur-md px-1.5 sm:px-2 py-0.5 rounded-full text-[8px] sm:text-[9px] font-medium border border-white/10 group-hover:bg-white/20 group-hover:border-white/30 transition-all duration-300">
                    {relation.relationType === 'PREQUEL' ? 'Previous' :
                     relation.relationType === 'SEQUEL' ? 'Next' :
                     relation.relationType === 'SIDE_STORY' ? 'Side' :
                     relation.relationType === 'ALTERNATIVE' ? 'Alt' :
                     relation.relationType === 'PARENT' ? 'Main' :
                     relation.relationType === 'SPIN_OFF' ? 'Spin-Off' :
                     relation.relationType.replace(/_/g, ' ')}
                  </div>
                </div>

                {/* Content - smaller padding on mobile */}
                <div className="p-2 sm:p-3">
                  <h3 className="text-xs sm:text-sm font-bold text-white line-clamp-2 mb-1 group-hover:text-white/90 transition-colors">
                    {relation.title}
                  </h3>

                  <div className="flex items-center justify-between mt-1 sm:mt-2">
                    {relation.format && (
                      <span className="text-[8px] sm:text-[10px] text-gray-400 bg-black/20 backdrop-blur-sm px-1.5 sm:px-2 py-0.5 rounded-full group-hover:bg-white/10 transition-colors duration-300">
                        {relation.format.replace(/_/g, ' ')}
                      </span>
                    )}
                    {relation.episodes && (
                      <span className="text-[8px] sm:text-[10px] text-gray-400 bg-black/20 backdrop-blur-sm px-1.5 sm:px-2 py-0.5 rounded-full group-hover:bg-white/10 transition-colors duration-300">
                        {relation.episodes} EP
                      </span>
                    )}
                  </div>
                </div>

                {/* Glassy highlight effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/30 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              </div>
            </Link>
          ))}
        </div>

        {/* Gradient fade effect on edges */}
        <div className="absolute top-0 bottom-0 left-0 w-4 sm:w-6 bg-gradient-to-r from-black to-transparent pointer-events-none"></div>
        <div className="absolute top-0 bottom-0 right-0 w-4 sm:w-6 bg-gradient-to-l from-black to-transparent pointer-events-none"></div>
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function to prevent unnecessary re-renders
  // Only re-render if the relations array has changed
  return JSON.stringify(prevProps.relations) === JSON.stringify(nextProps.relations);
});

// Info Component - Memoized
const Info = memo(({ info }) => {
  const [d, sd] = useState(false);

  return info ? (
    <div className="flex w-full flex-col gap-4">
      <div className="bg-gradient-to-br from-black/90 to-black/70 backdrop-blur-md rounded-xl overflow-hidden border border-white/10">
        {/* Top Banner Area */}
        <div className="relative h-40 sm:h-48 overflow-hidden">
          <Image
            src={info?.images?.bannerLarge || info?.images?.coverLarge}
            className="object-cover w-full h-full filter grayscale-[30%]"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/30 to-black"></div>

          {/* Top Bar with Type and Status */}
          <div className="absolute top-0 inset-x-0 p-3 flex justify-between items-center">
            <div className="flex gap-2">
              {info?.type && (
                <span className="bg-white text-black text-xs px-2 py-0.5 font-medium">
                  {info.type}
                </span>
              )}
              {info?.status && (
                <span className="bg-white/10 backdrop-blur-sm text-white/90 text-xs px-2 py-0.5">
                  {info.status}
                </span>
              )}
            </div>

            {info?.rating && (
              <div className="bg-white/10 backdrop-blur-sm px-2 py-0.5 rounded flex items-center gap-1">
                <Star fill="white" color="white" size={12} />
                <span className="text-xs font-medium">{info.rating}</span>
              </div>
            )}
          </div>
        </div>

        {/* Content Area */}
        <div className="px-4 sm:px-6 pb-5">
          {/* Cover and Info Row */}
          <div className="flex flex-col sm:flex-row gap-4 -mt-20 sm:-mt-24 mb-5">
            {/* Cover Image */}
            <div className="flex-shrink-0 mx-auto sm:mx-0">
              <div className="relative w-[130px] h-[195px] sm:w-[140px] sm:h-[210px] rounded-md overflow-hidden border border-white/20 shadow-[0_5px_15px_rgba(0,0,0,0.35)]">
                <Image
                  src={info?.images?.coverLarge}
                  className="object-cover w-full h-full"
                />
              </div>
            </div>

            {/* Anime Info */}
            <div className="flex flex-col flex-grow text-center sm:text-left sm:pt-24">
              <h1 className="text-xl sm:text-2xl font-bold mb-4">{info?.title}</h1>

              {/* Quick Stats */}
              <div className="flex flex-wrap justify-center sm:justify-start gap-x-4 gap-y-2 text-xs text-white/70 mb-5">
                {info?.release_date && (
                  <div className="flex items-center gap-1">
                    <Calendar size={12} className="text-white/50" />
                    <span>{info.release_date}</span>
                  </div>
                )}

                <div className="flex items-center gap-1">
                  <Clock size={12} className="text-white/50" />
                  <span>{info?.episodes || 0} Episodes</span>
                </div>

                {info?.studios && info.studios.length > 0 && (
                  <div className="flex items-center gap-1">
                    <Building size={12} className="text-white/50" />
                    <Link
                      to={`/explore?studio=${info.studios[0].id}`}
                      className="hover:text-blue-400 transition-colors cursor-pointer"
                    >
                      {info.studios[0].name}
                    </Link>
                  </div>
                )}
              </div>

              {/* Action Button */}
              <div className="flex justify-center sm:justify-start">
                <Link
                  to={`/anime/${info?.id}`}
                  className="bg-white hover:bg-white/90 text-black px-4 py-1.5 rounded flex items-center gap-1.5 font-medium transition-colors"
                >
                  <InfoIcon size={16} /> View Details
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Description */}
      <div className="bg-[#0f0f0f] rounded-xl p-4 border border-white/5">
        <h3 className="text-sm font-medium text-white/80 mb-2">Synopsis</h3>
        <div
          className={`text-sm text-white/70 ${d ? "" : "line-clamp-3"}`}
          dangerouslySetInnerHTML={{ __html: info?.description || "" }}
        />
        {info?.description && (
          <button
            onClick={() => sd(prev => !prev)}
            className="text-xs text-blue-400 hover:text-blue-300 mt-2"
          >
            {d ? "Show Less" : "Show More"}
          </button>
        )}
      </div>
    </div>
  ) : null;
}, (prevProps, nextProps) => {
  // Only re-render if the info object has changed
  // This is a simple comparison that works for most cases
  return JSON.stringify(prevProps.info) === JSON.stringify(nextProps.info);
});
