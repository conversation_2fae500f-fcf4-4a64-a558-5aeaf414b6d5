import express from 'express';
import userProfileController from '../controllers/userProfileController.js';
import { verifyAniListToken, checkUserMatch } from '../middleware/auth.js';

const router = express.Router();

/**
 * Get user profile
 * GET /api/profile/:userId
 */
router.get('/profile/:userId', userProfileController.getUserProfile);

/**
 * Update user profile
 * PUT /api/profile/:userId
 */
router.put('/profile/:userId', verifyAniListToken, checkUserMatch, userProfileController.updateUserProfile);

/**
 * Record a visit and update streak
 * POST /api/profile/:userId/visit
 */
router.post('/profile/:userId/visit', verifyAniListToken, checkUserMatch, userProfileController.recordVisit);

/**
 * Add XP
 * POST /api/profile/:userId/xp
 */
router.post('/profile/:userId/xp', verifyAniListToken, checkUser<PERSON>atch, userProfileController.addXp);

/**
 * Add like
 * POST /api/profile/:userId/like
 */
router.post('/profile/:userId/like', userProfileController.addLike);

/**
 * Get daily tasks
 * GET /api/profile/:userId/tasks
 */
router.get('/profile/:userId/tasks', verifyAniListToken, checkUserMatch, userProfileController.getDailyTasks);

/**
 * Complete a task
 * POST /api/profile/:userId/tasks/:taskId/complete
 */
router.post('/profile/:userId/tasks/:taskId/complete', verifyAniListToken, checkUserMatch, userProfileController.completeTask);

/**
 * Unlock achievement
 * POST /api/profile/:userId/achievements
 */
router.post('/profile/:userId/achievements', verifyAniListToken, checkUserMatch, userProfileController.unlockAchievement);

export default router;
