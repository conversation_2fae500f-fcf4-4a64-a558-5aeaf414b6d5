# Anime Streaming Backend

This is a backend server for streaming anime torrents using nyaa.si and WebTorrent.

## Setup

1. Install dependencies:
   ```
   npm install
   ```

2. Run the setup script to install nyaa.si-client and create necessary directories:
   ```
   node setup.js
   ```

3. Start the server:
   ```
   npm start
   ```

   Or for development with auto-restart:
   ```
   npm run dev
   ```

## API Endpoints

### Search for torrents
```
GET /api/search?anime=<anime_title>&episode=<episode_number>
```

### Find the best torrent for an anime episode
```
GET /api/best-torrent?anime=<anime_title>&episode=<episode_number>
```

### Add a torrent
```
POST /api/torrent
Body: { "magnetURI": "<magnet_uri>" }
```

### Get torrent info
```
GET /api/torrent/<info_hash>
```

### Stream a file from a torrent
```
GET /api/stream/<info_hash>/<file_index>
```

### Remove a torrent
```
DELETE /api/torrent/<info_hash>
```

## Notes

- Torrents are automatically removed after 1 hour of inactivity
- Downloaded files are stored in the `downloads` directory
- The server runs on port 3001 by default
