/**
 * Fetches the anime schedule for the current week using AniList API
 * @param {string} filter - The filter type (sub, dub, all) - used for UI consistency
 * @returns {Promise<Array>} - The anime schedule data
 */
export const getAnimeSchedule = async (filter = "sub") => {
  try {
    console.log("Fetching anime schedule from AniList API...");

    // Get the current date and next 14 days (to ensure we get a full week)
    const today = new Date();

    // Set today to the beginning of the day (midnight)
    today.setHours(0, 0, 0, 0);

    const twoWeeksLater = new Date(today);
    twoWeeksLater.setDate(today.getDate() + 14);
    // Set to end of day
    twoWeeksLater.setHours(23, 59, 59, 999);

    // Convert to Unix timestamp (seconds)
    const startTime = Math.floor(today.getTime() / 1000);
    const endTime = Math.floor(twoWeeksLater.getTime() / 1000);

    // AniList GraphQL query
    const query = `
      query ($startTime: Int, $endTime: Int) {
        Page(page: 1, perPage: 200) {
          airingSchedules(airingAt_greater: $startTime, airingAt_lesser: $endTime, sort: [TIME, EPISODE]) {
            id
            airingAt
            episode
            media {
              id
              title {
                romaji
                english
                native
              }
              coverImage {
                large
                medium
              }
              episodes
              status
              averageScore
              isAdult
              nextAiringEpisode {
                airingAt
                episode
              }
              format
              genres
              studios {
                nodes {
                  name
                }
              }
            }
          }
        }
      }
    `;

    const variables = {
      startTime,
      endTime
    };

    // Fetch multiple pages of results
    let processedData = [];
    let hasMorePages = true;
    let currentPage = 1;

    while (hasMorePages && currentPage <= 3) { // Limit to 3 pages to avoid excessive requests
      const response = await fetch('https://graphql.anilist.co', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          query: query.replace('Page(page: 1,', `Page(page: ${currentPage},`),
          variables
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch anime schedule: ${response.status}`);
      }

      const data = await response.json();
      console.log(`AniList API response for page ${currentPage}:`, data);

      // Check if we have results
      const schedules = data.data?.Page?.airingSchedules || [];
      if (schedules.length < 200) {
        hasMorePages = false;
      }

      // Process this page's data
      schedules.forEach(schedule => {
        // Skip NSFW content
        if (schedule.media.isAdult) {
          return;
        }

        const airingDate = new Date(schedule.airingAt * 1000);
        const dayName = new Intl.DateTimeFormat('en-US', { weekday: 'long' }).format(airingDate);

        // Create a unique ID that includes the episode number to prevent duplicates
        const uniqueId = `${schedule.media.id}-${schedule.episode}`;

        // Check if we already have this exact episode in our data
        const isDuplicate = processedData.some(item =>
          item.route === schedule.media.id.toString() &&
          item.episodeNumber === schedule.episode
        );

        // Skip if it's a duplicate
        if (isDuplicate) {
          return;
        }

        processedData.push({
          id: uniqueId,
          title: schedule.media.title.english || schedule.media.title.romaji,
          route: schedule.media.id.toString(),
          episodeNumber: schedule.episode,
          totalEpisodes: schedule.media.episodes || "?",
          episodeDate: airingDate.toISOString(),
          airingStatus: "airing",
          imageVersionRoute: schedule.media.coverImage.large || schedule.media.coverImage.medium,
          day: dayName,
          score: schedule.media.averageScore ? (schedule.media.averageScore / 10).toFixed(1) : null,
          type: schedule.media.format || "Unknown",
          genres: schedule.media.genres || [],
          studios: schedule.media.studios?.nodes?.map(s => s.name) || [],
          url: `https://anilist.co/anime/${schedule.media.id}`
        });
      });

      currentPage++;
    }

    console.log(`Total processed anime: ${processedData.length}`);
    return processedData;
  } catch (error) {
    console.error("Error fetching anime schedule:", error);
    return [];
  }
};

/**
 * Groups anime schedule by day of the week
 * @param {Array} schedule - The anime schedule data
 * @returns {Object} - The schedule grouped by day
 */
export const groupScheduleByDay = (schedule) => {
  const days = {
    Monday: [],
    Tuesday: [],
    Wednesday: [],
    Thursday: [],
    Friday: [],
    Saturday: [],
    Sunday: [],
    Unknown: []
  };

  if (!Array.isArray(schedule)) return days;

  // Group anime by their ID to handle duplicates
  const animeById = {};

  // Group anime by their ID first
  schedule.forEach(anime => {
    const animeId = anime.route;
    const episodeNum = anime.episodeNumber;

    // Create a unique key for this anime episode
    const key = `${animeId}-${episodeNum}`;

    // If we haven't processed this anime episode yet, or if this is a newer entry
    if (!animeById[key] || new Date(anime.episodeDate) > new Date(animeById[key].episodeDate)) {
      animeById[key] = anime;
    }
  });

  // Now process each unique anime and assign to the correct day
  Object.values(animeById).forEach(anime => {
    const day = anime.day;

    if (!day) return;

    // Capitalize the first letter of the day
    const formattedDay = day.charAt(0).toUpperCase() + day.slice(1).toLowerCase();

    // Skip if not a valid day
    if (!days[formattedDay] && formattedDay !== "Unknown") return;

    // Add to the appropriate day
    if (days[formattedDay]) {
      days[formattedDay].push(anime);
    } else {
      days.Unknown.push(anime);
    }
  });

  // Sort each day's anime by title
  Object.keys(days).forEach(day => {
    days[day].sort((a, b) => a.title.localeCompare(b.title));
  });

  // Log the count of anime for each day
  console.log("Anime count by day:");
  Object.keys(days).forEach(day => {
    console.log(`${day}: ${days[day].length} anime`);
  });

  return days;
};
