import { useState, useEffect } from 'react';
import { Clock } from 'lucide-react';

const LiveClock = () => {
  const [time, setTime] = useState(new Date());
  const [timezone, setTimezone] = useState('');

  useEffect(() => {
    // Update time every second
    const timer = setInterval(() => {
      setTime(new Date());
    }, 1000);

    // Get timezone name
    try {
      const timezoneName = Intl.DateTimeFormat().resolvedOptions().timeZone;
      setTimezone(timezoneName);
    } catch (error) {
      console.error('Error getting timezone:', error);
      setTimezone('');
    }

    // Clean up interval on unmount
    return () => clearInterval(timer);
  }, []);

  // Format time as HH:MM:SS
  const formattedTime = time.toLocaleTimeString();

  // Format hours and minutes separately for styling
  const hours = time.getHours().toString().padStart(2, '0');
  const minutes = time.getMinutes().toString().padStart(2, '0');
  const seconds = time.getSeconds().toString().padStart(2, '0');

  // Format date as Day, Month Date, Year
  const formattedDate = time.toLocaleDateString(undefined, {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <div className="relative overflow-hidden rounded-xl bg-black/40 backdrop-blur-sm border border-yellow-500/20 p-3 text-center">
      <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/10 to-black/20 z-0"></div>

      <div className="relative z-10">
        <div className="flex items-center justify-center gap-2 text-2xl font-bold">
          <div className="flex items-center">
            <span className="bg-yellow-500/20 text-yellow-500 px-2 py-1 rounded-lg">{hours}</span>
            <span className="mx-1 text-yellow-500">:</span>
            <span className="bg-yellow-500/20 text-yellow-500 px-2 py-1 rounded-lg">{minutes}</span>
            <span className="mx-1 text-yellow-500">:</span>
            <span className="bg-yellow-500/20 text-yellow-500 px-2 py-1 rounded-lg">{seconds}</span>
          </div>
        </div>

        <div className="text-sm text-white mt-2 font-medium">{formattedDate}</div>
        {timezone && (
          <div className="flex items-center justify-center gap-1 mt-1">
            <Clock size={12} className="text-yellow-500" />
            <span className="text-xs text-yellow-500">{timezone}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default LiveClock;
