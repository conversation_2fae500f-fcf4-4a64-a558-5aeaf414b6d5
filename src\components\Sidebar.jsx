import { Bookmark, Clock, User, LogIn, LogOut, Home, Film, Calendar, Flame, Heart, Compass, ChevronRight, X, Sparkles, Settings, ChevronDown, Bell, Info, HelpCircle, FileText, DollarSign, MessageCircleQuestion, Radio, ImageIcon, Trophy } from "lucide-react";
import { useEffect, useState } from "react";
import { Link, useLocation } from "react-router-dom";
import SearchSnippet from "./search/SearchSnippet";
import SimpleChangelogButton from "./ui/SimpleChangelogButton";
import TitlePreferenceToggle from "./TitlePreferenceToggle";
import { useAniList } from "@/hooks/useAniList";
import Image from "./ui/Image";
import { useMainContext } from "@/context/MainContext";
import NotificationIcon from "./notifications/NotificationIcon";

const Sidebar = ({ isOpen, setIsOpen }) => {
  const location = useLocation();
  const { user, isAuthenticated, login, logout } = useAniList();
  const { hideHeader } = useMainContext();
  const [isMobile, setIsMobile] = useState(false);
  const isSidebarOpen = isOpen !== undefined ? isOpen : false;

  // Check if device is mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    // Initial check
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);

    // Cleanup
    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  // Main navigation items
  const navItems = [
    { path: "/", label: "Home", icon: <Home size={18} /> },
    { path: "/shorts", label: "Shorts", icon: <Film size={18} /> },
    { path: "/live", label: "Live Stream", icon: <Radio size={18} /> },
    { path: "/explore", label: "Explore", icon: <Compass size={18} /> },
    { path: "/trending", label: "Most Popular", icon: <Flame size={18} /> },
    { path: "/recommendations", label: "Yor's Anime Picks", icon: <Sparkles size={18} /> },
    { path: "/image-search", label: "Find by Image", icon: <ImageIcon size={18} /> },
    { path: "/schedule", label: "Schedule", icon: <Calendar size={18} /> },
    { path: "/leaderboard", label: "Leaderboard", icon: <Trophy size={18} /> },
  ];

  // Additional items for categories section
  const categoryItems = [
    { path: "/explore?genre=Action", label: "Action", icon: null },
    { path: "/explore?genre=Romance", label: "Romance", icon: <Heart size={18} /> },
    { path: "/explore?genre=Fantasy", label: "Fantasy", icon: null },
    { path: "/explore?genre=Comedy", label: "Comedy", icon: null },
  ];

  // User-related items
  const userItems = [
    { path: "/history", label: "History", icon: <Clock size={18} /> },
    { path: "/watchlist", label: "Watchlist", icon: <Bookmark size={18} /> },
    {
      component: (
        <div className="flex items-center gap-2 px-2 py-1.5 rounded-md transition-all duration-200 text-gray-300 hover:bg-white/5 hover:text-white group">
          <span className="text-white"><Bell size={18} /></span>
          <span className="flex-1">Notifications</span>
          <div className="relative z-[9999]">
            <NotificationIcon />
          </div>
        </div>
      )
    },
    { path: "/profile-new", label: "Profile", icon: <User size={20} /> },
  ];

  if (hideHeader) return null;

  // Check if the current path matches a nav item
  const isActive = (path) => {
    if (path === "/" && location.pathname === "/") return true;
    if (path !== "/" && location.pathname.includes(path.split("?")[0])) return true;
    return false;
  };

  const closeSidebar = () => {
    if (setIsOpen) {
      setIsOpen(false);
    }
  };

  return (
    <>
      {/* Sidebar overlay for mobile */}
      {isMobile && (
        <div
          className={`fixed inset-0 z-40 bg-black/60 backdrop-blur-sm transition-opacity duration-300 lg:hidden ${
            isSidebarOpen ? "opacity-100" : "opacity-0 pointer-events-none"
          }`}
          onClick={closeSidebar}
        ></div>
      )}

      {/* Sidebar */}
      <aside
        className={`fixed top-0 left-0 h-full z-50 bg-black/80 backdrop-blur-xl border-r border-white/10 transition-all duration-300 ${
          isMobile
            ? isSidebarOpen
              ? "translate-x-0 opacity-100"
              : "-translate-x-full opacity-0"
            : "translate-x-0 opacity-100"
        } ${isMobile ? "w-64" : "w-56"}`}
      >
        <div className="flex flex-col h-full overflow-y-auto">
          {/* Logo */}
          <div className="p-4 flex items-center justify-between">
            <Link
              className="flex items-center"
              to={"/"}
              onClick={closeSidebar}
            >
              <div className="flex items-center gap-2">
                {/* Icon */}
                <div className="bg-white/10 rounded-lg w-8 h-8 overflow-hidden">
                  <img
                    src="https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png"
                    alt="AnimeHQ Icon"
                    className="w-full h-full object-cover"
                  />
                </div>
                {/* Text */}
                <div className="flex flex-col">
                  <span className="font-bold text-base text-white">
                    AnimeHQ
                  </span>
                  <span className="text-[10px] text-white/70 -mt-1">
                    Platform
                  </span>
                </div>
              </div>
            </Link>
            {isMobile && (
              <button onClick={closeSidebar} className="text-gray-400 hover:text-white">
                <X size={18} />
              </button>
            )}
          </div>

          {/* Search */}
          <div className="px-2 mb-2">
            <SearchSnippet closeSidebar={closeSidebar} />
          </div>

          {/* Main Navigation */}
          <div className="px-2 mb-4">
            <h3 className="text-[10px] font-semibold text-gray-400 uppercase tracking-wider px-2 mb-1">
              Navigation
            </h3>
            <nav className="space-y-0.5">
              {navItems.map((item) => (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={closeSidebar}
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md transition-all duration-200 text-sm ${
                    isActive(item.path)
                      ? "bg-white/10 text-white font-medium"
                      : "text-gray-300 hover:bg-white/5 hover:text-white"
                  }`}
                >
                  <span className="text-white">{item.icon}</span>
                  {item.label}
                </Link>
              ))}
            </nav>
          </div>

          {/* User Section */}
          <div className="px-2 mb-4">
            <h3 className="text-[10px] font-semibold text-gray-400 uppercase tracking-wider px-2 mb-1">
              Your Library
            </h3>
            <nav className="space-y-0.5">
              {isAuthenticated ? (
                userItems.map((item, index) => (
                  item.component ? (
                    <div key={`custom-${index}`} className="text-sm">{item.component}</div>
                  ) : (
                    <Link
                      key={item.path}
                      to={item.path}
                      onClick={closeSidebar}
                      className={`flex items-center gap-2 px-2 py-1.5 rounded-md transition-all duration-200 text-sm ${
                        isActive(item.path)
                          ? "bg-white/10 text-white font-medium"
                          : "text-gray-300 hover:bg-white/5 hover:text-white"
                      }`}
                    >
                      <span className="text-white">{item.icon}</span>
                      {item.label}
                    </Link>
                  )
                ))
              ) : (
                <button
                  onClick={login}
                  className="flex w-full items-center gap-2 px-2 py-1.5 rounded-md text-gray-300 hover:bg-white/5 hover:text-white transition-all duration-200 text-sm"
                >
                  <LogIn size={18} className="text-white" />
                  <span>AniList Login</span>
                </button>
              )}
            </nav>
          </div>

          {/* Info Pages Section */}
          <div className="px-2 mb-4">
            <h3 className="text-[10px] font-semibold text-gray-400 uppercase tracking-wider px-2 mb-1">
              Information
            </h3>
            <nav className="space-y-0.5">
              <Link
                to="/about"
                onClick={closeSidebar}
                className={`flex items-center gap-2 px-2 py-1.5 rounded-md transition-all duration-200 text-sm ${
                  isActive("/about")
                    ? "bg-white/10 text-white font-medium"
                    : "text-gray-300 hover:bg-white/5 hover:text-white"
                }`}
              >
                <Info size={18} className="text-white" />
                About Us
              </Link>
              <Link
                to="/donate"
                onClick={closeSidebar}
                className={`flex items-center gap-2 px-2 py-1.5 rounded-md transition-all duration-200 text-sm ${
                  isActive("/donate")
                    ? "bg-white/10 text-white font-medium"
                    : "text-gray-300 hover:bg-white/5 hover:text-white"
                }`}
              >
                <DollarSign size={18} className="text-white" />
                Support Us
              </Link>
              <Link
                to="/faq"
                onClick={closeSidebar}
                className={`flex items-center gap-2 px-2 py-1.5 rounded-md transition-all duration-200 text-sm ${
                  isActive("/faq")
                    ? "bg-white/10 text-white font-medium"
                    : "text-gray-300 hover:bg-white/5 hover:text-white"
                }`}
              >
                <MessageCircleQuestion size={18} className="text-white" />
                FAQ
              </Link>
              <Link
                to="/terms"
                onClick={closeSidebar}
                className={`flex items-center gap-2 px-2 py-1.5 rounded-md transition-all duration-200 text-sm ${
                  isActive("/terms")
                    ? "bg-white/10 text-white font-medium"
                    : "text-gray-300 hover:bg-white/5 hover:text-white"
                }`}
              >
                <FileText size={18} className="text-white" />
                Terms
              </Link>
              <Link
                to="/privacy"
                onClick={closeSidebar}
                className={`flex items-center gap-2 px-2 py-1.5 rounded-md transition-all duration-200 text-sm ${
                  isActive("/privacy")
                    ? "bg-white/10 text-white font-medium"
                    : "text-gray-300 hover:bg-white/5 hover:text-white"
                }`}
              >
                <HelpCircle size={18} className="text-white" />
                Privacy
              </Link>
            </nav>
          </div>

          {/* Bottom Section */}
          <div className="mt-auto px-2 pb-4">
            <div className="space-y-1.5">
              {/* Title Preference Toggle */}
              <div className="px-2 py-1.5">
                <TitlePreferenceToggle />
              </div>

              {/* Changelog Button */}
              <div className="px-2 py-1.5">
                <SimpleChangelogButton
                  className="flex items-center gap-1.5 px-2 py-1.5 bg-white/10 hover:bg-white/15 rounded-md text-xs transition-colors w-full"
                  iconOnly={false}
                />
              </div>

              {/* User Profile */}
              <div className="mt-1.5">
                {isAuthenticated ? (
                  <div className="relative group">
                    <button className="flex items-center justify-between w-full p-2 bg-black/60 hover:bg-black/80 rounded-md border border-white/20 transition-colors">
                      <div className="flex items-center gap-2">
                        <div className="flex items-center justify-center size-7 bg-white/10 rounded-full overflow-hidden border border-white/10">
                          {user?.avatar?.medium ? (
                            <Image src={user?.avatar?.medium} className="object-cover" />
                          ) : (
                            <User size={14} className="text-white" />
                          )}
                        </div>
                        <div className="flex flex-col items-start">
                          <span className="font-medium text-xs text-white">{user?.name || "User"}</span>
                          <span className="text-[10px] text-white/70">AniList User</span>
                        </div>
                      </div>
                      <ChevronDown size={14} className="text-white/70" />
                    </button>

                    {/* Dropdown menu */}
                    <div className="absolute right-0 mt-1 w-full invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-all duration-200 z-50">
                      <div className="bg-black/80 backdrop-blur-md border border-white/20 rounded-md shadow-xl overflow-hidden">
                        <Link to="/profile-new" className="flex items-center gap-2 p-2 hover:bg-white/10 w-full text-left text-xs text-white">
                          <User size={14} className="text-white/70" />
                          <span>Profile</span>
                        </Link>
                        <Link to="/settings" className="flex items-center gap-2 p-2 hover:bg-white/10 w-full text-left text-xs text-white">
                          <Settings size={14} className="text-white/70" />
                          <span>Settings</span>
                        </Link>
                        <button onClick={logout} className="flex items-center gap-2 p-2 hover:bg-white/10 w-full text-left text-xs text-white">
                          <LogOut size={14} className="text-white/70" />
                          <span>Sign Out</span>
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="relative group">
                    <button className="flex items-center justify-between w-full p-2 bg-black/60 hover:bg-black/80 rounded-md border border-white/20 transition-colors">
                      <div className="flex items-center gap-2">
                        <div className="flex items-center justify-center size-7 bg-white/10 rounded-full overflow-hidden border border-white/10">
                          <User size={14} className="text-white" />
                        </div>
                        <div className="flex flex-col items-start">
                          <span className="font-medium text-xs text-white">Guest</span>
                          <span className="text-[10px] text-white/70">N/A</span>
                        </div>
                      </div>
                      <ChevronDown size={14} className="text-white/70" />
                    </button>

                    {/* Dropdown menu */}
                    <div className="absolute right-0 mt-1 w-full invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-all duration-200 z-50">
                      <div className="bg-black/80 backdrop-blur-md border border-white/20 rounded-md shadow-xl overflow-hidden">
                        <button onClick={login} className="flex items-center gap-2 p-2 hover:bg-white/10 w-full text-left text-xs text-white">
                          <LogIn size={14} className="text-white/70" />
                          <span>Sign In</span>
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
