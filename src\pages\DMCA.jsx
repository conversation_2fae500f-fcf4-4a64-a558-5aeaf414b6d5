import { useEffect } from "react";

const DMCA = () => {
  // Set page title
  useEffect(() => {
    document.title = "DMCA Policy | AnimeHQ";
    return () => {
      document.title = "AnimeHQ"; // Reset title when component unmounts
    };
  }, []);

  return (
    <div className="py-8 px-4 sm:px-6 lg:px-8 max-w-5xl mx-auto">
      <h1 className="text-3xl font-bold mb-8 text-center">DMCA Policy</h1>
      
      <div className="prose prose-invert max-w-none">
        <p className="text-gray-300 mb-6">
          Last updated: {new Date().toLocaleDateString()}
        </p>

        <h2 className="text-xl font-semibold mt-8 mb-4">DMCA Notice & Takedown Policy</h2>
        <p className="text-gray-300 mb-4">
          AnimeHQ respects the intellectual property rights of others and expects its users to do the same. In accordance with the Digital Millennium Copyright Act of 1998 ("DMCA"), we will respond expeditiously to claims of copyright infringement that are reported to our designated copyright agent.
        </p>

        <h2 className="text-xl font-semibold mt-8 mb-4">Notification of Claimed Infringement</h2>
        <p className="text-gray-300 mb-4">
          If you believe that your copyrighted work has been copied in a way that constitutes copyright infringement and is accessible on AnimeHQ, please notify our copyright agent as set forth in the DMCA. For your complaint to be valid under the DMCA, you must provide the following information when providing notice of the claimed copyright infringement:
        </p>
        
        <ol className="list-decimal pl-6 text-gray-300 mb-4">
          <li className="mb-2">A physical or electronic signature of a person authorized to act on behalf of the copyright owner.</li>
          <li className="mb-2">Identification of the copyrighted work claimed to have been infringed.</li>
          <li className="mb-2">Identification of the material that is claimed to be infringing or to be the subject of the infringing activity and that is to be removed or access to which is to be disabled, and information reasonably sufficient to permit us to locate the material.</li>
          <li className="mb-2">Information reasonably sufficient to permit us to contact the complaining party, such as an address, telephone number, and, if available, an electronic mail address at which the complaining party may be contacted.</li>
          <li className="mb-2">A statement that the complaining party has a good faith belief that use of the material in the manner complained of is not authorized by the copyright owner, its agent, or the law.</li>
          <li className="mb-2">A statement that the information in the notification is accurate, and under penalty of perjury, that the complaining party is authorized to act on behalf of the owner of an exclusive right that is allegedly infringed.</li>
        </ol>

        <p className="text-gray-300 mb-4">
          The above information must be submitted as a written notification to our designated copyright agent at:
        </p>

        <div className="bg-white/5 p-4 rounded-lg text-gray-300 mb-6">
          <p>DMCA Complaints</p>
          <p>AnimeHQ</p>
          <p>Email: <EMAIL></p>
        </div>

        <h2 className="text-xl font-semibold mt-8 mb-4">Counter-Notification</h2>
        <p className="text-gray-300 mb-4">
          If you believe that your content that was removed (or to which access was disabled) is not infringing, or that you have the authorization from the copyright owner, the copyright owner's agent, or pursuant to the law, to post and use the material in your content, you may send a counter-notification containing the following information to our copyright agent:
        </p>

        <ol className="list-decimal pl-6 text-gray-300 mb-4">
          <li className="mb-2">Your physical or electronic signature.</li>
          <li className="mb-2">Identification of the content that has been removed or to which access has been disabled and the location at which the content appeared before it was removed or disabled.</li>
          <li className="mb-2">A statement that you have a good faith belief that the content was removed or disabled as a result of mistake or a misidentification of the content.</li>
          <li className="mb-2">Your name, address, telephone number, and email address, and a statement that you consent to the jurisdiction of the federal court for the judicial district in which your address is located, or if your address is outside of the United States, for any judicial district in which AnimeHQ may be found, and that you will accept service of process from the person who provided notification of the alleged infringement.</li>
        </ol>

        <h2 className="text-xl font-semibold mt-8 mb-4">Repeat Infringers</h2>
        <p className="text-gray-300 mb-4">
          It is our policy to terminate the user accounts of repeat infringers. We process and investigate notices of alleged infringement and will take appropriate actions under the DMCA and other applicable intellectual property laws.
        </p>

        <h2 className="text-xl font-semibold mt-8 mb-4">Changes to Our DMCA Policy</h2>
        <p className="text-gray-300 mb-4">
          We may update our DMCA Policy from time to time. We will notify you of any changes by posting the new DMCA Policy on this page and updating the "last updated" date.
        </p>

        <h2 className="text-xl font-semibold mt-8 mb-4">Contact Us</h2>
        <p className="text-gray-300 mb-4">
          If you have any questions about this DMCA Policy, please contact <NAME_EMAIL>.
        </p>
      </div>
    </div>
  );
};

export default DMCA;
