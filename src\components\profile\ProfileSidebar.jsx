import { useState } from "react";
import { useAniList } from "@/hooks/useAniList";
import { useUserActivity } from "@/context/UserActivityContext";
import RankSystem from "./RankSystem";
import TaskSystem from "./TaskSystem";
import StreakCounter from "./StreakCounter";
import LikesCounter from "./LikesCounter";
import { Dices, BarChart2, Clock, Award, ListChecks } from "lucide-react";

const ProfileSidebar = () => {
  const { user, isAuthenticated } = useAniList();
  const { level, rank, xp } = useUserActivity();
  const [activeTab, setActiveTab] = useState("stats");

  if (!isAuthenticated) {
    return (
      <div className="w-full lg:w-80 bg-black/20 rounded-lg p-4">
        <p className="text-white/60 text-sm">Please log in to view your profile.</p>
      </div>
    );
  }

  const tabs = [
    { id: "stats", label: "Stats", icon: <BarChart2 size={16} /> },
    { id: "tasks", label: "Tasks", icon: <ListChecks size={16} /> },
    { id: "achievements", label: "Achievements", icon: <Award size={16} /> },
    { id: "history", label: "History", icon: <Clock size={16} /> }
  ];

  return (
    <div className="w-full lg:w-80 bg-black/20 rounded-lg overflow-hidden flex flex-col">
      {/* Profile header */}
      <div className="p-4 bg-black/30">
        <div className="flex items-center gap-3">
          <img
            src={user.avatar.medium}
            alt={user.name}
            className="w-12 h-12 rounded-full border-2 border-white/10"
          />
          <div>
            <h3 className="font-medium">{user.name}</h3>
            <div className="flex items-center gap-1 text-xs text-white/60">
              <span className="bg-black/30 px-1.5 py-0.5 rounded">Lvl {level}</span>
              <span className="bg-black/30 px-1.5 py-0.5 rounded">{rank}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Tab navigation */}
      <div className="flex border-b border-white/10">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`flex-1 py-2 text-xs font-medium flex flex-col items-center gap-1
              ${activeTab === tab.id
                ? "text-white border-b-2 border-blue-500"
                : "text-white/60 hover:text-white/80"
              }`}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.icon}
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab content - scrollable area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {activeTab === "stats" && (
          <>
            <RankSystem />
            <StreakCounter />
            <LikesCounter />
            <AnimeStats />
          </>
        )}

        {activeTab === "tasks" && (
          <TaskSystem />
        )}

        {activeTab === "achievements" && (
          <Achievements />
        )}

        {activeTab === "history" && (
          <ActivityHistory />
        )}
      </div>
    </div>
  );
};

// Placeholder components for the other sections
const AnimeStats = () => {
  return (
    <div className="w-full">
      <h3 className="text-sm text-white/80 flex items-center gap-1 mb-3">
        <BarChart2 size={14} className="text-blue-500" />
        Anime Stats
      </h3>
      <div className="bg-black/30 rounded-lg p-3 space-y-2">
        <div className="flex justify-between">
          <span className="text-xs text-white/60">Total Anime</span>
          <span className="text-xs font-medium">127</span>
        </div>
        <div className="flex justify-between">
          <span className="text-xs text-white/60">Episodes Watched</span>
          <span className="text-xs font-medium">1,893</span>
        </div>
        <div className="flex justify-between">
          <span className="text-xs text-white/60">Watch Time</span>
          <span className="text-xs font-medium">789 hours</span>
        </div>
        <div className="flex justify-between">
          <span className="text-xs text-white/60">Mean Score</span>
          <span className="text-xs font-medium">7.8</span>
        </div>
      </div>
    </div>
  );
};

const Achievements = () => {
  const achievements = [
    { id: 1, title: "First Steps", description: "Watch your first anime", completed: true },
    { id: 2, title: "Dedicated Fan", description: "Watch 100 episodes", completed: true },
    { id: 3, title: "Anime Veteran", description: "Watch 1000 episodes", completed: false },
    { id: 4, title: "Genre Explorer", description: "Watch anime from 10 different genres", completed: false },
    { id: 5, title: "Perfect Streak", description: "Maintain a 7-day streak", completed: true },
  ];

  return (
    <div className="w-full">
      <h3 className="text-sm text-white/80 flex items-center gap-1 mb-3">
        <Award size={14} className="text-yellow-500" />
        Achievements
      </h3>
      <div className="space-y-2">
        {achievements.map(achievement => (
          <div 
            key={achievement.id}
            className={`bg-black/30 rounded-lg p-3 flex items-center gap-3
              ${achievement.completed ? 'border border-yellow-500/30' : 'border border-white/10'}`}
          >
            <div className={`w-8 h-8 rounded-full flex items-center justify-center 
              ${achievement.completed ? 'bg-yellow-500/20' : 'bg-black/50'}`}>
              {achievement.completed ? (
                <Award size={16} className="text-yellow-500" />
              ) : (
                <Dices size={16} className="text-white/60" />
              )}
            </div>
            <div className="flex-1">
              <h4 className="text-sm font-medium">{achievement.title}</h4>
              <p className="text-xs text-white/60">{achievement.description}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const ActivityHistory = () => {
  const activities = [
    { id: 1, type: "watch", title: "Watched episode 12 of Demon Slayer", time: "2 hours ago" },
    { id: 2, type: "add", title: "Added Jujutsu Kaisen to your list", time: "Yesterday" },
    { id: 3, type: "rate", title: "Rated Attack on Titan 9/10", time: "3 days ago" },
    { id: 4, type: "achievement", title: "Unlocked 'Dedicated Fan' achievement", time: "1 week ago" },
    { id: 5, type: "level", title: "Reached Level 3", time: "2 weeks ago" },
  ];

  return (
    <div className="w-full">
      <h3 className="text-sm text-white/80 flex items-center gap-1 mb-3">
        <Clock size={14} className="text-purple-500" />
        Recent Activity
      </h3>
      <div className="space-y-2">
        {activities.map(activity => (
          <div key={activity.id} className="bg-black/30 rounded-lg p-3">
            <h4 className="text-sm">{activity.title}</h4>
            <p className="text-xs text-white/60 mt-1">{activity.time}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ProfileSidebar;
