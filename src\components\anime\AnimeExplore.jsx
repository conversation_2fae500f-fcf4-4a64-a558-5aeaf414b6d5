import { useEffect, useMemo, useState, useRef } from "react";
import { Link, useSearchParams } from "react-router-dom";
import { debounce } from "lodash";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { exploreAnime } from "@/api/anilist";
import useFetch from "@/hooks/useFetch";
import AddToList from "@/components/AddToList";
import { useTitlePreference, TITLE_PREFERENCES } from "@/context/TitlePreferenceContext";
import { trackExploreGenres } from "@/utils/activityTracking";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Loader2,
  Star,
  Trash2,
  Info,
  AlertCircle,
  Search,
  X,
} from "lucide-react";
import Image from "@/components/ui/Image";
import AgeRating from "@/components/ui/AgeRating";

const anime_options = {
  genres: [
    { name: "Any", value: "any" },
    { name: "Action", value: "Action" },
    { name: "Adventure", value: "Adventure" },
    { name: "Comedy", value: "Comedy" },
    { name: "Drama", value: "Drama" },
    { name: "Fantasy", value: "Fantasy" },
    { name: "Horror", value: "Horror" },
    { name: "Mystery", value: "Mystery" },
    { name: "Romance", value: "Romance" },
    { name: "Sci-Fi", value: "Sci-Fi" },
    { name: "Slice of Life", value: "Slice of Life" },
    { name: "Sports", value: "Sports" },
    { name: "Supernatural", value: "Supernatural" },
    { name: "Thriller", value: "Thriller" },
  ],
  years: [
    { name: "Any", value: "any" },
    { name: "2024", value: "2024" },
    { name: "2023", value: "2023" },
    { name: "2022", value: "2022" },
    { name: "2021", value: "2021" },
    { name: "2020", value: "2020" },
    { name: "2019", value: "2019" },
    { name: "2018", value: "2018" },
    { name: "2017", value: "2017" },
    { name: "2016", value: "2016" },
    { name: "2015", value: "2015" },
    { name: "2010-2014", value: "2010" },
    { name: "2000-2009", value: "2000" },
    { name: "1990-1999", value: "1990" },
    { name: "1980-1989", value: "1980" },
  ],
  sorts: [
    { name: "Popularity", value: "POPULARITY_DESC" },
    { name: "Trending", value: "TRENDING_DESC" },
    { name: "Average Score", value: "SCORE_DESC" },
    { name: "Favorites", value: "FAVOURITES_DESC" },
    { name: "Date (Newest)", value: "START_DATE_DESC" },
    { name: "Date (Oldest)", value: "START_DATE" },
  ],
};

const AnimeExplore = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [body, setBody] = useState({});
  const [searchQuery, setSearchQuery] = useState("");
  const searchInputRef = useRef(null);
  const { titlePreference } = useTitlePreference();

  const genre = searchParams.get("genre") || "any";
  const year = searchParams.get("year") || "any";
  const sort = searchParams.get("sort") || "POPULARITY_DESC";
  const page = parseInt(searchParams.get("page") || 1);
  const query = searchParams.get("query") || "";

  const { data, isLoading } = useFetch({
    key: [`anime-explore${JSON.stringify(body)}`],
    fun: async () => await exploreAnime(body),
  });

  const filters = useMemo(
    () => [
      {
        name: "GENRE",
        options: anime_options.genres,
      },
      { name: "YEAR", options: anime_options.years },
      {
        name: "SORT",
        options: anime_options.sorts,
      },
    ],
    []
  );

  const updateBody = debounce(() => {
    setBody({
      page,
      genre,
      year,
      sort,
      query,
    });
  }, 500);

  const { results, pageInfo } = useMemo(
    () => ({
      results: data?.results || [],
      pageInfo: data?.pageInfo || {
        total: 0,
        currentPage: 1,
        lastPage: 1,
        hasNextPage: false,
      },
    }),
    [data]
  );

  // Initialize search query from URL
  useEffect(() => {
    setSearchQuery(query);
  }, [query]);

  useEffect(() => {
    updateBody();
    return () => updateBody.cancel();
  }, [genre, year, sort, page, query]);

  // Handle search form submission
  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim() !== query) {
      setSearchParams((params) => {
        if (searchQuery.trim()) {
          params.set("query", searchQuery.trim());
        } else {
          params.delete("query");
        }
        params.set("page", 1); // Reset to first page on new search
        return params;
      });
    }
  };

  // Clear search
  const clearSearch = () => {
    setSearchQuery("");
    setSearchParams((params) => {
      params.delete("query");
      params.set("page", 1);
      return params;
    });
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  const goToPage = (page) => {
    window.scrollTo({ top: 0, behavior: "smooth" });
    if (page >= 1 && page <= pageInfo.lastPage) {
      setSearchParams((params) => {
        params.set("page", page);
        return params;
      });
    }
  };

  return (
    <div className="w-full py-4 flex flex-col gap-4">
      <div className="flex w-full flex-col md:flex-row justify-between gap-4 md:items-center">
        <div className="text-xl md:text-2xl xl:text-3xl font-semibold pl-1">
          Explore Anime
        </div>

        {/* Search form */}
        <form onSubmit={handleSearch} className="flex items-center gap-1 w-full md:max-w-md">
          <div className="relative flex-1">
            <input
              ref={searchInputRef}
              type="text"
              placeholder="Search anime..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-white/5 border-none rounded-lg py-2 pl-3 pr-8 focus:ring-1 focus:ring-primary"
            />
            {searchQuery && (
              <button
                type="button"
                onClick={clearSearch}
                className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white"
              >
                <X size={16} />
              </button>
            )}
          </div>
          <button
            type="submit"
            className="bg-primary hover:bg-primary/90 text-black font-medium rounded-lg p-2"
          >
            <Search size={20} />
          </button>
        </form>
      </div>

      <div className="flex flex-col gap-10 w-full">
        <div className="flex w-full flex-wrap lg:!flex-nowrap items-end">
          {filters.map(({ name, options }) => (
            <div
              key={name}
              className="flex w-1/2 sm:w-1/3 md:w-1/4 lg:w-[19%] p-[.4rem] sm:p-2 shrink-0 flex-col gap-1 !select-none"
            >
              <div className="flex w-full pl-1 cursor-help" title={`Filter anime by ${name.toLowerCase()}`}>{name}</div>
              <Select
                onValueChange={(v) => {
                  searchParams.set(name.toLowerCase(), v);
                  setSearchParams(searchParams);

                  // Track genre exploration for task verification
                  if (name.toLowerCase() === 'genre' && v !== 'any') {
                    const anilistStorage = localStorage.getItem('anilist');
                    if (anilistStorage) {
                      try {
                        const anilistData = JSON.parse(anilistStorage);
                        if (anilistData.id) {
                          trackExploreGenres(anilistData.id, v);
                        }
                      } catch (error) {
                        console.error('Error tracking genre exploration:', error);
                      }
                    }
                  }
                }}
                value={
                  searchParams.get(name.toLowerCase()) || options[0]?.value
                }
              >
                <SelectTrigger className="w-full rounded-lg overflow-hidden bg-white/5">
                  <SelectValue
                    placeholder={
                      options.find(
                        (o) =>
                          o.value ===
                          (searchParams.get(name.toLowerCase()) ||
                            options[0]?.value)
                      )?.name
                    }
                  />
                </SelectTrigger>
                <SelectContent
                  align={"start"}
                  className="bg-black/80 backdrop-blur-sm rounded-xl overflow-hidden py-2"
                >
                  {options.map((o) => (
                    <SelectItem
                      key={o.value}
                      value={o.value}
                      className="hover:bg-white/5 !py-2"
                    >
                      {o.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          ))}
          <div className="flex gap-2 ml-auto mb-2">
            {query && (
              <div className="flex items-center gap-1 bg-primary/20 text-primary-foreground px-3 py-1 rounded-lg text-sm">
                <span>Search: "{query}"</span>
                <button
                  onClick={clearSearch}
                  className="hover:text-white"
                  title="Clear search"
                >
                  <X size={16} />
                </button>
              </div>
            )}
            <Link
              to={`/anime`}
              className="shrink-0 p-2 bg-white/[.12] rounded-lg overflow-hidden"
              title="Reset all filters"
            >
              <Trash2 size={23} />
            </Link>
          </div>
        </div>
        <div className="flex w-full flex-wrap gap-y-4">
          {isLoading ? (
            <span className="text-sm text-gray-300 flex items-center size-full justify-center gap-1 p-10">
              <Loader2 className="animate-spin text-gray-300" size={18} />{" "}
              Loading...
            </span>
          ) : results?.length ? (
            results?.map((anime) => (
              <div
                key={`anime-${anime?.id}`}
                className="flex p-[.4rem] sm:p-2 w-1/3 sm:w-1/4 md:w-1/5 lg:w-1/6 xl:w-[14.28%] flex-wrap !select-none shrink-0"
              >
                <div className="flex flex-col group w-full gap-1">
                  <div className="flex w-full aspect-[1/1.45] pp smooth relative bg-white/5 shrink-0 rounded-xl overflow-hidden">
                    <Link
                      to={`/anime/${anime?.id}`}
                      className="size-full relative"
                    >
                      <Image
                        src={anime?.images?.coverLarge || anime?.images?.coverMedium || anime?.images?.coverSmall}
                        quality="high"
                      />
                      <AgeRating isAdult={anime?.isAdult} genres={anime?.genres} compact={true} />
                    </Link>
                    <span className="flex flex-col gap-1 absolute items-end text-xs right-1 top-1">
                      {Number(anime?.rating) > 0 && (
                        <span className="bg-black/75 p-[.1rem] px-1 gap-1 lg:group-hover:hidden rounded-md flex items-center">
                          <Star fill="gold" color="gold" size={12} />
                          {Number(anime?.rating)?.toFixed(1) || "n/a"}
                        </span>
                      )}
                      {/* Removed AddToList and QTip */}
                    </span>
                  </div>
                  <Link
                    to={`/anime/${anime?.id}`}
                    className="flex w-full gap-1 shrink-0 flex-col"
                  >
                    <div className="w-full flex text-xs items-center justify-between">
                      <span className="uppercase">{anime?.type}</span>
                      <span>
                        {anime?.release_date ? anime?.release_date.slice(-4) : ""}
                      </span>
                    </div>
                    <div className="line-clamp-2 !leading-tight text-sm lg:text-base tracking-wider">
                      {titlePreference === TITLE_PREFERENCES.ENGLISH
                        ? (anime?.title || anime?.titleRomaji || "Unknown Title")
                        : (anime?.titleRomaji || anime?.title || "Unknown Title")}
                    </div>
                  </Link>
                </div>
              </div>
            ))
          ) : (
            <span className="text-sm text-gray-300 flex items-center size-full justify-center gap-1 p-10">
              No Results Found :(
            </span>
          )}
        </div>
        <div className="h-14 flex justify-center items-center my-5 md:my-8">
          <div className="bg-white/5 flex gap-[2px] justify-center items-center rounded-lg overflow-hidden select-none">
            <button
              onClick={() => goToPage(1)}
              disabled={page === 1}
              className={`bg-white/10 p-2 w-14 flex items-center justify-center ${
                page === 1
                  ? "opacity-60"
                  : "hover:bg-white/5 hover:text-[#4aeadc]"
              } `}
            >
              <ChevronsLeft />
            </button>
            <button
              onClick={() => goToPage(page - 1)}
              className={`bg-white/10 p-2 w-12 flex items-center justify-center ${
                page === 1
                  ? "opacity-60"
                  : "hover:bg-white/5 hover:text-[#4aeadc]"
              } `}
              disabled={page === 1}
            >
              <ChevronLeft />
            </button>
            <span className="bg-white/10 p-2 min-w-[2.5rem] text-center select-none">
              {page} - {pageInfo.lastPage}
            </span>
            <button
              onClick={() => goToPage(page + 1)}
              className={`bg-white/10 p-2 w-12 flex items-center justify-center ${
                page === pageInfo.lastPage
                  ? "opacity-60"
                  : "hover:bg-white/5 hover:text-[#4aeadc]"
              } `}
              disabled={page === pageInfo.lastPage}
            >
              <ChevronRight />
            </button>
            <button
              onClick={() => goToPage(pageInfo.lastPage)}
              className={`bg-white/10 p-2 w-14 flex items-center justify-center ${
                page === pageInfo.lastPage
                  ? "opacity-60"
                  : "hover:bg-white/5 hover:text-[#4aeadc]"
              } `}
              disabled={page === pageInfo.lastPage}
            >
              <ChevronsRight />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnimeExplore;
