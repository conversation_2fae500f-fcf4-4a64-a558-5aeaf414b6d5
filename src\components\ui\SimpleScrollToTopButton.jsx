import { useState, useEffect } from 'react';
import { ArrowUp } from 'lucide-react';

/**
 * A simplified button that appears when the user scrolls down and allows them to scroll back to the top
 * This version uses native browser scrolling without GSAP dependencies
 * 
 * @param {Object} props - Component props
 * @param {number} props.showAfter - Scroll position after which to show the button (default: 300)
 * @param {string} props.position - Position of the button (default: 'bottom-right')
 */
const SimpleScrollToTopButton = ({
  showAfter = 300,
  position = 'bottom-right'
}) => {
  const [isVisible, setIsVisible] = useState(false);

  // Determine position classes
  const positionClasses = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6',
    'bottom-center': 'bottom-6 left-1/2 transform -translate-x-1/2',
  }[position] || 'bottom-6 right-6';

  // Check scroll position to show/hide button
  useEffect(() => {
    const toggleVisibility = () => {
      if (window.scrollY > showAfter) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    // Add scroll event listener
    window.addEventListener('scroll', toggleVisibility);

    // Initial check
    toggleVisibility();

    // Clean up
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, [showAfter]);

  // Simple scroll to top handler using native browser API
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <button
      onClick={scrollToTop}
      className={`fixed ${positionClasses} z-50 p-3 rounded-full bg-black/60 backdrop-blur-sm border border-white/10 hover:bg-black/80 hover:border-white/20 transition-all duration-300 shadow-lg ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10 pointer-events-none'
      }`}
      aria-label="Scroll to top"
    >
      <ArrowUp size={20} className="text-white" />
    </button>
  );
};

export default SimpleScrollToTopButton;
