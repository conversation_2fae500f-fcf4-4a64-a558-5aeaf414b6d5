import { useState, useEffect, useMemo } from "react";
import Image from "@/components/ui/Image";
import { useWatchHistory } from "@/contexts/WatchHistoryContext";
import { getAnimeDetails } from "@/api/anilist";
import { Clock } from "lucide-react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";

// Format timestamp for video duration
export const formatTimeStamp = (tt) => {
  const t = parseInt(tt);
  const hours = Math.floor(t / 3600);
  const minutes = Math.floor((t % 3600) / 60);
  const seconds = t % 60;
  return `${hours ? `${hours}:` : ""}${minutes
    .toString()
    .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
};

// Format time since last watched
const formatTimeSince = (timestamp) => {
  if (!timestamp) return '';

  const now = Date.now();
  const diff = now - timestamp;

  // Convert to appropriate time unit
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) return `${days}d ago`;
  if (hours > 0) return `${hours}h ago`;
  if (minutes > 0) return `${minutes}m ago`;
  return 'Just now';
};

// Watch history item component
const WatchHistoryItem = ({ animeId, episodeNumber, progress, lastWatched, animeDetails }) => {
  const url = `/watch/anime/${animeId}?ep=${episodeNumber}`;

  if (!animeDetails) {
    return (
      <div className="flex w-full sm:w-1/2 md:w-1/3 lg:w-1/4 xl:w-1/5 z-10 gap-2 flex-col rounded-xl !select-none overflow-hidden p-2 animate-pulse">
        <div className="flex w-full relative shrink-0 aspect-video rounded-xl overflow-hidden bg-white/5"></div>
        <div className="flex flex-col px-1 gap-2">
          <div className="h-5 bg-white/10 rounded w-3/4"></div>
          <div className="h-4 bg-white/10 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  return (
    <Link
      to={url}
      className="flex w-full sm:w-1/2 md:w-1/3 lg:w-1/4 xl:w-1/5 z-10 gap-2 flex-col rounded-xl !select-none overflow-hidden p-2 smooth transition-all duration-300 hover:bg-white/5 group"
    >
      <div className="flex w-full relative shrink-0 aspect-video rounded-xl overflow-hidden bg-black/40 border border-white/10 group-hover:border-white/20 transition-all duration-300">
        <Image
          src={animeDetails?.images?.coverLarge || animeDetails?.images?.coverMedium}
          className="brightness-95 transition-all duration-500 group-hover:scale-105 group-hover:brightness-110"
        />

        {/* Progress bar */}
        <div className="bg-black/60 backdrop-blur-sm absolute bottom-0 left-0 z-20 flex w-full">
          <div
            style={{
              width: `${progress < 10 ? 10 : progress}%`,
            }}
            className="h-1 bg-white/70 group-hover:bg-white transition-all duration-300"
          />
        </div>

        {/* Last watched time */}
        <span className="absolute top-2 right-2 z-20 text-xs bg-black/75 backdrop-blur-sm font-medium px-2 py-1 rounded-md overflow-hidden border border-white/10 group-hover:border-white/20 transition-all duration-300 flex items-center gap-1.5">
          <Clock size={10} className="text-white/80" />
          {formatTimeSince(lastWatched)}
        </span>

        {/* Episode badge */}
        <span className="absolute bottom-2 left-2 z-20 text-xs bg-black/75 backdrop-blur-sm font-medium px-2 py-1 rounded-md overflow-hidden border border-white/10 group-hover:border-white/20 transition-all duration-300">
          EP {episodeNumber}
        </span>
      </div>
      <div className="flex flex-col px-1">
        <div className="flex text-base tracking-wider !line-clamp-1 font-medium group-hover:text-white transition-colors duration-300">
          {animeDetails?.title}
        </div>
        <div className="flex text-xs lg:text-sm gap-1 text-white/70 group-hover:text-white/90 transition-colors duration-300">
          <span className="tracking-wider">
            {progress}% complete
          </span>
        </div>
      </div>
    </Link>
  );
};



const History = () => {
  document.title = "Continue Watching";
  const { watchHistory } = useWatchHistory(); // Watch history from localStorage

  const [animeDetails, setAnimeDetails] = useState({});
  const [loading, setLoading] = useState(true);

  // Extract and sort continue watching items from new watch history
  const continueWatchingItems = useMemo(() => {
    if (!watchHistory || Object.keys(watchHistory).length === 0) {
      return [];
    }

    // Convert watchHistory object to array of items
    const items = Object.entries(watchHistory).map(([animeId, episodes]) => {
      // Find the most recently watched episode
      let lastWatchedEpisode = null;
      let lastWatchedTime = 0;
      let highestProgress = 0;

      Object.entries(episodes).forEach(([episodeNum, data]) => {
        if (data.lastWatched > lastWatchedTime) {
          lastWatchedEpisode = episodeNum;
          lastWatchedTime = data.lastWatched;
          highestProgress = data.progress;
        }
      });

      // Only include if we have a valid episode and it's not fully watched (progress < 98%)
      if (lastWatchedEpisode && highestProgress < 98) {
        return {
          animeId,
          episodeNumber: lastWatchedEpisode,
          progress: highestProgress,
          lastWatched: lastWatchedTime
        };
      }
      return null;
    }).filter(Boolean);

    // Sort by last watched time (most recent first)
    return items.sort((a, b) => b.lastWatched - a.lastWatched);
  }, [watchHistory]);

  // Fetch anime details for all items in continue watching
  useEffect(() => {
    // Create a flag to track if the component is still mounted
    let isMounted = true;

    const fetchAnimeDetails = async () => {
      if (continueWatchingItems.length === 0) {
        setLoading(false);
        return;
      }

      console.log("Fetching anime details for Continue Watching items...");
      setLoading(true);

      const details = { ...animeDetails };
      let hasNewData = false;
      const missingAnimeIds = continueWatchingItems
        .filter(item => !details[item.animeId])
        .map(item => item.animeId);

      // Only fetch details for anime we don't already have
      if (missingAnimeIds.length > 0) {
        console.log(`Fetching details for ${missingAnimeIds.length} missing anime`);

        // Fetch details for each anime that we don't already have
        for (const animeId of missingAnimeIds) {
          try {
            const animeData = await getAnimeDetails(animeId);
            if (animeData && isMounted) {
              details[animeId] = animeData;
              hasNewData = true;
            }
          } catch (error) {
            console.error(`Error fetching details for anime ${animeId}:`, error);
          }
        }

        // Only update state if component is still mounted and we have new data
        if (isMounted && hasNewData) {
          setAnimeDetails(details);
        }
      } else {
        console.log("All anime details already cached");
      }

      // Only update loading state if component is still mounted
      if (isMounted) {
        setLoading(false);
      }
    };

    fetchAnimeDetails();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [continueWatchingItems, animeDetails]);



  return (
    <div className="w-full px-2 py-4 flex flex-col gap-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-2xl xl:text-3xl font-semibold">
          Continue Watching
        </h1>
        <p className="text-sm text-white/70">
          Pick up where you left off
        </p>
      </div>

      <div className="flex gap-y-4 flex-wrap">
        {loading ? (
          // Loading skeleton
          Array.from({ length: 10 }).map((_, index) => (
            <div key={index} className="flex w-full sm:w-1/2 md:w-1/3 lg:w-1/4 xl:w-1/5 z-10 gap-2 flex-col rounded-xl !select-none overflow-hidden p-2 animate-pulse">
              <div className="flex w-full relative shrink-0 aspect-video rounded-xl overflow-hidden bg-white/5"></div>
              <div className="flex flex-col px-1 gap-2">
                <div className="h-5 bg-white/10 rounded w-3/4"></div>
                <div className="h-4 bg-white/10 rounded w-1/2"></div>
              </div>
            </div>
          ))
        ) : continueWatchingItems.length > 0 ? (
          continueWatchingItems.map((item) => (
            <WatchHistoryItem
              key={`${item.animeId}-${item.episodeNumber}`}
              animeId={item.animeId}
              episodeNumber={item.episodeNumber}
              progress={item.progress}
              lastWatched={item.lastWatched}
              animeDetails={animeDetails[item.animeId]}
            />
          ))
        ) : (
          <div className="w-full flex flex-col items-center justify-center py-10 gap-4">
            <div className="bg-black/40 backdrop-blur-sm p-4 rounded-full border border-white/10">
              <Clock size={32} className="text-white/50" />
            </div>
            <div className="text-center">
              <h3 className="text-lg font-medium text-white">No in-progress anime</h3>
              <p className="text-sm text-white/70 mt-1">
                Start watching anime to see your progress here
              </p>
            </div>
            <Button asChild className="mt-2 bg-white/10 hover:bg-white/20 border border-white/10">
              <Link to="/">Browse Anime</Link>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default History;
