import React, { useState, useRef, useEffect } from 'react';
import { Upload, Search, X, AlertCircle, Loader2, Play, ExternalLink, Camera, Info, Clock, Film, Maximize2, Minimize2, Volume2, VolumeX } from 'lucide-react';
import axios from 'axios';
import { Link } from 'react-router-dom';
import Image from '@/components/ui/Image';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';

// Video Player Component
const VideoPlayer = ({ src, title }) => {
  const videoRef = useRef(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);

  // Handle play/pause
  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  // Handle mute/unmute
  const toggleMute = (e) => {
    e.stopPropagation();
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  // Handle fullscreen
  const toggleFullscreen = (e) => {
    e.stopPropagation();
    if (isFullscreen) {
      document.exitFullscreen();
    } else if (videoRef.current) {
      videoRef.current.requestFullscreen();
    }
  };

  // Update progress bar and time
  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const current = videoRef.current.currentTime;
      const duration = videoRef.current.duration;
      setCurrentTime(current);
      setProgress((current / duration) * 100);
    }
  };

  // Set duration when metadata is loaded
  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };

  // Format time in seconds to MM:SS format
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Listen for fullscreen change
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  return (
    <div className="relative w-full h-full bg-black rounded-lg overflow-hidden group video-player">
      <video
        ref={videoRef}
        src={src}
        className="w-full h-full object-contain"
        onClick={togglePlay}
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        muted={isMuted}
        playsInline
        autoPlay
      />

      {/* Play/Pause Overlay */}
      <div
        className="absolute inset-0 flex items-center justify-center bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        onClick={togglePlay}
      >
        <div className="w-16 h-16 rounded-full bg-black/50 backdrop-blur-sm flex items-center justify-center border border-white/20">
          {isPlaying ? (
            <div className="w-4 h-10 flex items-center justify-center">
              <div className="w-1 h-10 bg-white mx-0.5 rounded-sm"></div>
              <div className="w-1 h-10 bg-white mx-0.5 rounded-sm"></div>
            </div>
          ) : (
            <Play size={30} className="text-white ml-1" />
          )}
        </div>
      </div>

      {/* Video Title */}
      <div className="absolute top-0 left-0 right-0 p-4 bg-gradient-to-b from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <h3 className="text-white text-sm font-medium truncate">{title}</h3>
      </div>

      {/* Controls */}
      <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        {/* Progress bar */}
        <div className="w-full h-1 bg-white/20 rounded-full mb-3 cursor-pointer" onClick={(e) => {
          const rect = e.currentTarget.getBoundingClientRect();
          const pos = (e.clientX - rect.left) / rect.width;
          if (videoRef.current) {
            videoRef.current.currentTime = pos * videoRef.current.duration;
          }
        }}>
          <div
            className="h-full bg-white/80 rounded-full"
            style={{ width: `${progress}%` }}
          ></div>
        </div>

        {/* Controls row */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <button
              className="text-white hover:text-white/80 transition-colors"
              onClick={togglePlay}
            >
              {isPlaying ? (
                <div className="w-4 h-4 flex items-center justify-center">
                  <div className="w-0.5 h-4 bg-white mx-0.5"></div>
                  <div className="w-0.5 h-4 bg-white mx-0.5"></div>
                </div>
              ) : (
                <Play size={16} />
              )}
            </button>

            <div className="text-white/80 text-xs">
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
          </div>

          <div className="flex items-center gap-3">
            <button
              className="text-white hover:text-white/80 transition-colors"
              onClick={toggleMute}
            >
              {isMuted ? <VolumeX size={16} /> : <Volume2 size={16} />}
            </button>

            <button
              className="text-white hover:text-white/80 transition-colors"
              onClick={toggleFullscreen}
            >
              {isFullscreen ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const AnimeImageSearch = () => {
  const [image, setImage] = useState(null);
  const [preview, setPreview] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState([]);
  const [dragActive, setDragActive] = useState(false);
  const [activeVideo, setActiveVideo] = useState(null);
  const fileInputRef = useRef(null);

  // Handle file selection
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    handleFile(file);
  };

  // Handle file drop
  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  // Handle drag events
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  // Process the selected file
  const handleFile = (file) => {
    if (!file) return;

    // Check if file is an image
    if (!file.type.match('image.*')) {
      setError('Please upload an image file (PNG, JPG, JPEG, GIF)');
      return;
    }

    // Check file size (limit to 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setError('Image size should be less than 10MB');
      return;
    }

    setImage(file);
    setError(null);

    // Create preview
    const reader = new FileReader();
    reader.onloadend = () => {
      setPreview(reader.result);
    };
    reader.readAsDataURL(file);
  };

  // Clear the selected image and results
  const clearImage = () => {
    setImage(null);
    setPreview(null);
    setResults([]);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Search for anime using trace.moe API
  const searchAnime = async () => {
    if (!image) {
      setError('Please select an image first');
      return;
    }

    setLoading(true);
    setError(null);
    setResults([]);

    try {
      // According to trace.moe API docs, we need to use the correct approach
      // Create a base64 string or use the file directly
      let response;

      try {
        // First try with FormData (for file upload)
        const formData = new FormData();
        formData.append('image', image);

        response = await axios.post('https://api.trace.moe/search', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
      } catch (uploadError) {
        console.error('Error with direct upload, trying with base64:', uploadError);

        // If that fails, try with base64 encoding
        const reader = new FileReader();
        const base64Promise = new Promise((resolve, reject) => {
          reader.onload = () => resolve(reader.result);
          reader.onerror = reject;
        });
        reader.readAsDataURL(image);

        const base64Image = await base64Promise;
        const base64Data = base64Image.split(',')[1]; // Remove the data:image/jpeg;base64, part

        response = await axios.post('https://api.trace.moe/search', {
          image: base64Data
        }, {
          headers: {
            'Content-Type': 'application/json',
          },
        });
      }

      if (response.data && response.data.result && response.data.result.length > 0) {
        // Process and format results
        const processedResults = response.data.result.map(result => ({
          anilist_id: result.anilist,
          filename: result.filename,
          episode: result.episode,
          from: formatTime(result.from),
          to: formatTime(result.to),
          similarity: (result.similarity * 100).toFixed(2),
          video: result.video,
          image: result.image,
          title: result.filename.split('/').pop().replace(/\.[^/.]+$/, '').replace(/_/g, ' ')
        }));

        setResults(processedResults);
      } else {
        setError('No matches found for this image');
      }
    } catch (err) {
      console.error('Error searching anime:', err);
      setError(err.response?.data?.error || 'Failed to search. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Format time in seconds to MM:SS format
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Animation for floating icons
  useEffect(() => {
    const interval = setInterval(() => {
      const icons = document.querySelectorAll('.floating-icon');
      icons.forEach(icon => {
        const randomX = Math.random() * 10 - 5;
        const randomY = Math.random() * 10 - 5;
        icon.style.transform = `translate(${randomX}px, ${randomY}px)`;
      });
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  // Handle click outside to close video player
  useEffect(() => {
    const handleClickOutside = (e) => {
      // If clicking on a play button or inside the video player, don't close
      if (
        e.target.closest('button[title="Play clip"]') ||
        e.target.closest('.video-player') ||
        e.target.closest('button') ||
        e.target.closest('.dialog-content')
      ) {
        return;
      }

      // Otherwise, close the video player
      setActiveVideo(null);
    };

    if (activeVideo !== null) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [activeVideo]);

  // Reset other active videos when a new one is selected
  useEffect(() => {
    // When activeVideo changes, make sure to reset any previously playing videos
    const videos = document.querySelectorAll('video');
    videos.forEach(video => {
      // Find the closest parent with data-index attribute
      const container = video.closest('[data-index]');
      if (container && parseInt(container.dataset.index) !== activeVideo) {
        video.pause();
      }
    });
  }, [activeVideo]);

  return (
    <div className="min-h-screen pb-16">
      {/* Hero Section with Animated Background */}
      <div className="relative overflow-hidden">
        {/* Background with gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/80 via-black/60 to-black/90 z-0"></div>

        {/* Floating icons in background */}
        <div className="absolute inset-0 overflow-hidden opacity-20">
          <div className="floating-icon absolute top-[10%] left-[15%] transition-transform duration-[3s] ease-in-out">
            <Camera size={32} className="text-white" />
          </div>
          <div className="floating-icon absolute top-[20%] right-[20%] transition-transform duration-[3s] ease-in-out">
            <Film size={40} className="text-white" />
          </div>
          <div className="floating-icon absolute bottom-[30%] left-[25%] transition-transform duration-[3s] ease-in-out">
            <Search size={36} className="text-white" />
          </div>
          <div className="floating-icon absolute bottom-[15%] right-[15%] transition-transform duration-[3s] ease-in-out">
            <Play size={28} className="text-white" />
          </div>
        </div>

        {/* Hero content */}
        <div className="container mx-auto px-4 py-16 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 tracking-tight">
              Anime Scene Finder
            </h1>
            <p className="text-xl text-white/80 mb-8 leading-relaxed">
              Upload a screenshot from any anime, and we'll identify the exact episode and timestamp.
            </p>

            {/* Feature highlights */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 mb-8">
              <div className="bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10 hover:border-white/20 transition-all">
                <div className="w-12 h-12 rounded-full bg-white/10 flex items-center justify-center mx-auto mb-4">
                  <Camera size={24} className="text-white" />
                </div>
                <h3 className="text-white font-medium text-lg mb-2">Upload Screenshot</h3>
                <p className="text-white/70 text-sm">Drag and drop or select any anime screenshot to get started.</p>
              </div>

              <div className="bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10 hover:border-white/20 transition-all">
                <div className="w-12 h-12 rounded-full bg-white/10 flex items-center justify-center mx-auto mb-4">
                  <Search size={24} className="text-white" />
                </div>
                <h3 className="text-white font-medium text-lg mb-2">Instant Search</h3>
                <p className="text-white/70 text-sm">Our AI quickly identifies the anime, episode, and timestamp.</p>
              </div>

              <div className="bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10 hover:border-white/20 transition-all">
                <div className="w-12 h-12 rounded-full bg-white/10 flex items-center justify-center mx-auto mb-4">
                  <Play size={24} className="text-white" />
                </div>
                <h3 className="text-white font-medium text-lg mb-2">Watch Instantly</h3>
                <p className="text-white/70 text-sm">Jump directly to the exact scene in the episode.</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 max-w-5xl -mt-8">
        {/* Upload Section - Glassy Card */}
        <div className="bg-black/30 backdrop-blur-md border border-white/10 rounded-2xl overflow-hidden shadow-xl mb-12">
          {/* Upload Area */}
          <div
            className={`p-8 text-center transition-all duration-300 relative
              ${dragActive ? 'bg-white/10' : 'hover:bg-white/5'}
            `}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            {!preview ? (
              <div className="space-y-6 py-8">
                <div className={`w-24 h-24 mx-auto rounded-full flex items-center justify-center border-2 transition-all duration-300 ${dragActive ? 'border-white scale-110 bg-white/10' : 'border-white/30'}`}>
                  <Upload size={36} className={`transition-all duration-300 ${dragActive ? 'text-white scale-110' : 'text-white/70'}`} />
                </div>
                <div>
                  <p className="text-white text-xl font-medium">Drag and drop an image here</p>
                  <p className="text-white/70 text-base mt-2">or click to select a file from your device</p>
                </div>
                <div>
                  <button
                    onClick={() => fileInputRef.current.click()}
                    className="px-6 py-3 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-all duration-300 border border-white/10 hover:border-white/30 shadow-lg hover:shadow-xl"
                  >
                    Select Image
                  </button>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    accept="image/*"
                    className="hidden"
                  />
                </div>
                <div className="flex items-center justify-center gap-2 mt-6 text-white/50 text-sm">
                  <Info size={14} />
                  <p>Supported formats: PNG, JPG, JPEG, GIF (Max: 10MB)</p>
                </div>
              </div>
            ) : (
              <div className="space-y-6 py-4">
                <div className="relative max-w-lg mx-auto">
                  <img
                    src={preview}
                    alt="Preview"
                    className="max-h-[350px] rounded-lg mx-auto object-contain shadow-lg border border-white/20"
                  />
                  <button
                    onClick={clearImage}
                    className="absolute top-3 right-3 bg-black/70 text-white p-2 rounded-full hover:bg-black transition-all duration-300 border border-white/10 hover:border-white/30"
                  >
                    <X size={20} />
                  </button>
                </div>
                <div className="flex flex-wrap justify-center gap-4 mt-6">
                  <button
                    onClick={clearImage}
                    className="px-5 py-2.5 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-all duration-300 border border-white/10 hover:border-white/30"
                  >
                    Change Image
                  </button>
                  <button
                    onClick={searchAnime}
                    disabled={loading}
                    className="px-5 py-2.5 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-all duration-300 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed border border-white/20 hover:border-white/40 shadow-lg"
                  >
                    {loading ? (
                      <>
                        <Loader2 size={18} className="animate-spin" />
                        <span>Searching...</span>
                      </>
                    ) : (
                      <>
                        <Search size={18} />
                        <span>Find Anime</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-gradient-to-r from-red-500/20 to-red-500/10 backdrop-blur-sm border border-red-500/30 rounded-xl p-5 mb-10 flex items-start gap-4 shadow-lg animate-fadeIn">
            <div className="bg-red-500/20 rounded-full p-2 mt-0.5">
              <AlertCircle size={22} className="text-red-400" />
            </div>
            <div>
              <h3 className="text-white font-medium mb-1">Search Error</h3>
              <p className="text-white/80">{error}</p>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex flex-col items-center justify-center py-16 animate-fadeIn">
            <div className="relative">
              <div className="w-16 h-16 border-4 border-white/10 border-t-white/80 rounded-full animate-spin"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <Search size={20} className="text-white/80" />
              </div>
            </div>
            <p className="text-white/70 mt-6 text-lg">Searching for matches...</p>
            <p className="text-white/50 mt-2 text-sm">This may take a few seconds</p>
          </div>
        )}

        {/* Results Section */}
        {results.length > 0 && !loading && (
          <div className="space-y-8 animate-fadeIn">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-white">Search Results</h2>
              <div className="text-white/70 text-sm flex items-center gap-2">
                <span>{results.length} matches found</span>
                <div className="h-4 w-px bg-white/30"></div>
                <button
                  onClick={clearImage}
                  className="text-white/70 hover:text-white transition-colors flex items-center gap-1"
                >
                  <X size={14} />
                  <span>Clear</span>
                </button>
              </div>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
              {results.map((result, index) => (
                <div
                  key={index}
                  data-index={index}
                  className="bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden hover:border-white/30 transition-all duration-300 shadow-lg hover:shadow-xl group"
                >
                  <div className="relative">
                    {activeVideo === index ? (
                      <div className="w-full h-52 relative">
                        <VideoPlayer
                          src={result.video}
                          title={`${result.title || 'Unknown Anime'} - Episode ${result.episode || '?'}`}
                        />
                      </div>
                    ) : (
                      <>
                        <img
                          src={result.image}
                          alt={result.title}
                          className="w-full h-52 object-cover transition-transform duration-500 group-hover:scale-105"
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = 'https://via.placeholder.com/640x360?text=No+Preview';
                          }}
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/40 to-black/20"></div>
                      </>
                    )}

                    {/* Episode info overlay */}
                    <div className="absolute bottom-0 left-0 right-0 p-4">
                      <div className="flex flex-wrap items-center gap-3">
                        <div className="bg-black/60 backdrop-blur-sm text-white text-sm px-3 py-1.5 rounded-lg border border-white/10 flex items-center gap-1.5">
                          <Film size={14} />
                          <span>{result.episode ? `Episode ${result.episode}` : 'Unknown Episode'}</span>
                        </div>
                        <div className="bg-black/60 backdrop-blur-sm text-white text-sm px-3 py-1.5 rounded-lg border border-white/10 flex items-center gap-1.5">
                          <Clock size={14} />
                          <span>{result.from} - {result.to}</span>
                        </div>
                      </div>
                    </div>

                    {/* Play clip button */}
                    {result.video && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setActiveVideo(index);
                        }}
                        className="absolute top-4 right-4 bg-black/70 backdrop-blur-sm text-white p-2.5 rounded-full hover:bg-white/20 transition-all duration-300 border border-white/10 hover:border-white/30 group-hover:scale-110"
                        title="Play clip"
                      >
                        <Play size={18} />
                      </button>
                    )}

                    {/* Similarity badge */}
                    <div className="absolute top-4 left-4">
                      <div className={`text-white text-xs px-3 py-1.5 rounded-lg backdrop-blur-sm flex items-center gap-1.5 border ${
                        parseFloat(result.similarity) > 90
                          ? 'bg-green-500/20 border-green-500/30'
                          : parseFloat(result.similarity) > 80
                            ? 'bg-yellow-500/20 border-yellow-500/30'
                            : 'bg-white/20 border-white/30'
                      }`}>
                        <span className="font-medium">{result.similarity}%</span>
                        <span className="text-white/80">match</span>
                      </div>
                    </div>
                  </div>

                  <div className="p-5">
                    <h3 className="text-white font-medium text-lg mb-4 line-clamp-2">{result.title}</h3>

                    <div className="flex items-center gap-3">
                      <Link
                        to={`/watch/${result.anilist_id}/${result.episode || 1}`}
                        className="flex-1 text-sm bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg transition-all duration-300 flex items-center justify-center gap-2 border border-white/10 hover:border-white/30"
                      >
                        <Play size={16} />
                        <span>Watch Episode {result.episode || 1}</span>
                      </Link>
                      <Link
                        to={`/anime/${result.anilist_id}`}
                        className="text-sm bg-white/10 hover:bg-white/20 text-white p-2 rounded-lg transition-all duration-300 border border-white/10 hover:border-white/30"
                        title="View Anime"
                      >
                        <Info size={18} />
                      </Link>
                      <a
                        href={`https://anilist.co/anime/${result.anilist_id}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm bg-white/10 hover:bg-white/20 text-white p-2 rounded-lg transition-all duration-300 border border-white/10 hover:border-white/30"
                        title="View on AniList"
                      >
                        <ExternalLink size={18} />
                      </a>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Powered by notice */}
        <div className="mt-16 text-center">
          <div className="inline-block bg-white/5 backdrop-blur-sm px-4 py-2 rounded-full border border-white/10">
            <p className="text-white/70 text-sm">
              Powered by <a href="https://trace.moe" target="_blank" rel="noopener noreferrer" className="text-white hover:underline font-medium">trace.moe</a> API
            </p>
          </div>
        </div>
      </div>

      {/* Fullscreen Video Dialog */}
      <Dialog>
        <DialogTrigger asChild>
          <button
            className={`fixed bottom-6 right-6 z-50 bg-black/70 backdrop-blur-sm text-white p-3 rounded-full shadow-lg border border-white/20 hover:bg-white/20 transition-all duration-300 ${activeVideo === null ? 'hidden' : 'flex'} items-center gap-2`}
            onClick={(e) => e.preventDefault()}
          >
            <Maximize2 size={18} />
            <span className="text-sm font-medium">Fullscreen</span>
          </button>
        </DialogTrigger>
        <DialogContent className="max-w-5xl p-0 bg-transparent border-none dialog-content">
          {activeVideo !== null && results[activeVideo] && (
            <VideoPlayer
              src={results[activeVideo].video}
              title={`${results[activeVideo].title || 'Unknown Anime'} - Episode ${results[activeVideo].episode || '?'}`}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Add custom CSS for animations */}
      <style jsx>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn {
          animation: fadeIn 0.5s ease-out forwards;
        }
      `}</style>
    </div>
  );
};

export default AnimeImageSearch;
