{"name": "thor-bkl", "private": true, "version": "1.3.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@tanstack/react-query": "^5.59.18", "@vidstack/react": "^1.12.13", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-autoplay": "^8.3.1", "embla-carousel-fade": "^8.3.1", "embla-carousel-react": "^8.3.1", "gsap": "^3.13.0", "hls.js": "1.5.17", "lodash": "^4.17.21", "lucide-react": "^0.454.0", "next-themes": "^0.4.3", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-intersection-observer": "^9.16.0", "react-lazy-load-image-component": "^1.6.2", "react-router-dom": "^6.27.0", "react-tooltip": "^5.28.1", "sonner": "^1.7.0", "swiper": "^11.2.6", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "postcss": "^8.4.47", "tailwind-scrollbar": "^3.0.5", "tailwindcss": "^3.4.14", "vite": "^5.4.10"}}