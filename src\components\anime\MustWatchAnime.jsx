import React, { memo } from 'react';
import { Link } from 'react-router-dom';
import Image from '@/components/ui/Image';
import { Star, Play, ChevronRight, Trophy } from 'lucide-react';
import useFetch from '@/hooks/useFetch';
import { getTopRatedAnime } from '@/api/anilist';
import AgeRating from '@/components/ui/AgeRating';
import { Carousel, CarouselContent, CarouselItem } from "@/components/ui/carousel";
import { Button } from '@/components/ui/button';

const MustWatchAnime = () => {
  const { data } = useFetch({
    key: ["must-watch-anime"],
    fun: async () => {
      return (await getTopRatedAnime()) || null;
    },
    placeholderData: [],
  });

  // Take the top 5 anime
  const topAnime = data?.slice(0, 5) || [];

  return (
    <div className="w-full">
      {/* Header with trophy icon */}
      <div className="flex items-center gap-2 mb-3">
        <div className="bg-white/20 p-1.5 rounded-md">
          <Trophy size={16} className="text-white" />
        </div>
        <h2 className="text-lg font-medium">Must Watch Anime</h2>
        <div className="flex-grow"></div>
        <Link
          to="/explore?sort=SCORE_DESC"
          className="text-xs text-white hover:underline"
        >
          View All
        </Link>
      </div>

      {/* Horizontal list with rank numbers */}
      <div className="flex flex-col gap-2">
        {topAnime.map((anime, index) => (
          <Link
            key={index}
            to={anime?.id ? `/anime/${anime?.id}` : ""}
            className="group"
          >
            <div className="flex relative rounded-lg overflow-hidden transition-all duration-300 border-l-4 border-white/50 group-hover:scale-[1.01]">
              {/* Blurred banner background */}
              <div className="absolute inset-0 z-0">
                <Image
                  src={anime?.images?.bannerLarge || anime?.images?.bannerSmall || anime?.images?.coverLarge}
                  className="!object-cover !w-full !h-full blur-sm opacity-40 scale-110"
                  quality="low"
                  loading="lazy"
                />
                <div className="absolute inset-0 bg-black/60 group-hover:bg-black/70 transition-colors duration-300"></div>
              </div>
              {/* Rank number */}
              <div className="flex items-center justify-center w-10 bg-white/80 text-black font-bold relative z-10">
                #{index + 1}
              </div>

              {/* Poster image */}
              <div className="w-14 h-20 shrink-0 overflow-hidden relative z-10">
                <Image
                  src={anime?.images?.coverLarge || anime?.images?.coverMedium}
                  className="!object-cover !w-full !h-full"
                  quality="high"
                  loading="lazy"
                />
              </div>

              {/* Content */}
              <div className="flex flex-col justify-center p-2 flex-grow min-w-0 relative z-10">
                <div className="flex items-center gap-1">
                  <h3 className="text-sm font-medium line-clamp-1 flex-grow">{anime?.title || ""}</h3>
                  {Number(anime?.rating) > 0 && (
                    <div className="flex items-center gap-0.5 shrink-0 bg-white/20 px-1.5 py-0.5 rounded-full">
                      <Star fill="#FFFFFF" color="#FFFFFF" size={12} />
                      <span className="text-xs font-bold text-white">{Number(anime?.rating)?.toFixed(1)}</span>
                    </div>
                  )}
                </div>

                <div className="flex items-center text-xs text-gray-400 mt-0.5">
                  <span>{anime?.type || ""}</span>
                  {anime?.episodes && (
                    <>
                      <span className="mx-1">•</span>
                      <span>{anime?.episodes} Eps</span>
                    </>
                  )}
                  {anime?.release_date && (
                    <>
                      <span className="mx-1">•</span>
                      <span>{anime?.release_date?.slice(-4)}</span>
                    </>
                  )}
                </div>

                {/* Genres */}
                {anime?.genres && anime.genres.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-1">
                    {anime.genres.slice(0, 2).map((genre, i) => (
                      <span key={i} className="text-[10px] px-1.5 py-0.5 bg-black/50 border border-white/30 text-white rounded-full">
                        {typeof genre === 'string' ? genre : ''}
                      </span>
                    ))}
                  </div>
                )}
              </div>

              {/* Watch button */}
              <div className="flex items-center pr-3 pl-1 relative z-10">
                <div className="w-8 h-8 rounded-full bg-white/80 flex items-center justify-center group-hover:bg-white transition-colors duration-300 shadow-md">
                  <Play size={14} className="text-black" fill="black" />
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default memo(MustWatchAnime);
