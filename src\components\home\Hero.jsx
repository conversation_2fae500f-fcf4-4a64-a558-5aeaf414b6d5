import useEmblaCarousel from "embla-carousel-react";
import useFetch from "@/hooks/useFetch";
import { getNowPlaying } from "@/api/tmdb";
import Image from "../ui/Image";
import { PlayIcon, Star } from "lucide-react";
import { Link } from "react-router-dom";
import { useEffect, useRef, useState } from "react";
import axios from "axios";
const Hero = () => {
  const [slide, setSlide] = useState(0);
  const [trailer, setTrailer] = useState("");
  const [mute, setMute] = useState(true);
  const [playing, setPlaying] = useState(true);
  const [volume, setVolume] = useState(1);
  const [ref, api] = useEmblaCarousel({ loop: true, dragFree: false });
  const re = useRef();
  const { data, isLoading } = useFetch({
    key: ["nowplaying"],
    fun: async () => {
      return (await getNowPlaying()) || null;
    },
    placeholderData: [],
  });

  const { data: trailerData } = useFetch({
    enabled: data?.[slide]?.id ? true : false,
    placeholderData: [],
    key: [`trailer${data?.[slide]?.id}`],
    fun: async () => {
      try {
        if (data?.[slide]?.id) {
          const { data: t } = await axios.get(
            `https://tmdb.gojo.wtf/trailer?id=${data?.[slide]?.id}`
          );
          return t || [];
        }
        return [];
      } catch (e) {
        console.log(e?.message);
        return [];
      }
    },
  });

  useEffect(() => {
    if (trailerData) {
      const trailers = trailerData?.filter(
        (t) =>
          t?.type === "Trailer" ||
          t?.type === "Teaser" ||
          (t?.type === "Clip" && t?.site === "YouTube")
      );
      setTrailer(
        trailerData?.length > 0
          ? `https://www.youtube.com/embed/${
              trailers ? trailers?.pop()?.key : trailers?.[0]?.key
            }`
          : ""
      );
    }
    return () => setTrailer("");
  }, [slide, trailerData]);

  const he = (s) => {
    const n = s?.selectedScrollSnap();
    if (n !== slide) {
      setTrailer("");
      setSlide(s?.selectedScrollSnap());
    }
  };
  useEffect(() => {
    if (api) api?.on("select", he);
  }, [api]);
  useEffect(() => {
    try {
      let i = null;
      const fadeVolume = (flow) => {
        if (i) clearInterval(i);
        i = setInterval(() => {
          setVolume((v) => {
            const nv = flow === "up" ? v + 0.1 : v - 0.1;
            if (nv >= 1) {
              clearInterval(i);
              return 1;
            } else if (nv <= 0) {
              clearInterval(i);
              setPlaying(false);
              return 0;
            }
            return nv;
          });
        }, 100);
      };
      const hi = (en) => {
        en.forEach((e) => {
          if (mute) {
            setPlaying(e?.isIntersecting);
          } else {
            if (e?.isIntersecting) {
              setPlaying(true);
              fadeVolume("up");
            } else {
              fadeVolume("down");
            }
          }
        });
      };
      const observer = new IntersectionObserver(hi, {
        root: null,
        rootMargin: "0px",
        threshold: 0,
      });
      if (re?.current) observer?.observe(re?.current);
      return () => {
        clearInterval(i);
        observer?.disconnect();
      };
    } catch (e) {
      console.log(e?.message);
    }
  }, [re, mute]);
  return (
    <div
      ref={re}
      className="!select-none w-full aspect-[16/10] sm:aspect-video max-h-[70vh] overflow-hidden rounded-xl"
    >
      <div className="size-full" ref={ref}>
        <div className="flex size-full">
          {isLoading ? (
            <div className="basis-full bg-white/5 rounded-xl overflow-hidden"></div>
          ) : (
            data?.map((h, i) => (
              <div
                key={h?.id}
                className="flex basis-full rounded-xl overflow-hidden relative shrink-0 "
              >
                <div className="flex items-center justify-center size-full absolute top-0 left-0">
                  {(
                    <Image
                      src={h?.images?.bannerLarge}
                      className={"pointer-events-none"}
                    />
                  )}

                  <span className="hero-gradient size-full absolute"></span>
                </div>
                <div className="flex gap-1 sm:gap-2 flex-col w-full z-10 p-2 pl-3 sm:p-8 sm:pb-10 mt-auto sm:max-w-[60%]">
                  <div className="flex text-2xl sm:text-3xl font-semibold !leading-[1.1] drop-shadow-xl sm:leading-snug sm:max-w-[90%] mb-1 !line-clamp-2">
                    {h?.images?.logo?.length ? (
                      <span
                        title={h?.title}
                        className="flex !h-14 items-end mb-1 brightness-110 overflow-hidden max-w-[90%]"
                      >
                        <Image
                          src={h?.images?.logo}
                          className={"!object-contain !object-left"}
                        >
                          <div className="flex text-2xl sm:text-3xl font-semibold self-end !leading-[1.1] sm:leading-snug sm:max-w-[90%] drop-shadow-xl -mb-1 !line-clamp-2">
                            {h?.title}
                          </div>
                        </Image>
                      </span>
                    ) : (
                      h?.title
                    )}
                  </div>
                  <div className="flex items-center gap-4 text-xs text-gray-300 font-medium tracking-wide">
                    <span className="flex items-center">
                      {h?.type?.toUpperCase()}
                    </span>{" "}
                    {h?.rating ? (
                      <span className="flex items-center gap-1">
                        <Star
                          className="text-gray-300 fill-gray-300"
                          size={13}
                        />
                        {Number(h?.rating)?.toFixed(1)}
                      </span>
                    ) : (
                      ""
                    )}
                    <span className="uppercase flex items-center">
                      {h?.release_date || h?.status || ""}
                    </span>
                  </div>
                  <div
                    dangerouslySetInnerHTML={{
                      __html: h?.description || "",
                    }}
                    className="flex !line-clamp-2 text-xs max-w-[90%] sm:max-w-full font-light sm:!line-clamp-3 sm:text-sm !leading-tight"
                  ></div>
                  <div className="flex items-center gap-2 mt-2">
                    <Link
                      to={`/watch/${h?.type}/${h?.id}`}
                      className="text-black bg-white hover:bg-white/80 h-9 w-28 justify-center rounded-xl gap-[.4rem] font-medium flex items-center"
                    >
                      <PlayIcon size={15} fill="black" strokeWidth={3} /> Watch
                    </Link>
                    {/* Removed AddToList */}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default Hero;
