import { useState, useEffect } from "react";
import { useAniList } from "@/hooks/useAniList";
import { useNavigate } from "react-router-dom";
import { Loader2, LogOut, Settings, Heart } from "lucide-react";
import Image from "@/components/ui/Image";
import { Button } from "@/components/ui/button";
import ProfileSidebar from "@/components/profile/ProfileSidebar";
import ProfileStats from "@/components/profile/ProfileStats";
import GenreChart from "@/components/profile/GenreChart";
import WatchHistoryChart from "@/components/profile/WatchHistoryChart";
import AnimeStatusChart from "@/components/profile/AnimeStatusChart";
import { useUserActivity } from "@/context/UserActivityContext";
import { toast } from "sonner";

const ProfileRedesigned = () => {
  const { user, loading, logout, isAuthenticated } = useAniList();
  const { level, rank, likes, addLike } = useUserActivity();
  const navigate = useNavigate();
  const [likeClicked, setLikeClicked] = useState(false);

  // Handle like button click
  const handleLike = () => {
    if (!likeClicked) {
      const newLikes = addLike();
      setLikeClicked(true);
      toast.success("Thanks for the like!", {
        description: "You've given this profile a like and earned 5 XP!"
      });
    }
  };

  // Redirect if not authenticated
  useEffect(() => {
    if (!loading && !isAuthenticated) {
      navigate("/");
    }
  }, [loading, isAuthenticated, navigate]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[70vh]">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[70vh] gap-4">
        <div className="p-4 bg-red-900/20 border border-red-900/30 rounded-lg max-w-md text-center">
          <h1 className="text-xl font-medium text-red-400 mb-2">Profile Not Available</h1>
          <p className="text-gray-400 mb-4">Unable to load your AniList profile. Please try logging in again.</p>
          <Button onClick={() => navigate("/")} variant="outline">
            Return to Home
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Left Column - Main Content */}
        <div className="lg:col-span-3 space-y-6">
          {/* Profile Banner */}
          <div className="bg-black/40 backdrop-blur-sm rounded-lg overflow-hidden border border-white/10">
            {/* Banner Image */}
            <div className="relative h-48 w-full">
              {user.bannerImage ? (
                <Image
                  src={user.bannerImage}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-r from-purple-900/50 to-blue-900/50"></div>
              )}
              
              {/* Profile Avatar */}
              <div className="absolute -bottom-12 left-6 border-4 border-black/40 rounded-full overflow-hidden">
                <Image
                  src={user.avatar?.large || user.avatar?.medium}
                  className="w-24 h-24 object-cover"
                />
              </div>
            </div>
            
            {/* Profile Info */}
            <div className="pt-16 pb-6 px-6">
              <div className="flex justify-between items-start">
                <div>
                  <h1 className="text-2xl font-bold">{user.name}</h1>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-sm text-white/60">@{user.name}</span>
                    {user.id && (
                      <span className="text-xs bg-white/10 px-2 py-0.5 rounded-full">
                        ID: {user.id}
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-white/10 hover:bg-white/5"
                    onClick={handleLike}
                    disabled={likeClicked}
                  >
                    <Heart size={16} className={`${likeClicked ? 'fill-red-500 text-red-500' : ''} mr-1`} />
                    {likes} Likes
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-white/10 hover:bg-white/5"
                    onClick={logout}
                  >
                    <LogOut size={16} />
                  </Button>
                </div>
              </div>
            </div>
          </div>
          
          {/* Stats Cards */}
          <ProfileStats />
          
          {/* Charts */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-black/40 backdrop-blur-sm rounded-lg p-6 border border-white/10">
              <GenreChart />
            </div>
            
            <div className="bg-black/40 backdrop-blur-sm rounded-lg p-6 border border-white/10">
              <AnimeStatusChart />
            </div>
          </div>
          
          {/* Watch History */}
          <div className="bg-black/40 backdrop-blur-sm rounded-lg p-6 border border-white/10">
            <WatchHistoryChart />
          </div>
        </div>
        
        {/* Right Column - Sidebar */}
        <div className="lg:col-span-1">
          <ProfileSidebar />
        </div>
      </div>
    </div>
  );
};

export default ProfileRedesigned;
