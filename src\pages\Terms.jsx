import { useEffect } from "react";

const Terms = () => {
  // Set page title
  useEffect(() => {
    document.title = "Terms of Service | AnimeHQ";
    return () => {
      document.title = "AnimeHQ"; // Reset title when component unmounts
    };
  }, []);

  return (
    <div className="py-8 px-4 sm:px-6 lg:px-8 max-w-5xl mx-auto">
      <h1 className="text-3xl font-bold mb-8 text-center">Terms of Service</h1>
      
      <div className="prose prose-invert max-w-none">
        <p className="text-gray-300 mb-6">
          Last updated: {new Date().toLocaleDateString()}
        </p>

        <h2 className="text-xl font-semibold mt-8 mb-4">1. Acceptance of Terms</h2>
        <p className="text-gray-300 mb-4">
          By accessing or using AnimeHQ, you agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use our service.
        </p>

        <h2 className="text-xl font-semibold mt-8 mb-4">2. Description of Service</h2>
        <p className="text-gray-300 mb-4">
          AnimeHQ provides a platform for users to discover and stream anime content. We do not host any content on our servers but provide links to third-party services.
        </p>

        <h2 className="text-xl font-semibold mt-8 mb-4">3. User Accounts</h2>
        <p className="text-gray-300 mb-4">
          Some features of AnimeHQ may require you to create an account. You are responsible for maintaining the confidentiality of your account information and for all activities that occur under your account.
        </p>

        <h2 className="text-xl font-semibold mt-8 mb-4">4. User Conduct</h2>
        <p className="text-gray-300 mb-4">
          You agree not to use AnimeHQ for any unlawful purpose or in any way that could damage, disable, or impair the service. You also agree not to attempt to gain unauthorized access to any part of the service.
        </p>

        <h2 className="text-xl font-semibold mt-8 mb-4">5. Content</h2>
        <p className="text-gray-300 mb-4">
          AnimeHQ does not host any content on its servers. All content is streamed through third-party services. We do not claim ownership of any content accessible through our service.
        </p>

        <h2 className="text-xl font-semibold mt-8 mb-4">6. Intellectual Property</h2>
        <p className="text-gray-300 mb-4">
          The AnimeHQ name, logo, and all related graphics, design elements, and content are the property of AnimeHQ or its content suppliers and are protected by copyright and trademark laws.
        </p>

        <h2 className="text-xl font-semibold mt-8 mb-4">7. Disclaimer of Warranties</h2>
        <p className="text-gray-300 mb-4">
          AnimeHQ is provided "as is" without warranties of any kind, either express or implied. We do not guarantee that the service will be uninterrupted or error-free.
        </p>

        <h2 className="text-xl font-semibold mt-8 mb-4">8. Limitation of Liability</h2>
        <p className="text-gray-300 mb-4">
          AnimeHQ shall not be liable for any indirect, incidental, special, consequential, or punitive damages resulting from your use of or inability to use the service.
        </p>

        <h2 className="text-xl font-semibold mt-8 mb-4">9. Changes to Terms</h2>
        <p className="text-gray-300 mb-4">
          We reserve the right to modify these Terms of Service at any time. Your continued use of AnimeHQ after such changes constitutes your acceptance of the new terms.
        </p>

        <h2 className="text-xl font-semibold mt-8 mb-4">10. Governing Law</h2>
        <p className="text-gray-300 mb-4">
          These Terms of Service shall be governed by and construed in accordance with the laws of the jurisdiction in which AnimeHQ operates.
        </p>
      </div>
    </div>
  );
};

export default Terms;
