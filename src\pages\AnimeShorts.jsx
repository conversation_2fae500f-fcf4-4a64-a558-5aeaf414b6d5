import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import {
  Play,
  Volume2,
  VolumeX,
  ChevronUp,
  ChevronDown,
  Info,
  ArrowLeft
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useInView } from "react-intersection-observer";
import { useMainContext } from "@/context/MainContext";

const AnimeShorts = () => {
  const [shorts, setShorts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [muted, setMuted] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const navigate = useNavigate();
  const { setHideHeader, setHideFooter } = useMainContext();

  // Hide header and footer for immersive experience
  useEffect(() => {
    setHideHeader(true);
    setHideFooter(true);

    return () => {
      setHideHeader(false);
      setHideFooter(false);
    };
  }, [setHideHeader, setHideFooter]);

  // Fetch anime shorts from real API
  useEffect(() => {
    const fetchShorts = async () => {
      if (!hasMore || loading) return;

      try {
        setLoading(true);
        console.log("Fetching shorts page:", page);

        // Use Jikan API to get real anime data with trailers
        // Using different endpoints to maximize chances of finding anime with trailers
        let endpoint;

        // Alternate between different endpoints to get more variety
        if (page % 3 === 0) {
          // Top anime
          endpoint = `https://api.jikan.moe/v4/top/anime?page=${page}&limit=25`;
        } else if (page % 3 === 1) {
          // Seasonal anime
          endpoint = `https://api.jikan.moe/v4/seasons/now?page=${page}&limit=25`;
        } else {
          // Popular anime
          endpoint = `https://api.jikan.moe/v4/anime?page=${page}&limit=25&order_by=popularity&sort=desc`;
        }

        console.log("Fetching from endpoint:", endpoint);
        const response = await fetch(endpoint);
        const data = await response.json();

        if (!data.data || data.data.length === 0) {
          setHasMore(false);
          return;
        }

        // Process the API data to create shorts - use only API data
        console.log("API response data:", data.data.length, "anime found");

        // Log the trailer data to debug
        data.data.forEach((anime, index) => {
          if (anime.trailer) {
            console.log(`Anime ${index}: ${anime.title} - Trailer:`, anime.trailer);
          }
        });

        // Extract anime with video URLs and convert YouTube URLs to Piped API URLs
        const processedShorts = await Promise.all(
          data.data
            .filter(anime => {
              // Check if anime has trailer data
              return anime.trailer && (anime.trailer.url || anime.trailer.embed_url);
            })
            .map(async anime => {
              // For each anime, create a short object
              // Use the most appropriate video URL available
              let videoUrl = null;
              let youtubeId = null;

              // Extract YouTube video ID if available
              if (anime.trailer.url && anime.trailer.url.includes('youtube.com')) {
                const match = anime.trailer.url.match(/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/);
                if (match && match[1]) {
                  youtubeId = match[1];
                }
              } else if (anime.trailer.embed_url && anime.trailer.embed_url.includes('youtube.com')) {
                const match = anime.trailer.embed_url.match(/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/);
                if (match && match[1]) {
                  youtubeId = match[1];
                }
              }

              // If we have a YouTube ID, use Piped API to get the video
              if (youtubeId) {
                try {
                  // Use Piped API to get video streams
                  // Try multiple Piped API instances in case some are down
                  const pipedInstances = [
                    `https://pipedapi.leptons.xyz/streams/${youtubeId}`,
                    `https://pipedapi.kavin.rocks/streams/${youtubeId}`,
                    `https://pipedapi.tokhmi.xyz/streams/${youtubeId}`,
                    `https://api.piped.projectsegfau.lt/streams/${youtubeId}`
                  ];

                  let pipedResponse = null;
                  let instanceIndex = 0;

                  // Try each instance until one works
                  while (!pipedResponse && instanceIndex < pipedInstances.length) {
                    try {
                      console.log(`Trying Piped API instance ${instanceIndex + 1}/${pipedInstances.length}`);
                      const response = await fetch(pipedInstances[instanceIndex]);
                      if (response.ok) {
                        pipedResponse = response;
                        console.log(`Successfully connected to Piped API instance ${instanceIndex + 1}`);
                        break;
                      } else {
                        console.log(`Piped API instance ${instanceIndex + 1} returned status ${response.status}`);
                        instanceIndex++;
                      }
                    } catch (err) {
                      console.log(`Error with Piped API instance ${instanceIndex + 1}:`, err);
                      instanceIndex++;
                    }
                  }

                  // If all instances failed, throw an error
                  if (!pipedResponse) {
                    throw new Error("All Piped API instances failed");
                  }

                  // Process the response
                  const pipedData = await pipedResponse.json();

                    // Get the highest quality video URL
                    if (pipedData.videoStreams && pipedData.videoStreams.length > 0) {
                      // Sort by quality (descending)
                      const sortedStreams = [...pipedData.videoStreams].sort((a, b) => {
                        // Extract quality numbers (e.g., "720p" -> 720)
                        const qualityA = parseInt(a.quality.replace('p', '')) || 0;
                        const qualityB = parseInt(b.quality.replace('p', '')) || 0;
                        return qualityB - qualityA;
                      });

                      // Find a suitable video format that browsers can play
                      // Prefer mp4 format if available
                      const mp4Stream = sortedStreams.find(stream =>
                        stream.mimeType && stream.mimeType.includes('mp4'));

                      if (mp4Stream) {
                        videoUrl = mp4Stream.url;
                        console.log(`Using Piped API for ${anime.title}, mp4 format, quality: ${mp4Stream.quality}`);
                      } else {
                        // Fallback to the highest quality stream
                        videoUrl = sortedStreams[0].url;
                        console.log(`Using Piped API for ${anime.title}, format: ${sortedStreams[0].mimeType || 'unknown'}, quality: ${sortedStreams[0].quality}`);
                      }
                    }
                } catch (error) {
                  console.error(`Error fetching from Piped API for ${youtubeId}:`, error);
                  // Fallback to original URL if Piped API fails
                  videoUrl = anime.trailer.url || anime.trailer.embed_url;
                }
              } else {
                // Use original URL if not YouTube
                videoUrl = anime.trailer.url || anime.trailer.embed_url;
              }

              return {
                id: `short-${anime.mal_id}`,
                animeId: anime.mal_id,
                title: anime.title,
                description: anime.synopsis || "No description available",
                coverImage: anime.images.jpg.large_image_url,
                videoUrl: videoUrl,
                isYouTube: !!youtubeId
              };
            })
        );

        console.log(`Found ${processedShorts.length} anime with trailers`);

        // Only add shorts if we have some with videos
        if (processedShorts.length > 0) {
          setShorts(prev => [...prev, ...processedShorts]);
        } else {
          // If no videos found, try next page
          console.log("No anime with trailers found, trying next page");
          setPage(prev => prev + 1);
        }
        setHasMore(page < 10); // Limit to 10 pages
      } catch (error) {
        console.error("Error fetching shorts:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchShorts();
  }, [page, hasMore, loading]);

  // Load more shorts when reaching the end
  const loadMore = () => {
    if (!loading && hasMore) {
      setPage(prev => prev + 1);
    }
  };

  // Like functionality removed as requested

  // Navigate to anime page
  const navigateToAnime = (animeId) => {
    navigate(`/anime/${animeId}`);
  };

  // Navigate to watch page
  const navigateToWatch = (animeId, episodeNumber = 1) => {
    navigate(`/watch/${animeId}?episode=${episodeNumber}`);
  };

  // Handle scroll to next/previous short
  const scrollToShort = (direction) => {
    const newIndex = direction === 'next'
      ? Math.min(currentIndex + 1, shorts.length - 1)
      : Math.max(currentIndex - 1, 0);

    setCurrentIndex(newIndex);

    // Load more if approaching the end
    if (newIndex >= shorts.length - 3 && hasMore && !loading) {
      loadMore();
    }

    // Scroll to the short
    document.getElementById(`short-${newIndex}`)?.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    });
  };

  return (
    <div className="fixed inset-0 bg-black flex flex-col">
      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-50 p-4 bg-gradient-to-b from-black/80 to-transparent">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              className="rounded-full bg-black/50 text-white"
              onClick={() => navigate('/')}
            >
              <ArrowLeft size={20} />
            </Button>
            <h1 className="text-2xl font-bold text-white">Anime Shorts</h1>
          </div>
        </div>
      </div>

      {/* Shorts Container */}
      <div className="flex-1 overflow-y-auto snap-y snap-mandatory">
        {shorts.map((short, index) => (
          <ShortItem
            key={short.id}
            short={short}
            index={index}
            currentIndex={currentIndex}
            setCurrentIndex={setCurrentIndex}
            muted={muted}
            setMuted={setMuted}
            onWatchClick={() => navigateToWatch(short.animeId)}
            onInfoClick={() => navigateToAnime(short.animeId)}
            loadMore={loadMore}
            isLast={index === shorts.length - 1}
          />
        ))}

        {loading && (
          <div className="h-screen flex items-center justify-center snap-start">
            <div className="animate-pulse flex flex-col items-center">
              <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <p className="mt-4 text-white">Loading more shorts...</p>
            </div>
          </div>
        )}
      </div>

      {/* Navigation Controls */}
      <div className="absolute right-4 bottom-1/2 transform translate-y-1/2 z-50 flex flex-col gap-4">
        <Button
          variant="ghost"
          size="icon"
          className="rounded-full bg-black/50 text-white"
          onClick={() => scrollToShort('prev')}
          disabled={currentIndex === 0}
        >
          <ChevronUp size={24} />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          className="rounded-full bg-black/50 text-white"
          onClick={() => scrollToShort('next')}
          disabled={currentIndex === shorts.length - 1 && !hasMore}
        >
          <ChevronDown size={24} />
        </Button>
      </div>

      {/* Volume Control */}
      <div className="absolute left-4 bottom-4 z-50">
        <Button
          variant="ghost"
          size="icon"
          className="rounded-full bg-black/50 text-white"
          onClick={() => setMuted(!muted)}
        >
          {muted ? <VolumeX size={24} /> : <Volume2 size={24} />}
        </Button>
      </div>
    </div>
  );
};

// Individual Short Item Component
const ShortItem = ({
  short,
  index,
  setCurrentIndex,
  muted,
  setMuted,
  onWatchClick,
  onInfoClick,
  loadMore,
  isLast
}) => {
  const videoRef = useRef(null);

  // Use Intersection Observer to detect when the short is in view
  const { ref, inView } = useInView({
    threshold: 0.7, // 70% of the item must be visible
  });

  // Play/pause video based on visibility with better error handling
  useEffect(() => {
    if (videoRef.current && short.videoUrl) {
      if (inView) {
        // Handle video playback with error handling
        if (videoRef.current.play) {
          // Check if video is in a playable state
          if (videoRef.current.readyState >= 2) { // HAVE_CURRENT_DATA or better
            videoRef.current.play().catch(err => {
              console.error("Autoplay failed:", err);
              // Mark this video as having an error
              if (videoRef.current) {
                videoRef.current.dataset.playError = "true";
              }
            });
          } else {
            // Add event listener for when video becomes ready
            const handleCanPlay = () => {
              videoRef.current?.play().catch(err => {
                console.error("Delayed autoplay failed:", err);
              });
            };

            videoRef.current.addEventListener('canplay', handleCanPlay, { once: true });

            // Clean up
            return () => {
              videoRef.current?.removeEventListener('canplay', handleCanPlay);
            };
          }
        }

        setCurrentIndex(index);

        // Load more if this is the last item
        if (isLast) {
          loadMore();
        }
      } else {
        // Only pause if it's a direct video element
        if (videoRef.current.pause) {
          try {
            videoRef.current.pause();
          } catch (err) {
            console.error("Error pausing video:", err);
          }
        }
      }
    }
  }, [inView, index, setCurrentIndex, isLast, loadMore, short.videoUrl]);

  // Update mute state
  useEffect(() => {
    if (videoRef.current && videoRef.current.muted !== undefined) {
      videoRef.current.muted = muted;
    }
  }, [muted]);

  // Like functionality removed as requested

  return (
    <div
      id={`short-${index}`}
      ref={ref}
      className="h-screen w-full relative snap-start flex items-center justify-center bg-black"
    >
      {/* Video - using direct video URLs from Piped API with error handling */}
      {short.videoUrl ? (
        <video
          ref={videoRef}
          src={short.videoUrl}
          className="h-full w-full object-contain"
          loop
          muted={muted}
          playsInline
          onClick={() => setMuted(!muted)}
          onError={(e) => {
            console.error("Video error:", e);
            // Add a custom error class to help with debugging
            e.target.classList.add("video-error");
          }}
        />
      ) : (
        <div className="flex flex-col items-center justify-center text-white">
          <p>Video not available</p>
          <p className="text-sm text-gray-400 mt-2">Try another short</p>
        </div>
      )}

      {/* Overlay with gradient for better text visibility */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-black/40 pointer-events-none" />

      {/* Anime Info */}
      <div className="absolute bottom-16 left-4 right-4 text-white z-10">
        <h2 className="text-xl font-bold mb-1">{short.title}</h2>
        <p className="text-sm text-gray-300 mb-4 line-clamp-2">{short.description}</p>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button
            variant="default"
            className="bg-blue-600 hover:bg-blue-700 text-white"
            onClick={onWatchClick}
          >
            <Play size={16} className="mr-1" />
            Watch Now
          </Button>

          <Button
            variant="outline"
            className="border-white/20 bg-black/50 text-white hover:bg-black/70"
            onClick={onInfoClick}
          >
            <Info size={16} className="mr-1" />
            Details
          </Button>
        </div>
      </div>

      {/* Right Side Actions removed as requested */}
    </div>
  );
};

// Implementation Notes for Anime Shorts
/*
 * Current Implementation:
 * - Using Jikan API (MyAnimeList) to fetch anime data
 * - Using Piped API to convert YouTube videos to direct video streams
 * - Using multiple API endpoints to maximize variety and content
 * - Filtering to only include anime with trailer URLs
 * - No mock or sample videos are used
 *
 * API Details:
 * - Anime Data: Jikan API (MyAnimeList)
 *   - Multiple endpoints used in rotation:
 *     1. Top anime: https://api.jikan.moe/v4/top/anime
 *     2. Seasonal anime: https://api.jikan.moe/v4/seasons/now
 *     3. Popular anime: https://api.jikan.moe/v4/anime (with popularity sorting)
 *   - Each request fetches 25 anime to maximize chances of finding trailers
 *
 * - Video Streams: Piped API
 *   - Endpoint: https://pipedapi.kavin.rocks/streams/{youtubeId}
 *   - Converts YouTube videos to direct video streams
 *   - Selects the highest quality stream available
 *   - Falls back to original URL if Piped API fails
 *
 * Video Handling:
 * - All videos are played as direct video files (MP4)
 * - Videos automatically play when in view and pause when out of view
 * - Mute/unmute functionality is available by clicking on videos
 * - Videos are played in a loop
 *
 * Navigation:
 * - Scroll or use the up/down buttons to navigate between shorts
 * - New content loads automatically as you approach the end of the list
 * - Debug information is logged to the console for troubleshooting
 */

export default AnimeShorts;
