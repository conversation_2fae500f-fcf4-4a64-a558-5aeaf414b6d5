const types = ["movie", "tv"];
const Image_Base = "http://image.tmdb.org/t/p/";
const { images, change_keys } = {
  images: {
    base_url: Image_Base,
    secure_base_url: "https://image.tmdb.org/t/p/",
    backdrop_sizes: ["w300", "w780", "w1280", "original"],
    logo_sizes: ["w45", "w92", "w154", "w185", "w300", "w500", "original"],
    poster_sizes: ["w92", "w154", "w185", "w342", "w500", "w780", "original"],
    profile_sizes: ["w45", "w185", "h632", "original"],
    still_sizes: ["w92", "w185", "w300", "original"],
  },
  change_keys: [
    "adult",
    "air_date",
    "also_known_as",
    "alternative_titles",
    "biography",
    "birthday",
    "budget",
    "cast",
    "certifications",
    "character_names",
    "created_by",
    "crew",
    "deathday",
    "episode",
    "episode_number",
    "episode_run_time",
    "freebase_id",
    "freebase_mid",
    "general",
    "genres",
    "guest_stars",
    "homepage",
    "images",
    "imdb_id",
    "languages",
    "name",
    "network",
    "origin_country",
    "original_name",
    "original_title",
    "overview",
    "parts",
    "place_of_birth",
    "plot_keywords",
    "production_code",
    "production_companies",
    "production_countries",
    "releases",
    "revenue",
    "runtime",
    "season",
    "season_number",
    "season_regular",
    "spoken_languages",
    "status",
    "tagline",
    "title",
    "translations",
    "tvdb_id",
    "tvrage_id",
    "type",
    "video",
    "videos",
  ],
};
const gender_list = {
  0: "Not Specified",
  1: "Female",
  2: "Male",
  3: "Non-binary",
};
const tmdbImgOrignal = Image_Base + "original";
const tmdbPosterSmall = Image_Base + "w342";
const tmdbPosterBig = Image_Base + "w780";
const tmdbBannerSmall = Image_Base + "w780";
const tmdbBannerBig = Image_Base + "w1280";
export const formatDetails = (data, type) => {
  try {
    let d = {};
    const f = (x) => {
      if (!types?.includes(x?.media_type || type)) return null;
      const l = x?.images?.logos
        ?.filter((l) => l.iso_639_1 === "en" && l.aspect_ratio > 1.4)
        ?.sort((a, b) => b.aspect_ratio - a.aspect_ratio)?.[0]?.file_path;
      const logo = l ? images?.base_url + images?.logo_sizes?.[4] + l : null;

      const format_date = (d) => {
        if (!d) return null;
        const date = new Date(d);
        return new Intl.DateTimeFormat("en-US", {
          year: "numeric",
          month: "short",
          day: "numeric",
        }).format(date);
      };

      return {
        id: Number(x?.id),
        type: x?.media_type || type,
        rating: Number(x?.vote_average)?.toFixed(1),
        release_date: format_date(x?.release_date || x?.first_air_date),
        last_air_date: format_date(x?.last_air_date),
        lang: x?.original_language,
        description: x?.overview,
        title: x?.title || x?.original_title || x?.name,
        budget: x?.budget,
        revenue: x?.revenue,
        runtime: x?.runtime,
        tagline: x?.tagline,
        status: x?.status,
        images: {
          coverSmall: tmdbPosterSmall + x?.poster_path,
          coverMedium: tmdbPosterBig + x?.poster_path,
          coverLarge: tmdbPosterBig + x?.poster_path,
          bannerSmall: tmdbBannerSmall + x?.backdrop_path,
          bannerLarge: tmdbBannerBig + x?.backdrop_path,
          logo,
        },
        genres: x?.genres?.filter((g) => g),
        isAdult: x?.adult || false,
        characters: x?.credits?.cast?.map((c) => {
          return {
            character: c?.character,
            gender: gender_list?.[c?.gender],
            name: c?.name || c?.original_name,
            image:
              images?.base_url + images?.profile_sizes?.[1] + c?.profile_path,
          };
        }),
        seasons: x?.seasons
          ?.filter(
            (a) => (a?.air_date ? +new Date(a?.air_date) : 0) < Date.now()
          )
          ?.map((s) => {
            return {
              number: s?.season_number,
              image:
                images?.base_url + images.poster_sizes?.[2] + s?.poster_path,
              title: s?.name,
              airdate: s?.air_date ? +new Date(s?.air_date) : null,
            };
          })
          ?.filter((s) => s?.number !== 0),
        languages: x?.spoken_languages?.map((l) => {
          return l?.name;
        }),
        production_companies: x?.production_companies?.map((p) => {
          return {
            id: p?.id,
            name: p?.name,
            country: p?.logo_path,
            logo: Image_Base + images.logo_sizes?.[3] + p?.logo_path,
          };
        }),
        videos: x?.videos?.results?.map((v) => {
          if (!v?.key) return;
          return {
            url: `https://www.youtube.com/embed/${v?.key}?controls=1&loop=1&autoplay=1&mute=0`,
            title: v?.name || "",
            type: v?.type || "",
            date: v?.published_at?.slice(0, 10) || "",
          };
        }),
        total_episodes: x?.number_of_episodes,
        total_seasons: x?.number_of_seasons,
      };
    };

    if (Array.isArray(data)) {
      const fd = data?.filter((x) =>
        types?.includes(x?.type || x?.media_type || type)
      );
      d = fd?.map((x) => {
        return f(x);
      });
    } else {
      d = f(data);
      d.recommendations = data?.recommendations?.results?.map((r) => f(r));
    }
    return d;
  } catch (e) {
    console.log("format", e?.message);
    return data;
  }
};
export const formatEpisodes = (eps) => {
  return eps
    ?.filter((x) => (x?.air_date ? +new Date(x?.air_date) : 0) < Date.now())
    ?.map((e) => {
      return {
        number: e?.episode_number,
        title: e?.name,
        description: e?.overview,
        runtime: e?.runtime,
        airdate: e?.air_date,
        image: Image_Base + images?.still_sizes?.[2] + e?.still_path,
      };
    });
};

// Format function for AniList anime data
export const formatAnimeDetails = (data) => {
  try {
    const format_date = (year, month, day) => {
      if (!year) return null;
      const date = new Date(year, (month || 1) - 1, day || 1);
      return new Intl.DateTimeFormat("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      }).format(date);
    };

    const formatSingleAnime = (anime) => {
      if (!anime) return null;

      return {
        id: Number(anime.id),
        type: "anime",
        rating: (anime.averageScore / 10).toFixed(1),
        release_date: format_date(
          anime.startDate?.year,
          anime.startDate?.month,
          anime.startDate?.day
        ),
        end_date: format_date(
          anime.endDate?.year,
          anime.endDate?.month,
          anime.endDate?.day
        ),
        description: anime.description,
        title: anime.title?.english || anime.title?.romaji || anime.title?.native,
        episodes: anime.episodes,
        status: anime.status,
        images: {
          coverSmall: anime.coverImage?.medium,
          coverMedium: anime.coverImage?.large,
          coverLarge: anime.coverImage?.extraLarge || anime.coverImage?.large,
          bannerSmall: anime.bannerImage,
          bannerLarge: anime.bannerImage,
        },
        genres: anime.genres?.map(genre => ({ name: genre })),
        isAdult: anime.isAdult || false,
        characters: anime.characters?.nodes?.map((c) => {
          return {
            character: "",
            gender: c.gender || "Not Specified",
            name: c.name?.full,
            image: c.image?.medium,
          };
        }),
        studios: anime.studios?.nodes?.map((s) => {
          return {
            id: s.id,
            name: s.name,
          };
        }),
        trailer: anime.trailer ? {
          id: anime.trailer.id,
          url: `https://www.youtube.com/watch?v=${anime.trailer.id}`,
          embedUrl: `https://www.youtube.com/embed/${anime.trailer.id}`,
          site: anime.trailer.site?.toLowerCase() || "youtube",
          thumbnail: anime.trailer.thumbnail,
        } : null,
        recommendations: anime.recommendations?.nodes?.map((r) => {
          const rec = r.mediaRecommendation;
          return {
            id: rec.id,
            type: "anime",
            title: rec.title?.english || rec.title?.romaji,
            rating: (rec.averageScore / 10).toFixed(1),
            images: {
              coverSmall: rec.coverImage?.medium,
              coverMedium: rec.coverImage?.large || rec.coverImage?.medium,
              coverLarge: rec.coverImage?.extraLarge || rec.coverImage?.large || rec.coverImage?.medium,
            },
          };
        }),
        seasonYear: anime.seasonYear,
        relations: anime.relations?.edges?.map((edge) => {
          const related = edge.node;
          return {
            id: related.id,
            type: related.type?.toLowerCase() || "anime",
            relationType: edge.relationType,
            title: related.title?.english || related.title?.romaji,
            format: related.format,
            status: related.status,
            episodes: related.episodes,
            rating: (related.averageScore / 10).toFixed(1),
            images: {
              coverSmall: related.coverImage?.medium,
              coverMedium: related.coverImage?.large || related.coverImage?.medium,
              coverLarge: related.coverImage?.large || related.coverImage?.medium,
            },
          };
        }),
      };
    };

    if (Array.isArray(data)) {
      return data.map(formatSingleAnime).filter(Boolean);
    } else {
      return formatSingleAnime(data);
    }
  } catch (e) {
    console.log("format anime", e?.message);
    return data;
  }
};
