/**
 * Format anime details from AniList API response
 * @param {Object|Array} data - Anime data from AniList API
 * @returns {Object|Array} - Formatted anime data
 */
export const formatAnimeDetails = (data) => {
  // If data is null or undefined, return null
  if (!data) return null;

  // If data is an array, map over each item
  if (Array.isArray(data)) {
    return data.map(item => formatSingleAnime(item));
  }

  // Otherwise, format a single anime object
  return formatSingleAnime(data);
};

/**
 * Format a single anime object
 * @param {Object} anime - Single anime object from AniList API
 * @returns {Object} - Formatted anime object
 */
const formatSingleAnime = (anime) => {
  if (!anime) return null;

  // Extract title information
  const title = anime.title?.english || anime.title?.romaji || "Unknown Title";
  const titleRomaji = anime.title?.romaji || anime.title?.english || "Unknown Title";
  const titleNative = anime.title?.native || "";

  // Extract cover images
  const coverLarge = anime.coverImage?.large || anime.coverImage?.medium || "";
  const coverMedium = anime.coverImage?.medium || "";
  const coverSmall = anime.coverImage?.medium || "";

  // Format studios
  const studios = anime.studios?.nodes?.map(studio => ({
    id: studio.id,
    name: studio.name
  })) || [];

  // Format recommendations
  const recommendations = anime.recommendations?.nodes?.map(rec => {
    const media = rec.mediaRecommendation;
    if (!media) return null;

    return {
      id: media.id,
      title: media.title?.english || media.title?.romaji || "Unknown Title",
      titleRomaji: media.title?.romaji || media.title?.english || "Unknown Title",
      images: {
        coverLarge: media.coverImage?.large || media.coverImage?.medium || "",
        coverMedium: media.coverImage?.medium || "",
        coverSmall: media.coverImage?.medium || ""
      },
      rating: media.averageScore ? (media.averageScore / 10).toFixed(1) : null
    };
  }).filter(Boolean) || [];

  // Format relations
  const relations = anime.relations?.edges?.map(edge => {
    const node = edge.node;
    if (!node) return null;

    return {
      id: node.id,
      title: node.title?.english || node.title?.romaji || "Unknown Title",
      titleRomaji: node.title?.romaji || node.title?.english || "Unknown Title",
      type: node.type || "ANIME",
      format: node.format || "",
      status: node.status || "",
      episodes: node.episodes || 0,
      images: {
        coverLarge: node.coverImage?.large || node.coverImage?.medium || "",
        coverMedium: node.coverImage?.medium || "",
        coverSmall: node.coverImage?.medium || ""
      },
      rating: node.averageScore ? (node.averageScore / 10).toFixed(1) : null,
      relationType: edge.relationType || ""
    };
  }).filter(Boolean) || [];

  // Format genres
  const genres = anime.genres?.map(genre => ({
    name: genre
  })) || [];

  // Format start date
  const startDate = anime.startDate;
  const releaseDate = startDate ?
    `${startDate.year || ""}${startDate.month ? `-${String(startDate.month).padStart(2, '0')}` : ""}${startDate.day ? `-${String(startDate.day).padStart(2, '0')}` : ""}`
    : "";

  // Return formatted anime object
  return {
    id: anime.id,
    title,
    titleRomaji,
    titleNative,
    description: anime.description || "",
    type: anime.type || "ANIME",
    episodes: anime.episodes || 0,
    status: anime.status || "",
    genres,
    rating: anime.averageScore ? (anime.averageScore / 10).toFixed(1) : null,
    popularity: anime.popularity || 0,
    release_date: releaseDate,
    year: anime.seasonYear || (startDate?.year || null),
    season: anime.season || "",
    isAdult: anime.isAdult || false,
    nextAiringEpisode: anime.nextAiringEpisode ? {
      airingAt: anime.nextAiringEpisode.airingAt,
      timeUntilAiring: anime.nextAiringEpisode.timeUntilAiring,
      episode: anime.nextAiringEpisode.episode
    } : null,
    images: {
      bannerLarge: anime.bannerImage || "",
      coverLarge,
      coverMedium,
      coverSmall
    },
    trailer: anime.trailer ? {
      id: anime.trailer.id || "",
      site: anime.trailer.site || "",
      thumbnail: anime.trailer.thumbnail || ""
    } : null,
    studios,
    recommendations,
    relations
  };
};
