import { useEffect } from "react";
import { Mail, Heart, Code, Globe } from "lucide-react";
import { <PERSON> } from "react-router-dom";

const About = () => {
  // Set page title
  useEffect(() => {
    document.title = "About Us | AnimeHQ";
    return () => {
      document.title = "AnimeHQ"; // Reset title when component unmounts
    };
  }, []);

  return (
    <div className="py-8 px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-8 text-center">About AnimeHQ</h1>

      {/* Hero Section */}
      <div className="relative rounded-2xl overflow-hidden mb-16">
        <div className="absolute inset-0">
          <img
            src="https://wallpapercave.com/wp/wp11501349.jpg"
            alt="AnimeHQ Banner"
            className="w-full h-full object-cover opacity-50"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black via-black/80 to-black/70"></div>
        </div>

        <div className="relative z-10 py-16 px-6 sm:px-12 text-center">
          <h2 className="text-4xl font-bold mb-4 text-white">
            Our Mission
          </h2>
          <p className="text-xl text-white/90 max-w-3xl mx-auto">
            To create the ultimate platform for anime enthusiasts to discover, track, and enjoy their favorite shows in one seamless experience.
          </p>
        </div>
      </div>

      {/* Our Story Section */}
      <div className="mb-16">
        <h2 className="text-2xl font-bold mb-6 flex items-center gap-2">
          <Heart size={24} className="text-white" />
          Our Story
        </h2>

        <div className="bg-white/5 rounded-xl p-6 backdrop-blur-sm border border-white/10">
          <p className="text-gray-300 mb-4">
            AnimeHQ began in 2023 as a passion project by a small group of anime fans. Frustrated with the limitations of existing platforms, we set out to create something better—a platform that truly understands what anime fans want.
          </p>
          <p className="text-gray-300 mb-4">
            What started as a simple tracking tool has evolved into a comprehensive platform for discovering, watching, and discussing anime. Our community has grown beyond our wildest expectations, and we're constantly working to improve and expand our offerings.
          </p>
          <p className="text-gray-300">
            Today, AnimeHQ is more than just a website—it's a community of passionate anime fans from around the world, united by their love for this unique art form.
          </p>
        </div>
      </div>

      {/* Features Section */}
      <div className="mb-16">
        <h2 className="text-2xl font-bold mb-6 flex items-center gap-2">
          <Code size={24} className="text-white" />
          What Makes Us Different
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white/5 rounded-xl p-6 backdrop-blur-sm border border-white/10">
            <div className="w-12 h-12 bg-white/10 rounded-full flex items-center justify-center mb-4">
              <Globe size={24} className="text-white" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Comprehensive Library</h3>
            <p className="text-gray-300">
              Access thousands of anime titles from classics to the latest releases, all in one place.
            </p>
          </div>

          <div className="bg-white/5 rounded-xl p-6 backdrop-blur-sm border border-white/10">
            <div className="w-12 h-12 bg-white/10 rounded-full flex items-center justify-center mb-4">
              <Code size={24} className="text-white" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Cutting-Edge Technology</h3>
            <p className="text-gray-300">
              Built with the latest web technologies to provide a fast, responsive, and seamless experience.
            </p>
          </div>

          <div className="bg-white/5 rounded-xl p-6 backdrop-blur-sm border border-white/10">
            <div className="w-12 h-12 bg-white/10 rounded-full flex items-center justify-center mb-4">
              <Heart size={24} className="text-white" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Community-Driven</h3>
            <p className="text-gray-300">
              We listen to our users and continuously improve based on community feedback and suggestions.
            </p>
          </div>
        </div>
      </div>

      {/* Contact Section */}
      <div className="mb-16">
        <h2 className="text-2xl font-bold mb-6 flex items-center gap-2">
          <Mail className="text-white" size={24} />
          Get in Touch
        </h2>

        <div className="bg-white/5 rounded-xl p-6 backdrop-blur-sm border border-white/10">
          <p className="text-gray-300 mb-6">
            Have questions, suggestions, or just want to say hello? We'd love to hear from you!
          </p>

          <div className="flex flex-col sm:flex-row gap-4">
            <a
              href="mailto:<EMAIL>"
              className="flex items-center justify-center gap-2 bg-white/10 hover:bg-white/20 transition-colors rounded-lg px-6 py-3 text-white"
            >
              <Mail size={18} />
              Email Us
            </a>

            <Link
              to="/donate"
              className="flex items-center justify-center gap-2 bg-white/10 hover:bg-white/20 transition-colors rounded-lg px-6 py-3 text-white border border-white/20"
            >
              <Heart size={18} />
              Support Us
            </Link>
          </div>
        </div>
      </div>

      {/* Legal Links */}
      <div className="text-center text-gray-400 text-sm">
        <div className="flex flex-wrap justify-center gap-4">
          <Link to="/terms" className="hover:text-white transition-colors">Terms of Service</Link>
          <Link to="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link>
          <Link to="/dmca" className="hover:text-white transition-colors">DMCA Policy</Link>
        </div>
      </div>
    </div>
  );
};

export default About;
