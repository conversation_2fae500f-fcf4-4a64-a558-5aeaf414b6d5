/**
 * API configuration
 * This file contains configuration for API endpoints
 */

// Backend port
const BACKEND_PORT = 3003;

// Get the current hostname or IP address
const getCurrentHost = () => {
  // Use the current window location to determine the API host
  const { protocol, hostname } = window.location;

  // If we're on localhost, use the backend port directly
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return `${hostname}:${BACKEND_PORT}`;
  }

  // For production or when accessed via IP, use the same hostname with backend port
  return `${hostname}:${BACKEND_PORT}`;
};

// Base URLs for APIs
export const API_URLS = {
  // Backend API base URL - dynamically set based on current host
  BACKEND_API: `${window.location.protocol}//${getCurrentHost()}/api`,
};

// Log the API URL for debugging
console.log('Using API URL:', API_URLS.BACKEND_API);

export default API_URLS;
