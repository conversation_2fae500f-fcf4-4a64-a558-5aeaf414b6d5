import { createContext, useContext, useState, useEffect } from 'react';
import { useAniList } from '@/hooks/useAniList';
import { useMainContext } from './MainContext';
import { toast } from 'sonner';
import { getRecentComments } from '@/api/comments';

// Create the context
export const NotificationContext = createContext();

// Custom hook to use the notification context
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

export const NotificationProvider = ({ children }) => {
  const { isAuthenticated, user } = useAniList();
  const { getStorage, setStorage } = useMainContext();
  
  // State for notifications
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  
  // Load notifications from localStorage on mount
  useEffect(() => {
    if (isAuthenticated && user?.id) {
      const savedNotifications = getStorage(`notifications-${user.id}`) || [];
      setNotifications(savedNotifications);
      
      // Calculate unread count
      const unread = savedNotifications.filter(notification => !notification.read).length;
      setUnreadCount(unread);
    }
  }, [isAuthenticated, user, getStorage]);
  
  // Save notifications to localStorage whenever they change
  useEffect(() => {
    if (isAuthenticated && user?.id && notifications.length > 0) {
      setStorage(`notifications-${user.id}`, notifications);
    }
  }, [notifications, isAuthenticated, user, setStorage]);
  
  // Check for new episode releases from user's watchlist
  useEffect(() => {
    if (!isAuthenticated) return;
    
    const checkNewEpisodes = async () => {
      try {
        // Get user's watchlist
        const watchlist = getStorage('watchlist') || [];
        if (watchlist.length === 0) return;
        
        // Get last check time
        const lastCheckTime = getStorage(`last-episode-check-${user.id}`) || 0;
        const currentTime = Date.now();
        
        // Only check once per hour
        if (currentTime - lastCheckTime < 3600000) return;
        
        // Update last check time
        setStorage(`last-episode-check-${user.id}`, currentTime);
        
        // For each anime in watchlist, check if there are new episodes
        // This would typically involve an API call to check for new episodes
        // For now, we'll simulate this with a random check
        
        // Simulate finding new episodes for 1-2 random anime in watchlist
        const randomCount = Math.floor(Math.random() * 2) + 1;
        const randomIndices = [];
        
        for (let i = 0; i < randomCount && i < watchlist.length; i++) {
          let randomIndex;
          do {
            randomIndex = Math.floor(Math.random() * watchlist.length);
          } while (randomIndices.includes(randomIndex));
          
          randomIndices.push(randomIndex);
          
          const anime = watchlist[randomIndex];
          const episodeNumber = Math.floor(Math.random() * 12) + 1;
          
          // Create notification
          addNotification({
            type: 'episode',
            title: `New Episode Available`,
            message: `Episode ${episodeNumber} of ${anime.title} is now available!`,
            data: {
              animeId: anime.id,
              episodeNumber,
              image: anime.image
            },
            timestamp: Date.now()
          });
        }
      } catch (error) {
        console.error('Error checking for new episodes:', error);
      }
    };
    
    // Check immediately on mount
    checkNewEpisodes();
    
    // Set up interval to check periodically (every hour)
    const intervalId = setInterval(checkNewEpisodes, 3600000);
    
    return () => clearInterval(intervalId);
  }, [isAuthenticated, user, getStorage, setStorage]);
  
  // Check for comment replies
  useEffect(() => {
    if (!isAuthenticated) return;
    
    const checkCommentReplies = async () => {
      try {
        // Get last check time
        const lastCheckTime = getStorage(`last-comment-check-${user.id}`) || 0;
        const currentTime = Date.now();
        
        // Only check once per 15 minutes
        if (currentTime - lastCheckTime < 900000) return;
        
        // Update last check time
        setStorage(`last-comment-check-${user.id}`, currentTime);
        
        // Get recent comments
        const recentComments = await getRecentComments(20);
        
        // Filter for replies to user's comments
        const userReplies = recentComments.filter(comment => 
          comment.replyTo && comment.replyTo.userId === user.id && 
          comment.timestamp > lastCheckTime
        );
        
        // Create notifications for new replies
        userReplies.forEach(reply => {
          addNotification({
            type: 'comment',
            title: 'New Comment Reply',
            message: `${reply.username} replied to your comment: "${reply.content.substring(0, 30)}${reply.content.length > 30 ? '...' : ''}"`,
            data: {
              commentId: reply.id,
              animeId: reply.animeId,
              episodeId: reply.episodeId
            },
            timestamp: reply.timestamp
          });
        });
      } catch (error) {
        console.error('Error checking for comment replies:', error);
      }
    };
    
    // Check immediately on mount
    checkCommentReplies();
    
    // Set up interval to check periodically (every 15 minutes)
    const intervalId = setInterval(checkCommentReplies, 900000);
    
    return () => clearInterval(intervalId);
  }, [isAuthenticated, user, getStorage, setStorage]);
  
  // Add a new notification
  const addNotification = (notification) => {
    setNotifications(prev => {
      // Check if this notification already exists to avoid duplicates
      const exists = prev.some(n => 
        n.type === notification.type && 
        JSON.stringify(n.data) === JSON.stringify(notification.data)
      );
      
      if (exists) return prev;
      
      // Add new notification at the beginning
      const newNotifications = [
        { ...notification, id: Date.now(), read: false },
        ...prev
      ];
      
      // Limit to 50 notifications
      if (newNotifications.length > 50) {
        newNotifications.pop();
      }
      
      // Update unread count
      setUnreadCount(prevCount => prevCount + 1);
      
      // Show toast for new notification
      toast(notification.title, {
        description: notification.message,
        duration: 5000
      });
      
      return newNotifications;
    });
  };
  
  // Mark a notification as read
  const markAsRead = (notificationId) => {
    setNotifications(prev => {
      const updated = prev.map(notification => {
        if (notification.id === notificationId && !notification.read) {
          setUnreadCount(prevCount => Math.max(0, prevCount - 1));
          return { ...notification, read: true };
        }
        return notification;
      });
      return updated;
    });
  };
  
  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotifications(prev => {
      const updated = prev.map(notification => ({ ...notification, read: true }));
      setUnreadCount(0);
      return updated;
    });
  };
  
  // Clear all notifications
  const clearAll = () => {
    setNotifications([]);
    setUnreadCount(0);
  };
  
  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        addNotification,
        markAsRead,
        markAllAsRead,
        clearAll
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};
