import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import Image from "@/components/ui/Image";
import { Link } from "react-router-dom";
import { Dot, Star } from "lucide-react";
import { getTrending } from "@/api/tmdb";
import useFetch from "@/hooks/useFetch";
import Autoplay from "embla-carousel-autoplay";
const Trending = () => {
  const { data } = useFetch({
    key: ["trendinall"],
    fun: async () => {
      return (await getTrending("all")) || null;
    },
    placeholderData: [],
  });
  return (
    <div className="w-full flex flex-col gap-2">
      <div className="flex text-xl lg:text-2xl font-medium pl-1">
        What's Trending Today
      </div>
      <div className="w-full">
        <Carousel
          plugins={[
            Autoplay({
              delay: 4000,
            }),
          ]}
          opts={{ dragFree: true, dragThreshold: 100 }}
          className="w-full"
        >
          <CarouselContent className="w-full">
            {data?.length
              ? data?.map((x) => {
                  return (
                    <CarouselItem
                      key={x?.id}
                      className="basis-[80%] sm:basis-[40%] lg:basis-1/4 p-[.4rem] aspect-[1.88/1] sm:p-2"
                    >
                      <Link
                        title={x?.title + " - " + x?.description}
                        to={x?.id ? `/watch/${x?.type}/${x?.id}` : ""}
                        className="flex size-full flex-col gap-2 group pp cursor-pointer hover:ring-2 smooth ring-white rounded-2xl lg:rounded-3xl overflow-hidden relative"
                      >
                        <div className="flex size-full pointer-events-none bg-white/10 lg:bg-white/5 smooth absolute top-0 left-0 z-[-1] group-hover:scale-105">
                          <Image src={x?.images?.bannerSmall} />
                        </div>
                        <div className="flex size-full flex-col justify-end bg-gradient-to-t !select-none from-black/65 pb-1 px-3">
                          <div className="flex !line-clamp-2 font-semibold uppercase !leading-tight drop-shadow-lg">
                            {x?.title}
                          </div>
                          <div className="flex items-center text-xs !leading-tight">
                            {x?.rating ? (
                              <span className="flex items-center gap-[1px] !leading-none">
                                <Star size={12} fill="white" />
                                <span>{x?.rating}</span>
                                <Dot />
                              </span>
                            ) : (
                              ""
                            )}
                            {x?.release_date ? (
                              <>
                                <span>{x?.release_date}</span>
                                <Dot />
                              </>
                            ) : (
                              ""
                            )}
                            {x?.lang ? (
                              <span className="uppercase">{x?.lang}</span>
                            ) : (
                              ""
                            )}
                          </div>
                        </div>
                      </Link>
                    </CarouselItem>
                  );
                })
              : Array.from({ length: 6 })?.map(() => {
                  return (
                    <CarouselItem className="basis-[80%] sm:basis-[40%] lg:basis-1/4 p-[.4rem] aspect-[1.88/1] sm:p-2">
                      <div className="size-full bg-white/5 rounded-2xl lg:rounded-3xl overflow-hidden"></div>
                    </CarouselItem>
                  );
                })}
          </CarouselContent>
        </Carousel>
      </div>
    </div>
  );
};

export default Trending;
