import { useState } from "react";
import { useMainContext } from "./useContexts";

const useWatchlist = () => {
  const { getStorage, setStorage } = useMainContext();
  const [watchlist, setW] = useState(getStorage("watchlist") || []);

  const inList = (id, type) => {
    return watchlist.some(
      (x) => Number(x?.id) === Number(id) && x?.type === type
    );
  };

  const setWatchlist = (n) => {
    setW((p) => {
      const u = typeof n === "function" ? n(p) : n;
      setStorage("watchlist", u);
      return u;
    });
  };

  const addToWatchList = (i) => {
    setWatchlist((p) => [...p, { ...i, dateAdded: Date.now() }]);
  };

  const removeFromWatchList = (id, type) => {
    setWatchlist((p) =>
      p.filter((x) => !(Number(x?.id) === Number(id) && x?.type === type))
    );
  };

  return {
    watchlist,
    setWatchlist,
    inList,
    addToWatchList,
    removeFromWatchList,
  };
};

export default useWatchlist;
