import mongoose from 'mongoose';

// Reply schema (nested in comments)
const ReplySchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true
  },
  userName: {
    type: String,
    required: true
  },
  userAvatar: {
    type: String,
    default: ''
  },
  content: {
    type: String,
    required: true,
    trim: true
  },
  hasSpoiler: {
    type: Boolean,
    default: false
  },
  likes: {
    type: Number,
    default: 0
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

const CommentSchema = new mongoose.Schema({
  animeId: {
    type: String,
    required: true,
    index: true
  },
  animeTitle: {
    type: String,
    default: 'Unknown Anime'
  },
  animeBanner: {
    type: String,
    default: ''
  },
  episodeNumber: {
    type: Number,
    required: true,
    index: true
  },
  userId: {
    type: String,
    required: true,
    index: true
  },
  userName: {
    type: String,
    required: true
  },
  userAvatar: {
    type: String,
    default: ''
  },
  content: {
    type: String,
    required: true,
    trim: true
  },
  hasSpoiler: {
    type: Boolean,
    default: false
  },
  likes: {
    type: Number,
    default: 0
  },
  replies: [ReplySchema],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create a compound index for faster queries
CommentSchema.index({ animeId: 1, episodeNumber: 1 });

const Comment = mongoose.model('Comment', CommentSchema);

export default Comment;
