import { useEffect, useState } from "react";
import { Co<PERSON>, Check, Heart, AlertCircle, ArrowUp } from "lucide-react";
import { QRCodeSVG } from "qrcode.react";

const Donate = () => {
  // State for scroll-to-top button
  const [showScrollTop, setShowScrollTop] = useState(false);

  // Set page title
  useEffect(() => {
    document.title = "Support AnimeHQ";
    return () => {
      document.title = "AnimeHQ"; // Reset title when component unmounts
    };
  }, []);

  // Handle scroll event for scroll-to-top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // Cryptocurrency donation options
  const cryptoOptions = [
    {
      name: "Bitcoin (BTC)",
      address: "******************************************",
      symbol: "₿",
      color: "bg-white/10",
      textColor: "text-white"
    },
    {
      name: "Ethereum (ETH)",
      address: "******************************************",
      symbol: "Ξ",
      color: "bg-white/10",
      textColor: "text-white"
    },
    {
      name: "Solana (SOL)",
      address: "8ZUgCCZpMUNWDnXnUzHRUQWHYxz2xLXQQxvL7TXcFwXg",
      symbol: "◎",
      color: "bg-white/10",
      textColor: "text-white"
    },
    {
      name: "Cardano (ADA)",
      address: "addr1qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlh",
      symbol: "₳",
      color: "bg-white/10",
      textColor: "text-white"
    }
  ];

  // State for copy button
  const [copiedAddress, setCopiedAddress] = useState(null);

  // Handle copy to clipboard
  const handleCopy = (address) => {
    navigator.clipboard.writeText(address);
    setCopiedAddress(address);

    // Reset copied state after 2 seconds
    setTimeout(() => {
      setCopiedAddress(null);
    }, 2000);
  };

  return (
    <div className="py-8 px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto">
      {/* Hero Section */}
      <div className="relative rounded-2xl overflow-hidden mb-16 group">
        <div className="absolute inset-0">
          <img
            src="https://wallpapercave.com/wp/wp11501349.jpg"
            alt="Support Banner"
            className="w-full h-full object-cover opacity-40 transition-transform duration-10000 ease-in-out group-hover:scale-105"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/90 via-black/70 to-black/90"></div>

          {/* Animated particles */}
          <div className="absolute inset-0 overflow-hidden opacity-20">
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className="absolute rounded-full bg-white/30 animate-float"
                style={{
                  width: `${Math.random() * 10 + 5}px`,
                  height: `${Math.random() * 10 + 5}px`,
                  top: `${Math.random() * 100}%`,
                  left: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 5}s`,
                  animationDuration: `${Math.random() * 10 + 10}s`
                }}
              />
            ))}
          </div>
        </div>

        <style jsx>{`
          @keyframes float {
            0% { transform: translateY(0) translateX(0); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateY(-100px) translateX(20px); opacity: 0; }
          }
          .animate-float {
            animation: float 15s ease-in-out infinite;
          }
        `}</style>

        <div className="relative z-10 py-20 px-6 sm:px-12 text-center">
          <div className="inline-block mb-6">
            <div className="w-16 h-16 bg-white/10 backdrop-blur-md rounded-full flex items-center justify-center mx-auto border border-white/20">
              <Heart size={32} className="text-white" />
            </div>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white tracking-tight">
            Support AnimeHQ
          </h2>
          <p className="text-xl text-white/80 max-w-2xl mx-auto leading-relaxed">
            AnimeHQ is a passion project created by anime fans for anime fans. Your support helps us maintain and improve the platform for everyone.
          </p>
          <div className="mt-8 flex flex-wrap justify-center gap-4">
            <a href="#crypto-donations" className="px-6 py-3 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white rounded-lg transition-all duration-300 border border-white/10 hover:border-white/30 shadow-lg hover:shadow-xl">
              Donate Now
            </a>
            <a href="#why-support" className="px-6 py-3 bg-black/40 hover:bg-black/60 backdrop-blur-sm text-white rounded-lg transition-all duration-300 border border-white/10 hover:border-white/30">
              Learn More
            </a>
          </div>
        </div>
      </div>

      {/* Why Support Section */}
      <div id="why-support" className="mb-16 scroll-mt-8">
        <h2 className="text-2xl font-bold mb-8 flex items-center gap-2 justify-center">
          <div className="w-10 h-10 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/20">
            <Heart className="text-white" size={20} />
          </div>
          <span>Why Support Us</span>
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300 hover:transform hover:scale-[1.02]">
            <div className="w-12 h-12 bg-white/10 rounded-full flex items-center justify-center mb-4 border border-white/20">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                <rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect>
                <rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect>
                <line x1="6" y1="6" x2="6.01" y2="6"></line>
                <line x1="6" y1="18" x2="6.01" y2="18"></line>
              </svg>
            </div>
            <h3 className="text-white font-medium text-lg mb-2">Server Costs</h3>
            <p className="text-white/70 text-sm">Your support helps us cover server and hosting costs to keep AnimeHQ running smoothly.</p>
          </div>

          <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300 hover:transform hover:scale-[1.02]">
            <div className="w-12 h-12 bg-white/10 rounded-full flex items-center justify-center mb-4 border border-white/20">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                <path d="M12 2v4"></path>
                <path d="M12 18v4"></path>
                <path d="m4.93 4.93 2.83 2.83"></path>
                <path d="m16.24 16.24 2.83 2.83"></path>
                <path d="M2 12h4"></path>
                <path d="M18 12h4"></path>
                <path d="m4.93 19.07 2.83-2.83"></path>
                <path d="m16.24 7.76 2.83-2.83"></path>
              </svg>
            </div>
            <h3 className="text-white font-medium text-lg mb-2">Ad-Free Experience</h3>
            <p className="text-white/70 text-sm">We're committed to providing an ad-free experience, and your donations help make this possible.</p>
          </div>

          <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300 hover:transform hover:scale-[1.02]">
            <div className="w-12 h-12 bg-white/10 rounded-full flex items-center justify-center mb-4 border border-white/20">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                <path d="m8 3 4 8 5-5 5 15H2L8 3z"></path>
              </svg>
            </div>
            <h3 className="text-white font-medium text-lg mb-2">New Features</h3>
            <p className="text-white/70 text-sm">Your support enables us to develop new features and improve the platform for everyone.</p>
          </div>
        </div>

        <div className="bg-white/5 rounded-xl p-6 backdrop-blur-sm border border-white/10">
          <p className="text-white/80 mb-4 text-center max-w-3xl mx-auto">
            AnimeHQ is completely free to use, but running and maintaining a platform like this comes with costs. Your donations make a real difference in keeping our service available to anime fans worldwide.
          </p>

          <div className="flex flex-wrap justify-center gap-4 mt-6">
            <div className="flex items-center gap-2 bg-white/10 px-4 py-2 rounded-lg">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
                <path d="m9 12 2 2 4-4"></path>
              </svg>
              <span className="text-white/80 text-sm">API Integrations</span>
            </div>
            <div className="flex items-center gap-2 bg-white/10 px-4 py-2 rounded-lg">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
                <path d="m9 12 2 2 4-4"></path>
              </svg>
              <span className="text-white/80 text-sm">Server Performance</span>
            </div>
            <div className="flex items-center gap-2 bg-white/10 px-4 py-2 rounded-lg">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
                <path d="m9 12 2 2 4-4"></path>
              </svg>
              <span className="text-white/80 text-sm">Continuous Availability</span>
            </div>
          </div>
        </div>
      </div>

      {/* Crypto Donations */}
      <div id="crypto-donations" className="mb-16 scroll-mt-8">
        <h2 className="text-2xl font-bold mb-8 flex items-center gap-2 justify-center">
          <div className="w-10 h-10 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/20">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
              <path d="M11.767 19.089c4.924.868 6.14-6.025 1.216-6.894m-1.216 6.894L5.86 18.047m5.908 1.042-.347 1.97m1.563-8.864c4.924.869 6.14-6.025 1.215-6.893m-1.215 6.893-3.94-.694m5.155-6.2L8.29 4.26m5.908 1.042.348-1.97"></path>
            </svg>
          </div>
          <span>Cryptocurrency Donations</span>
        </h2>

        <div className="bg-white/5 rounded-xl p-8 backdrop-blur-sm border border-white/10 mb-8">
          <div className="max-w-3xl mx-auto text-center mb-8">
            <p className="text-white/80 mb-6">
              We accept donations in various cryptocurrencies. Simply scan the QR code or send your contribution to one of the addresses below.
            </p>

            <div className="bg-gradient-to-r from-white/5 to-white/10 rounded-xl p-5 backdrop-blur-sm border border-white/10 inline-block">
              <div className="flex items-center gap-3 text-left">
                <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center border border-white/20">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                    <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                  </svg>
                </div>
                <div>
                  <p className="text-white font-medium">After donating, please contact us to:</p>
                  <ul className="list-disc pl-6 text-white/70 text-sm mt-1 space-y-1">
                    <li>Add your name to our supporters list</li>
                    <li>Receive an exclusive Discord role</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {cryptoOptions.map((crypto, index) => (
              <div
                key={index}
                className="bg-gradient-to-br from-black/40 to-black/20 rounded-xl p-6 backdrop-blur-sm border border-white/10 hover:border-white/20 transition-all duration-300 hover:shadow-xl group"
              >
                <div className="flex items-center gap-3 mb-5">
                  <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center border border-white/20 group-hover:bg-white/20 transition-all duration-300">
                    <span className="text-lg font-bold">{crypto.symbol}</span>
                  </div>
                  <h3 className="text-xl font-semibold text-white">{crypto.name}</h3>
                </div>

                {/* QR Code */}
                <div className="mb-5 bg-white p-4 rounded-lg flex justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                  <QRCodeSVG
                    value={crypto.address}
                    size={160}
                    bgColor="#FFFFFF"
                    fgColor="#000000"
                    level="H"
                    className="group-hover:scale-105 transition-all duration-300"
                  />
                </div>

                {/* Address with copy button */}
                <div className="bg-black/40 rounded-lg p-4 flex items-center justify-between gap-2 break-all border border-white/5 group-hover:border-white/10 transition-all duration-300">
                  <code className="text-xs sm:text-sm text-white/80 font-mono">{crypto.address}</code>
                  <button
                    onClick={() => handleCopy(crypto.address)}
                    className="shrink-0 bg-white/10 hover:bg-white/20 transition-colors rounded-lg p-2.5 border border-white/10 hover:border-white/20"
                    aria-label="Copy address"
                  >
                    {copiedAddress === crypto.address ? (
                      <Check size={18} className="text-white" />
                    ) : (
                      <Copy size={18} className="text-white/80" />
                    )}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Important Note */}
        <div className="bg-gradient-to-r from-black/60 to-black/40 rounded-xl p-5 backdrop-blur-sm border border-white/10 max-w-3xl mx-auto">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-white/10 rounded-full flex items-center justify-center shrink-0 border border-white/20">
              <AlertCircle size={24} className="text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">Important Note</h3>
              <p className="text-white/70 text-sm leading-relaxed">
                Please double-check the addresses before sending any cryptocurrency. Transactions are irreversible, and we cannot recover funds sent to incorrect addresses.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Thank You Section */}
      <div className="text-center mb-16">
        <div className="bg-gradient-to-b from-white/5 to-transparent p-8 rounded-2xl backdrop-blur-sm border border-white/10 max-w-4xl mx-auto">
          <div className="w-16 h-16 bg-white/10 backdrop-blur-md rounded-full flex items-center justify-center mx-auto mb-6 border border-white/20">
            <Heart size={32} className="text-white" />
          </div>
          <h2 className="text-3xl font-bold mb-4 text-white">Thank You for Your Support!</h2>
          <p className="text-white/80 max-w-2xl mx-auto mb-6 leading-relaxed">
            Your donations help us maintain our servers and keep AnimeHQ running smoothly for everyone to enjoy. We're grateful for every contribution.
          </p>
          <div className="inline-flex items-center gap-2 bg-white/10 px-5 py-3 rounded-lg border border-white/10">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
              <path d="M14 9a2 2 0 0 1-2 2H6l-4 4V4c0-1.1.9-2 2-2h8a2 2 0 0 1 2 2v5Z"></path>
              <path d="M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1"></path>
            </svg>
            <span className="text-white/90 text-sm">Contact us after donating to get your name added to our supporters list!</span>
          </div>
        </div>
      </div>

      {/* Supporters Section */}
      <div className="text-center mb-16">
        <h2 className="text-2xl font-bold mb-8 flex items-center gap-2 justify-center">
          <div className="w-10 h-10 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/20">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </div>
          <span>Our Supporters</span>
        </h2>

        <div className="bg-white/5 rounded-xl p-8 backdrop-blur-sm border border-white/10 max-w-4xl mx-auto">
          <p className="text-white/80 mb-8">
            We'd like to thank the following individuals for their generous support:
          </p>

          <div className="flex flex-wrap justify-center gap-3">
            <div className="bg-white/10 px-4 py-2 rounded-full text-white/90 text-sm border border-white/10">
              Anime_Fan_01
            </div>
            <div className="bg-white/10 px-4 py-2 rounded-full text-white/90 text-sm border border-white/10">
              MangaLover
            </div>
            <div className="bg-white/10 px-4 py-2 rounded-full text-white/90 text-sm border border-white/10">
              OtakuSupreme
            </div>
            <div className="bg-white/10 px-4 py-2 rounded-full text-white/90 text-sm border border-white/10">
              SakuraChan
            </div>
            <div className="bg-white/10 px-4 py-2 rounded-full text-white/90 text-sm border border-white/10">
              NarutoFan
            </div>
            <div className="bg-gradient-to-r from-white/10 to-white/20 px-4 py-2 rounded-full text-white font-medium text-sm border border-white/20">
              Your Name Here
            </div>
          </div>
        </div>
      </div>

      {/* Scroll to top button */}
      <button
        onClick={scrollToTop}
        className={`fixed bottom-6 right-6 z-50 p-3 rounded-full bg-black/60 backdrop-blur-sm border border-white/10 hover:bg-black/80 hover:border-white/20 transition-all duration-300 shadow-lg ${
          showScrollTop ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10 pointer-events-none'
        }`}
        aria-label="Scroll to top"
      >
        <ArrowUp size={20} className="text-white" />
      </button>
    </div>
  );
};

export default Donate;
