import { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Loader2, AlertCircle } from "lucide-react";
import { useAniList } from "@/hooks/useAniList";
import { toast } from "sonner";

const AuthCallback = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [error, setError] = useState(null);
  const [message, setMessage] = useState("Processing authentication...");

  useEffect(() => {
    // Function to handle the token from hash (implicit flow)
    const handleImplicitToken = () => {
      try {
        // Check if we have a token in the URL hash
        if (location.hash) {
          const hashParams = new URLSearchParams(location.hash.substring(1));
          const accessToken = hashParams.get("access_token");
          const tokenType = hashParams.get("token_type");
          const expiresIn = hashParams.get("expires_in");

          if (accessToken) {
            console.log("Received token from implicit flow:", { tokenType, expiresIn });

            // Store the token in localStorage
            localStorage.setItem("anilist_token", accessToken);

            setMessage("Successfully logged in! Redirecting...");
            toast.success("Successfully logged in to AniList!");

            // Redirect to home after a short delay
            setTimeout(() => {
              // Clean up URL hash
              window.history.replaceState({}, document.title, window.location.pathname);
              navigate("/");
            }, 1500);

            return true;
          }
        }
        return false;
      } catch (err) {
        console.error("Error processing implicit token:", err);
        setError("Failed to process authentication response");
        return false;
      }
    };

    // Check for error in URL
    const params = new URLSearchParams(location.search);
    const errorParam = params.get("error");
    const errorDescription = params.get("error_description");

    if (errorParam) {
      console.error("AniList auth error:", errorParam, errorDescription);
      setError(`${errorParam}: ${errorDescription || "Unknown error"}`);

      // Redirect to home after showing error
      const timer = setTimeout(() => {
        navigate("/");
      }, 3000);

      return () => clearTimeout(timer);
    }

    // Try to handle implicit token
    const tokenHandled = handleImplicitToken();

    // If no token was handled and no error, redirect to home
    if (!tokenHandled && !errorParam) {
      setMessage("No authentication data found. Redirecting...");
      const timer = setTimeout(() => {
        navigate("/");
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [navigate, location]);

  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] gap-4">
      {error ? (
        <>
          <AlertCircle className="w-12 h-12 text-red-500" />
          <h1 className="text-xl font-medium text-red-500">Authentication Error</h1>
          <p className="text-gray-400 text-center max-w-md">{error}</p>
          <p className="text-gray-400">You will be redirected shortly</p>
        </>
      ) : (
        <>
          <Loader2 className="w-12 h-12 animate-spin text-primary" />
          <h1 className="text-xl font-medium">AniList Authentication</h1>
          <p className="text-gray-400">{message}</p>
        </>
      )}
    </div>
  );
};

export default AuthCallback;
