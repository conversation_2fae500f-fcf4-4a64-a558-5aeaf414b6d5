import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { execSync } from 'child_process';

// ES modules don't have __dirname, so we need to create it
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create downloads directory if it doesn't exist
const downloadsDir = path.join(__dirname, 'downloads');
if (!fs.existsSync(downloadsDir)) {
  console.log('Creating downloads directory...');
  fs.mkdirSync(downloadsDir);
}

// Install nyaa.si-client from GitHub
console.log('Installing nyaa.si-client from GitHub...');
try {
  execSync('npm install github:Ashu11-A/Nyaa.si', { stdio: 'inherit' });
  console.log('Successfully installed nyaa.si-client');
} catch (error) {
  console.error('Failed to install nyaa.si-client:', error);
  process.exit(1);
}

console.log('Setup complete!');
