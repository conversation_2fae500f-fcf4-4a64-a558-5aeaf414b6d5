import { useState, useEffect } from "react";
import { useAniList } from "@/hooks/useAniList";
import { useNavigate } from "react-router-dom";
import { Loader2, LogOut, Heart, Camera, Image as ImageIcon } from "lucide-react";
import Image from "@/components/ui/Image";
import ProfileStats from "@/components/profile/ProfileStats";
import GenreChart from "@/components/profile/GenreChart";
import StreakTracker from "@/components/profile/StreakTracker";
import RankSystem from "@/components/profile/RankSystem";
import WatchHistoryChart from "@/components/profile/WatchHistoryChart";
import AnimeStatusChart from "@/components/profile/AnimeStatusChart";
import AchievementsDisplay from "@/components/profile/AchievementsDisplay";
import TaskSystemList from "@/components/profile/TaskSystemList";
import AvatarSelectionModal from "@/components/profile/AvatarSelectionModal";
import BannerSelectionModal from "@/components/profile/BannerSelectionModal";
import { useUserActivity } from "@/context/UserActivityContext";
import profileApi from "@/api/profileApi";
import { toast } from "sonner";

const ProfileNew = () => {
  const { user, loading, logout, isAuthenticated } = useAniList();
  const { level, rank, likes, addLike, userProfile } = useUserActivity();
  const navigate = useNavigate();
  const [likeClicked, setLikeClicked] = useState(false);

  // State for custom profile images
  const [customProfilePicture, setCustomProfilePicture] = useState(null);
  const [customProfileBanner, setCustomProfileBanner] = useState(null);

  // State for modals
  const [isAvatarModalOpen, setIsAvatarModalOpen] = useState(false);
  const [isBannerModalOpen, setIsBannerModalOpen] = useState(false);

  // Load custom profile images from user profile
  useEffect(() => {
    if (userProfile) {
      if (userProfile.profilePicture) {
        setCustomProfilePicture(userProfile.profilePicture);
      }
      if (userProfile.profileBanner) {
        setCustomProfileBanner(userProfile.profileBanner);
      }
    }
  }, [userProfile]);

  // Handle like button click
  const handleLike = () => {
    if (!likeClicked) {
      addLike(); // Add like and update the count
      setLikeClicked(true);
      toast.success("Thanks for the like!", {
        description: "You've given this profile a like and earned 5 XP!"
      });
    }
  };

  // Handle avatar selection
  const handleAvatarSelect = (avatarUrl) => {
    setCustomProfilePicture(avatarUrl);
  };

  // Handle banner selection
  const handleBannerSelect = (bannerUrl) => {
    setCustomProfileBanner(bannerUrl);
  };

  // Redirect if not authenticated
  useEffect(() => {
    if (!loading && !isAuthenticated) {
      navigate("/");
    }
  }, [loading, isAuthenticated, navigate]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[70vh]">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[70vh] gap-4">
        <div className="p-4 bg-red-900/20 border border-red-900/30 rounded-lg max-w-md text-center">
          <h1 className="text-xl font-medium text-red-400 mb-2">Profile Not Available</h1>
          <p className="text-gray-400 mb-4">Unable to load your AniList profile. Please try logging in again.</p>
          <button
            onClick={() => navigate("/")}
            className="py-2 px-4 rounded-lg bg-white/10 hover:bg-white/20 text-white/90 hover:text-white transition-all border border-white/20"
          >
            Return to Home
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Profile Info (Fixed) */}
        <div className="lg:col-span-1">
          <div className="bg-black/40 backdrop-blur-sm rounded-lg overflow-hidden border border-white/10 lg:sticky lg:top-20">
            {/* Banner Image with Edit Button */}
            <div className="relative h-32 w-full group">
              {customProfileBanner ? (
                <Image
                  src={customProfileBanner}
                  className="w-full h-full object-cover"
                />
              ) : user.bannerImage ? (
                <Image
                  src={user.bannerImage}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-r from-purple-900/50 to-blue-900/50"></div>
              )}

              {/* Banner Edit Button */}
              <button
                className="absolute bottom-2 right-2 bg-black/60 p-1.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => setIsBannerModalOpen(true)}
                title="Change banner"
              >
                <ImageIcon size={16} />
              </button>
            </div>

            {/* Profile Info */}
            <div className="relative px-4 pb-4">
              {/* Avatar - Centered with Edit Button */}
              <div className="flex justify-center">
                <div className="absolute -top-12 border-4 border-black/40 rounded-full overflow-hidden w-24 h-24 group">
                  <Image
                    src={customProfilePicture || (user.avatar?.large || user.avatar?.medium)}
                    className="w-full h-full object-cover rounded-full"
                  />

                  {/* Avatar Edit Button */}
                  <button
                    className="absolute inset-0 flex items-center justify-center bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => setIsAvatarModalOpen(true)}
                    title="Change avatar"
                  >
                    <Camera size={20} />
                  </button>
                </div>
              </div>

              {/* User Info - Centered */}
              <div className="pt-14 pb-2 flex flex-col items-center text-center">
                <h1 className="text-xl font-bold">{user.name}</h1>
                <div className="flex items-center gap-2 mt-1 justify-center">
                  <span className="text-xs text-white/60">@{user.name}</span>
                  {user.id && (
                    <span className="text-xs bg-white/10 px-2 py-0.5 rounded-full">
                      ID: {user.id}
                    </span>
                  )}
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-4 mt-4">
                <div className="flex flex-col items-center bg-black/30 rounded-lg p-2">
                  <span className="text-lg font-bold">{level}</span>
                  <span className="text-xs text-white/60">LVL</span>
                </div>

                <div className="flex flex-col items-center bg-black/30 rounded-lg p-2">
                  <span className="text-lg font-bold">{rank}</span>
                  <span className="text-xs text-white/60">Rank</span>
                </div>

                <div className="flex flex-col items-center bg-black/30 rounded-lg p-2 relative group">
                  <button
                    onClick={handleLike}
                    className="absolute inset-0 z-10 opacity-0"
                    disabled={likeClicked}
                    aria-label="Like profile"
                  />
                  <div className="flex items-center gap-1">
                    <Heart size={16} className={`${likeClicked ? 'fill-red-500 text-red-500' : 'text-white/60'} transition-colors group-hover:text-red-500`} />
                    <span className="text-lg font-bold">{likes}</span>
                  </div>
                  <span className="text-xs text-white/60">Likes</span>
                  {!likeClicked && (
                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none">
                      Click to like this profile
                    </div>
                  )}
                </div>
              </div>

              {/* Rank System */}
              <div className="mt-6">
                <RankSystem />
              </div>

              {/* Streak Tracker */}
              <div className="mt-6">
                <StreakTracker />
              </div>

              {/* Logout Button */}
              <button
                className="w-full mt-6 py-2 px-4 rounded-lg bg-gradient-to-r from-red-900/50 to-red-700/50 hover:from-red-800/60 hover:to-red-600/60 text-white/90 hover:text-white transition-all border border-red-900/30 flex items-center justify-center gap-2"
                onClick={logout}
              >
                <LogOut size={16} />
                Sign Out
              </button>
            </div>
          </div>
        </div>

        {/* Right Column - Stats and Charts (Scrollable) */}
        <div className="lg:col-span-2">
          {/* Scrollable container with max height */}
          <div className="space-y-6 lg:max-h-[calc(100vh-2rem)] lg:overflow-y-auto lg:pr-2 lg:pb-6 scrollbar-thin scrollbar-thumb-white/10 scrollbar-track-transparent">
            {/* Stats Cards */}
            <ProfileStats />

            {/* Charts */}
            <div className="bg-black/40 backdrop-blur-sm rounded-lg p-6 border border-white/10">
              <GenreChart />
            </div>

            {/* Watch History */}
            <div className="bg-black/40 backdrop-blur-sm rounded-lg p-6 border border-white/10">
              <WatchHistoryChart />
            </div>

            {/* Anime Status */}
            <div className="bg-black/40 backdrop-blur-sm rounded-lg p-6 border border-white/10">
              <AnimeStatusChart />
            </div>

            {/* Achievements */}
            <div className="bg-black/40 backdrop-blur-sm rounded-lg p-6 border border-white/10">
              <AchievementsDisplay />
            </div>

            {/* Daily Tasks */}
            <div className="bg-black/40 backdrop-blur-sm rounded-lg p-6 border border-white/10">
              <h3 className="text-lg font-medium mb-4">Daily Tasks</h3>
              <TaskSystemList />
            </div>
          </div>
        </div>
      </div>

      {/* Avatar Selection Modal */}
      <AvatarSelectionModal
        isOpen={isAvatarModalOpen}
        onClose={() => setIsAvatarModalOpen(false)}
        onSelect={handleAvatarSelect}
        currentAvatar={customProfilePicture}
      />

      {/* Banner Selection Modal */}
      <BannerSelectionModal
        isOpen={isBannerModalOpen}
        onClose={() => setIsBannerModalOpen(false)}
        onSelect={handleBannerSelect}
        currentBanner={customProfileBanner}
      />
    </div>
  );
};

export default ProfileNew;
