import { useState, useEffect } from "react";
import { useAniList } from "@/hooks/useAniList";

const GenreChart = () => {
  const { user, getUserAnimeList } = useAniList();
  const [genreCounts, setGenreCounts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchGenreData = async () => {
      if (!user?.id) return;

      try {
        setLoading(true);

        // Check if we have genre data in the user statistics
        if (user.statistics?.anime?.genres && user.statistics.anime.genres.length > 0) {
          console.log("Using genre data from user statistics");

          // Convert the genre data to our format
          const sortedGenres = user.statistics.anime.genres
            .map(g => ({ name: g.genre, count: g.count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10); // Get top 10 genres

          console.log("Processed genre data from statistics:", sortedGenres);
          setGenreCounts(sortedGenres);
          setLoading(false);
          return;
        }

        // Fallback to fetching from anime list if statistics don't have genre data
        console.log("No genre data in statistics, fetching from anime list");

        // Fetch all status types to get a complete picture
        const statuses = ["CURRENT", "COMPLETED", "PLANNING", "PAUSED", "DROPPED"];
        let allEntries = [];

        // Fetch each status type and combine the results
        for (const status of statuses) {
          const lists = await getUserAnimeList(status);
          if (lists && lists.length) {
            const entries = lists.flatMap(list => list.entries || []);
            allEntries = [...allEntries, ...entries];
          }
        }

        if (allEntries.length === 0) {
          console.log("No anime entries found in any lists");
          setLoading(false);
          return;
        }

        console.log(`Found ${allEntries.length} total anime entries across all lists`);

        // Count genres
        const genres = {};

        // Process entries to count genres
        for (const entry of allEntries) {
          if (entry.media && entry.media.genres) {
            entry.media.genres.forEach(genre => {
              if (!genres[genre]) {
                genres[genre] = 0;
              }
              genres[genre]++;
            });
          }
        }

        // Convert to array and sort by count
        const sortedGenres = Object.entries(genres)
          .map(([name, count]) => ({ name, count }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 10); // Get top 10 genres

        console.log("Processed genre data from anime list:", sortedGenres);
        setGenreCounts(sortedGenres);
      } catch (error) {
        console.error("Error fetching genre data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchGenreData();
  }, [user, getUserAnimeList]);

  // Find the maximum count to calculate percentages
  const maxCount = Math.max(...genreCounts.map(genre => genre.count), 1);

  return (
    <div className="w-full">
      <div className="flex items-center gap-2 mb-2">
        <h3 className="text-sm text-white/80">Your top 10 Genres</h3>
      </div>

      {loading ? (
        <div className="flex justify-center py-4">
          <div className="animate-spin w-6 h-6 border-2 border-white/20 border-t-white rounded-full"></div>
        </div>
      ) : (
        <div className="space-y-3">
          {genreCounts.map((genre, index) => (
            <div key={genre.name} className="space-y-1">
              <div className="flex justify-between items-center text-xs">
                <span className="text-white/80">{genre.name}</span>
                <span className="text-white/60">{genre.count}</span>
              </div>
              <div className="w-full h-2 bg-black/30 rounded-full overflow-hidden">
                <div
                  className={`h-full rounded-full ${getGenreColor(index)}`}
                  style={{ width: `${(genre.count / maxCount) * 100}%` }}
                ></div>
              </div>
            </div>
          ))}

          {genreCounts.length === 0 && (
            <div className="space-y-3">
              {/* Sample genre data when no real data is available */}
              {[
                { name: "Action", count: 9 },
                { name: "Fantasy", count: 7 },
                { name: "Drama", count: 5 },
                { name: "Adventure", count: 5 },
                { name: "Comedy", count: 4 },
                { name: "Romance", count: 4 },
                { name: "Supernatural", count: 3 },
                { name: "Ecchi", count: 1 },
                { name: "Slice of Life", count: 1 },
                { name: "Action", count: 0 }
              ].map((genre, index) => (
                <div key={genre.name + index} className="space-y-1">
                  <div className="flex justify-between items-center text-xs">
                    <span className="text-white/80">{genre.name}</span>
                    <span className="text-white/60">{genre.count}</span>
                  </div>
                  <div className="w-full h-2 bg-black/30 rounded-full overflow-hidden">
                    <div
                      className={`h-full rounded-full ${getGenreColor(index)}`}
                      style={{ width: `${(genre.count / 9) * 100}%` }}
                    ></div>
                  </div>
                </div>
              ))}
              <div className="text-center py-2 text-white/60 text-xs italic">
                Sample data shown. Add anime to your list to see your actual genre stats.
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Helper function to get different colors for genre bars
const getGenreColor = (index) => {
  const colors = [
    "bg-blue-500",
    "bg-purple-500",
    "bg-pink-500",
    "bg-yellow-500",
    "bg-green-500",
    "bg-red-500",
    "bg-indigo-500",
    "bg-orange-500",
    "bg-teal-500",
    "bg-cyan-500",
  ];

  return colors[index % colors.length];
};

export default GenreChart;
