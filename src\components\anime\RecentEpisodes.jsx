import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import Image from "@/components/ui/Image";
import { Link } from "react-router-dom";
import { Play, Clock, Eye, CalendarClock } from "lucide-react";
import { getRecentEpisodes } from "@/api/aninow";
import useFetch from "@/hooks/useFetch";
import Autoplay from "embla-carousel-autoplay";
import AgeRating from "@/components/ui/AgeRating";
import { memo } from "react";

// Memoized EpisodeCard component for better performance
const EpisodeCard = memo(({ episode: x }) => {
  return (
    <Link
      to={x?.id ? `/watch/anime/${x?.id}?ep=${x?.currentEpisode}` : ""}
      className="group block relative overflow-hidden rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300 shadow-lg"
    >
      {/* Card Container */}
      <div className="relative aspect-video w-full">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src={x?.coverImage}
            quality="high"
            className="object-cover w-full h-full group-hover:scale-110 transition-all duration-500"
          />
          {/* Simplified overlay - removed backdrop-blur */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-black/20 opacity-70 group-hover:opacity-80 transition-opacity duration-300"></div>
        </div>

        {/* Content */}
        <div className="absolute inset-0 flex flex-col justify-between p-2 sm:p-4">
          {/* Top Section */}
          <div className="flex justify-between items-start">
            {/* Episode Number */}
            <div className="bg-black/60 text-white px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-md text-xs sm:text-sm font-bold border border-white/10 flex items-center gap-1">
              <span>EP {x?.currentEpisode}</span>
            </div>

            {/* Date - Hidden on smallest screens */}
            <div className="hidden xs:flex bg-black/60 text-white px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-md text-[10px] sm:text-xs border border-white/10 items-center gap-1">
              <Clock size={10} />
              <span>{new Date(x?.updatedAt).toLocaleDateString()}</span>
            </div>
          </div>

          {/* Bottom Section */}
          <div>
            {/* Title */}
            <h3 className="text-sm sm:text-base font-bold text-white line-clamp-1 mb-0.5 sm:mb-1">
              {x?.title?.english || x?.title?.romaji || x?.title}
            </h3>

            {/* Status Indicator - With breathing effect for Airing */}
            {x?.status && (
              <div className="flex items-center gap-1 sm:gap-1.5 mb-0.5 sm:mb-1">
                <span className={`relative flex h-1.5 sm:h-2 w-1.5 sm:w-2 ${
                  x?.status === "RELEASING" ? "bg-green-500" :
                  x?.status === "FINISHED" ? "bg-blue-500" :
                  x?.status === "NOT_YET_RELEASED" ? "bg-yellow-500" :
                  x?.status === "CANCELLED" ? "bg-red-500" :
                  "bg-gray-500"
                } rounded-full`}>
                  {x?.status === "RELEASING" && (
                    <span className="absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75 animate-ping"></span>
                  )}
                </span>
                <span className="text-[8px] sm:text-[10px] text-white/80">
                  {x?.status === "RELEASING" ? "Airing" :
                   x?.status === "FINISHED" ? "Completed" :
                   x?.status === "NOT_YET_RELEASED" ? "Coming Soon" :
                   x?.status === "CANCELLED" ? "Cancelled" :
                   "Unknown"}
                </span>
              </div>
            )}

            {/* Genres - Show only one on mobile - Simplified */}
            {x?.genres && x?.genres.length > 0 && (
              <div className="flex flex-wrap gap-1">
                <span className="text-[8px] sm:text-xs px-1.5 sm:px-2 py-0.5 bg-black/60 text-white rounded-full border border-white/10">
                  {x?.genres[0]}
                </span>
                {x?.genres[1] && (
                  <span className="hidden sm:inline-block text-[8px] sm:text-xs px-1.5 sm:px-2 py-0.5 bg-black/60 text-white rounded-full border border-white/10">
                    {x?.genres[1]}
                  </span>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Play Button - Simplified */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="bg-white text-black font-bold px-4 py-2 rounded-full flex items-center gap-2">
            <Play fill="black" size={16} />
            <span>Watch Now</span>
          </div>
        </div>
      </div>
    </Link>
  );
});

const RecentEpisodes = memo(() => {
  const { data } = useFetch({
    key: ["recent-episodes"],
    fun: async () => {
      return (await getRecentEpisodes()) || null;
    },
    placeholderData: [],
  });

  return (
    <div className="w-full flex flex-col gap-3 sm:gap-5">
      {/* Header with icon - Simplified */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <div className="bg-black/40 p-1.5 sm:p-2 rounded-md border border-white/10">
            <CalendarClock size={14} className="text-white" />
          </div>
          <h2 className="text-lg sm:text-xl lg:text-2xl font-semibold">Recent Episodes</h2>
        </div>
        <Link
          to="/recent-episodes"
          className="bg-black/40 hover:bg-black/60 text-white px-2 py-1 sm:px-3 sm:py-1.5 rounded-md text-xs sm:text-sm transition-all duration-200 flex items-center gap-1 sm:gap-2 border border-white/10 hover:border-white/20"
        >
          <span>View All</span>
          <Eye size={12} className="sm:size-4" />
        </Link>
      </div>

      {/* Carousel */}
      <div className="relative">
        <Carousel
          plugins={[
            Autoplay({
              delay: 5000,
            }),
          ]}
          opts={{
            align: "start",
            loop: true,
          }}
          className="w-full"
        >
          <CarouselContent className="-ml-4">
            {data?.length
              ? data?.map((episode) => (
                <CarouselItem
                  key={episode?.id}
                  className="pl-4 basis-2/3 sm:basis-1/2 md:basis-1/3 lg:basis-1/4"
                >
                  <EpisodeCard episode={episode} />
                </CarouselItem>
              ))
              : Array.from({ length: 8 }).map((_, i) => (
                  <CarouselItem
                    key={i}
                    className="pl-4 basis-2/3 sm:basis-1/2 md:basis-1/3 lg:basis-1/4"
                  >
                    <div className="aspect-video bg-white/5 animate-pulse rounded-xl border border-white/10"></div>
                  </CarouselItem>
                ))}
          </CarouselContent>
        </Carousel>
      </div>
    </div>
  );
});

export default RecentEpisodes;
