import { Star, ExternalLink, Heart } from 'lucide-react';
import Image from '@/components/ui/Image';
import AgeRating from '@/components/ui/AgeRating';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';

const AnimeCard = ({ anime, onLike }) => {
  if (!anime) return null;

  return (
    <div className="bg-white/10 backdrop-blur-md rounded-xl overflow-hidden shadow-xl flex flex-col h-full border border-white/20 relative group hover:scale-[1.02] transition-all duration-300">
      {/* Image */}
      <div className="relative aspect-[3/4] overflow-hidden">
        <Image
          src={anime?.images?.jpg?.large_image_url || anime?.images?.webp?.large_image_url}
          className="w-full h-full object-cover"
          quality="high"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/30 to-transparent"></div>

        {/* Glassy overlay on hover */}
        <div className="absolute inset-0 bg-gradient-to-t from-primary/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        {/* Rating badge */}
        {anime?.score && (
          <div className="absolute top-2 right-2 flex items-center gap-1 bg-black/30 backdrop-blur-md px-2 py-1 rounded-full border border-white/20">
            <Star fill="gold" color="gold" size={16} />
            <span className="text-white font-medium">{anime.score}</span>
          </div>
        )}

        {/* Age rating */}
        <div className="absolute top-2 left-2">
          <AgeRating isAdult={anime?.isAdult || anime?.rating === 'Rx'} genres={anime?.genres} />
        </div>
      </div>

      {/* Content */}
      <div className="p-5 flex flex-col flex-grow bg-gradient-to-b from-black/50 to-black/20 backdrop-blur-sm">
        <h2 className="text-xl font-bold mb-2 line-clamp-2 text-white">{anime?.title}</h2>

        <div className="flex items-center gap-2 mb-3 text-white/80 text-sm">
          <span className="bg-white/10 px-2 py-0.5 rounded-full">{anime?.type || 'TV'}</span>
          {anime?.episodes && <span className="bg-white/10 px-2 py-0.5 rounded-full">{anime.episodes} eps</span>}
          {anime?.year && <span className="bg-white/10 px-2 py-0.5 rounded-full">{anime.year}</span>}
        </div>

        <p className="text-sm line-clamp-3 text-white/70 mb-4 flex-grow">{anime?.synopsis}</p>

        {/* Action buttons */}
        <div className="flex justify-between mt-auto pt-2">
          <Button
            variant="outline"
            size="sm"
            className="flex-1 mr-2 bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
            asChild
          >
            <Link to={`/anime/${anime?.mal_id}`}>
              View Details
            </Link>
          </Button>

          <Button
            variant="default"
            size="sm"
            className="flex-1 bg-primary/80 hover:bg-primary backdrop-blur-sm text-black font-medium"
            onClick={() => onLike && onLike(anime)}
          >
            <Heart size={16} className="mr-1" /> Like
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AnimeCard;
