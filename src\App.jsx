import { BrowserRouter, Route, Routes } from "react-router-dom";
import { useState, useEffect } from "react";
import { initGsapSmoothScrollAnchors } from "./utils/gsapSmoothScroll";
import Home from "./pages/Home";
import Watch from "./pages/Watch";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import "react-lazy-load-image-component/src/effects/blur.css";
import "react-lazy-load-image-component/src/effects/opacity.css";
import { AppContext } from "./context/MainContext";
import { AniListProvider } from "./context/AniListContext";
import { TitlePreferenceProvider } from "./context/TitlePreferenceContext";
import { NotificationProvider } from "./context/NotificationContext";
import { WatchHistoryProvider } from "./contexts/WatchHistoryContext";
import { UserActivityProvider } from "./context/UserActivityContext";
import { TaskSystemProvider } from "./context/TaskSystemContext";

import Footer from "./components/Footer";
import { Toaster } from "./components/ui/sonner";
import History from "./pages/History";
import Watchlist from "./pages/Watchlist";

import AnimeInfo from "./pages/AnimeInfo";
import Schedule from "./pages/Schedule";
import Changelog from "./pages/Changelog";
import ProfileRedesign from "./pages/ProfileRedesign";
import ProfileNew from "./pages/ProfileNew";
import Leaderboard from "./pages/Leaderboard";
import AuthCallback from "./pages/AuthCallback";
import Terms from "./pages/Terms";
import Privacy from "./pages/Privacy";
import DMCA from "./pages/DMCA";
import About from "./pages/About";
import Donate from "./pages/Donate";
import FAQ from "./pages/FAQ";
import NotFound from "./pages/NotFound";
import AnimeShorts from "./pages/AnimeShorts";
import AIRecommendations from "./pages/AIRecommendationsNew";
import AnimeLive from "./pages/AnimeLive";
import MostPopular from "./pages/MostPopular";
import RecentEpisodes from "./pages/RecentEpisodes";
import AnimeImageSearch from "./pages/AnimeImageSearch";
import AnimeExploreRedesign from "./components/anime/AnimeExploreRedesign";

import SimpleDialog from "./components/ui/SimpleDialog";
import RateLimitInfo from "./components/RateLimitInfo";
import Sidebar from "./components/Sidebar";
import MobileHeader from "./components/MobileHeader";
import ScrollToTop from "./components/ui/ScrollToTop";
import SimpleScrollToTopButton from "./components/ui/SimpleScrollToTopButton";
import { subscribeToRateLimitChanges } from "./utils/apiUtils";
import 'react-tooltip/dist/react-tooltip.css';
import './styles/tooltip.css';
const queryClient = new QueryClient();

const App = () => {
  const [isRateLimited, setIsRateLimited] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Subscribe to rate limit changes and initialize smooth scroll
  useEffect(() => {
    // This will be called whenever the rate limit status changes
    const unsubscribe = subscribeToRateLimitChanges((limited) => {
      console.log("Rate limit status changed:", limited);
      setIsRateLimited(limited);
    });

    // Initialize GSAP smooth scrolling for anchor links
    initGsapSmoothScrollAnchors({
      duration: 0.8,  // Animation duration in seconds for GSAP
      offset: -80,    // Offset to account for fixed header
      ease: 'power2.inOut'  // GSAP easing function
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, []);

  return (
    <BrowserRouter>
      <ScrollToTop smooth={true} />
      <AppContext>
        <AniListProvider>
          <TitlePreferenceProvider>
            <NotificationProvider>
              <WatchHistoryProvider>
                <UserActivityProvider>
                  <TaskSystemProvider>
                    <QueryClientProvider client={queryClient}>
              <Sidebar isOpen={isSidebarOpen} setIsOpen={setIsSidebarOpen} />
              <MobileHeader toggleSidebar={toggleSidebar} isSidebarOpen={isSidebarOpen} />
              <div className="flex flex-col min-h-screen h-auto">
                {/* Main content with sidebar margin */}
                <div className="lg:ml-56 transition-all duration-300">
                  <div className="px-4 sm:px-6 xl:container mx-auto">
                    <main className="flex-grow pt-16 mt-2 lg:pt-8 lg:mt-0">
                      <Routes>
                        <Route element={<Home />} path="/" />
                        <Route element={<Watch />} path="/watch/:type/:id" />
                        <Route element={<AnimeInfo />} path="/anime/:id" />
                        <Route element={<History />} path="/history" />
                        <Route element={<Watchlist />} path="/watchlist" />
                        <Route element={<Schedule />} path="/schedule" />
                        <Route element={<Changelog />} path="/changelog" />
                        <Route element={<ProfileRedesign />} path="/profile" />
                        <Route element={<ProfileNew />} path="/profile-new" />
                        <Route element={<Leaderboard />} path="/leaderboard" />
                        <Route element={<AuthCallback />} path="/auth/callback" />
                        <Route element={<AnimeShorts />} path="/shorts" />
                        <Route element={<AIRecommendations />} path="/recommendations" />
                        <Route element={<AnimeLive />} path="/live" />
                        <Route element={<MostPopular />} path="/trending" />
                        <Route element={<RecentEpisodes />} path="/recent-episodes" />
                        <Route element={<AnimeImageSearch />} path="/image-search" />

                        {/* Legal and Info pages */}
                        <Route element={<Terms />} path="/terms" />
                        <Route element={<Privacy />} path="/privacy" />
                        <Route element={<DMCA />} path="/dmca" />
                        <Route element={<About />} path="/about" />
                        <Route element={<Donate />} path="/donate" />
                        <Route element={<FAQ />} path="/faq" />

                        <Route element={<AnimeExploreRedesign />} path="/search" />
                        <Route element={<AnimeExploreRedesign />} path="/explore" />

                        {/* 404 Page */}
                        <Route element={<NotFound />} path="/404" />
                        <Route element={<NotFound />} path="*" />
                      </Routes>
                    </main>
                    <div className="mt-16 mb-4">
                      <Footer />
                    </div>
                  </div>
                </div>
                <Toaster />
                <SimpleDialog />

                {/* Rate limit info component */}
                <RateLimitInfo isVisible={isRateLimited} />

                {/* Scroll to top button */}
                <SimpleScrollToTopButton showAfter={400} />
              </div>
            </QueryClientProvider>
                  </TaskSystemProvider>
                </UserActivityProvider>
              </WatchHistoryProvider>
            </NotificationProvider>
          </TitlePreferenceProvider>
        </AniListProvider>
      </AppContext>
    </BrowserRouter>
  );
};

export default App;
