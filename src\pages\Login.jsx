import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { LogIn, Sparkles } from "lucide-react";
import { useAniList } from "@/hooks/useAniList";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

const Login = () => {
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAniList();
  const [isLoading, setIsLoading] = useState(false);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      navigate("/", { replace: true });
    }
  }, [isAuthenticated, user, navigate]);

  // Handle AniList login
  const handleAniListLogin = () => {
    setIsLoading(true);

    // AniList OAuth URL with implicit flow
    const ANILIST_CLIENT_ID = "26304";
    const authUrl = `https://anilist.co/api/v2/oauth/authorize?client_id=${ANILIST_CLIENT_ID}&response_type=token`;

    // Show loading toast
    toast.info("Redirecting to AniList...");

    // Small delay for better UX
    setTimeout(() => {
      window.location.href = authUrl;
    }, 500);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black relative overflow-hidden flex items-center justify-center p-4">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        {/* Floating orbs with subtle animation */}
        <div className="absolute top-20 left-20 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: "2s" }} />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-cyan-500/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: "4s" }} />

        {/* Smaller accent orbs */}
        <div className="absolute top-32 right-32 w-32 h-32 bg-white/5 rounded-full blur-xl animate-pulse" style={{ animationDelay: "1s" }} />
        <div className="absolute bottom-32 left-32 w-24 h-24 bg-white/5 rounded-full blur-xl animate-pulse" style={{ animationDelay: "3s" }} />
      </div>

      {/* Grid pattern overlay */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]" />

      {/* Main login card */}
      <div className="relative z-10 w-full max-w-md">
        <div className="relative animate-fade-in">
          {/* Glass card background */}
          <div className="absolute inset-0 bg-white/5 backdrop-blur-xl rounded-3xl border border-white/10" />

          {/* Glow effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-cyan-500/20 rounded-3xl blur-xl opacity-50" />

          {/* Card content */}
          <div className="relative p-8 space-y-8">
            {/* Header */}
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <div className="relative">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center">
                    <Sparkles className="w-8 h-8 text-white" />
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl blur-lg opacity-50" />
                </div>
              </div>

              <div>
                <h1 className="text-3xl font-bold text-white mb-2">Welcome to AnimeHQ</h1>
                <p className="text-gray-300 text-sm">
                  Sign in with AniList to access your anime collection
                </p>
              </div>
            </div>

            {/* Login button */}
            <div>
              <Button
                onClick={handleAniListLogin}
                disabled={isLoading}
                className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <LogIn className="w-5 h-5 mr-2" />
                {isLoading ? "Connecting..." : "Continue with AniList"}
              </Button>
            </div>

            {/* Footer */}
            <div className="text-center">
              <p className="text-xs text-gray-400">
                Secure authentication powered by AniList
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Custom CSS for animations */}
      <style jsx>{`
        @keyframes fade-in {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fade-in {
          animation: fade-in 0.6s ease-out;
        }
      `}</style>
    </div>
  );
};

export default Login;
