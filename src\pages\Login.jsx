import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { LogIn, <PERSON>rkles, RefreshCw } from "lucide-react";
import { useAniList } from "@/hooks/useAniList";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

const Login = () => {
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAniList();
  const [isLoading, setIsLoading] = useState(false);
  const [backgroundAnime, setBackgroundAnime] = useState(null);
  const [loadingBg, setLoadingBg] = useState(true);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      navigate("/", { replace: true });
    }
  }, [isAuthenticated, user, navigate]);

  // Fetch random anime for background
  useEffect(() => {
    const fetchRandomAnime = async () => {
      try {
        setLoadingBg(true);
        // Get a random page from top anime (first 5 pages have highest quality anime)
        const randomPage = Math.floor(Math.random() * 5) + 1;
        const response = await fetch(`https://api.jikan.moe/v4/top/anime?page=${randomPage}&limit=25`);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.data && data.data.length > 0) {
          // Filter anime with high-quality images and good scores
          const qualityAnime = data.data.filter(anime =>
            (anime.images?.webp?.large_image_url || anime.images?.jpg?.large_image_url) &&
            anime.score && anime.score >= 7.0 // Only high-rated anime
          );

          if (qualityAnime.length > 0) {
            const randomIndex = Math.floor(Math.random() * qualityAnime.length);
            setBackgroundAnime(qualityAnime[randomIndex]);
          } else {
            // Fallback to any anime with images
            const randomIndex = Math.floor(Math.random() * data.data.length);
            const anime = data.data[randomIndex];
            if (anime.images?.webp?.large_image_url || anime.images?.jpg?.large_image_url) {
              setBackgroundAnime(anime);
            }
          }
        }
      } catch (error) {
        console.error("Error fetching background anime:", error);
        // Could add a retry mechanism here
      } finally {
        setLoadingBg(false);
      }
    };

    fetchRandomAnime();
  }, []);

  // Handle AniList login
  const handleAniListLogin = () => {
    setIsLoading(true);

    // AniList OAuth URL with implicit flow
    const ANILIST_CLIENT_ID = "26304";
    const authUrl = `https://anilist.co/api/v2/oauth/authorize?client_id=${ANILIST_CLIENT_ID}&response_type=token`;

    // Show loading toast
    toast.info("Redirecting to AniList...");

    // Small delay for better UX
    setTimeout(() => {
      window.location.href = authUrl;
    }, 500);
  };

  return (
    <div className="min-h-screen relative overflow-hidden flex items-center justify-center p-4">
      {/* Background Image */}
      {backgroundAnime && !loadingBg && (
        <div className="absolute inset-0">
          <img
            src={backgroundAnime.images?.webp?.large_image_url || backgroundAnime.images?.jpg?.large_image_url}
            alt={backgroundAnime.title}
            className="w-full h-full object-cover"
            loading="eager"
          />
          {/* Dark overlay for better readability */}
          <div className="absolute inset-0 bg-black/70" />
        </div>
      )}

      {/* Fallback gradient background */}
      {(!backgroundAnime || loadingBg) && (
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black" />
      )}

      {/* Background decorative elements */}
      <div className="absolute inset-0">
        {/* Floating orbs with subtle animation */}
        <div className="absolute top-20 left-20 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: "2s" }} />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-cyan-500/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: "4s" }} />

        {/* Smaller accent orbs */}
        <div className="absolute top-32 right-32 w-32 h-32 bg-white/5 rounded-full blur-xl animate-pulse" style={{ animationDelay: "1s" }} />
        <div className="absolute bottom-32 left-32 w-24 h-24 bg-white/5 rounded-full blur-xl animate-pulse" style={{ animationDelay: "3s" }} />
      </div>

      {/* Grid pattern overlay */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]" />



      {/* Main login card */}
      <div className="relative z-10 w-full max-w-md mx-auto">
        <div className="relative animate-fade-in">
          {/* Enhanced glass card background */}
          <div className="absolute inset-0 bg-black/20 backdrop-blur-2xl rounded-3xl border border-white/20" />
          <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-white/5 to-transparent rounded-3xl" />

          {/* Enhanced glow effect */}
          <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/30 via-purple-500/30 to-cyan-500/30 rounded-3xl blur-xl opacity-60" />

          {/* Card content */}
          <div className="relative p-6 sm:p-8 space-y-6 sm:space-y-8">
            {/* Header */}
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <div className="relative">
                  <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-3xl flex items-center justify-center shadow-2xl">
                    <Sparkles className="w-10 h-10 text-white" />
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-3xl blur-xl opacity-60" />
                  {/* Additional glow rings */}
                  <div className="absolute -inset-2 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-2xl" />
                </div>
              </div>

              <div>
                <h1 className="text-3xl sm:text-4xl font-bold text-white mb-3 text-shadow">Welcome to AnimeHQ</h1>
                <p className="text-gray-200 text-sm sm:text-base leading-relaxed">
                  Sign in with AniList to access your anime collection and unlock premium features
                </p>
              </div>
            </div>

            {/* Anime info card with "Background From" */}
            {backgroundAnime && !loadingBg && (
              <div className="bg-black/30 backdrop-blur-sm rounded-2xl p-4 border border-white/10">
                <div className="text-xs text-gray-400 mb-2 font-medium">Background From</div>
                <div className="flex items-center gap-3">
                  <img
                    src={backgroundAnime.images?.webp?.image_url || backgroundAnime.images?.jpg?.image_url}
                    alt={backgroundAnime.title}
                    className="w-12 h-16 object-cover rounded-lg border border-white/20"
                  />
                  <div className="flex-1 min-w-0">
                    <h3 className="text-white font-medium text-sm truncate">
                      {backgroundAnime.title}
                    </h3>
                    <p className="text-gray-300 text-xs mt-1">
                      Featured Anime • Score: {backgroundAnime.score || 'N/A'}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Login button */}
            <div>
              <Button
                onClick={handleAniListLogin}
                disabled={isLoading}
                className="w-full h-14 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 hover:from-blue-700 hover:via-purple-700 hover:to-blue-700 text-white font-semibold rounded-2xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] shadow-2xl hover:shadow-blue-500/25 disabled:opacity-50 disabled:cursor-not-allowed border border-white/20"
              >
                <LogIn className="w-6 h-6 mr-3" />
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <RefreshCw className="w-4 h-4 animate-spin" />
                    Connecting...
                  </div>
                ) : (
                  "Continue with AniList"
                )}
              </Button>
            </div>

            {/* Footer */}
            <div className="text-center space-y-2">
              <p className="text-xs text-gray-300">
                Secure authentication powered by AniList
              </p>
              {loadingBg && (
                <p className="text-xs text-gray-400 flex items-center justify-center gap-1">
                  <RefreshCw className="w-3 h-3 animate-spin" />
                  Loading background...
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Custom CSS for animations */}
      <style jsx>{`
        @keyframes fade-in {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fade-in {
          animation: fade-in 0.6s ease-out;
        }
      `}</style>
    </div>
  );
};

export default Login;
