import { useEffect, useState, useRef, useCallback, memo, useMemo } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { getAnimeDetails } from "@/api/anilist";
import { getAnimeEpisodes } from "@/api/aninow";
import useFetch from "@/hooks/useFetch";
import Image from "@/components/ui/Image";
import {
  Star, Calendar, Clock, PlayIcon, ArrowLeft, AlertCircle, Loader2, Layers,
  Building, Grid, List, LayoutList, Users, ChevronLeft, ChevronRight
} from "lucide-react";
import Strip from "@/components/home/<USER>";
import AnimeRelations from "@/components/anime/AnimeRelations";
import AgeRating from "@/components/ui/AgeRating";
import AniListSync from "@/components/AniListSync";
import { useTitlePreference, TITLE_PREFERENCES } from "@/context/TitlePreferenceContext";
import useEmblaCarousel from "embla-carousel-react";
import axios from "axios";

// Memoized Episode Card for Grid View
const GridEpisodeCard = memo(({ episode, animeId, animeCover, onHover }) => {
  return (
    <div className="flex-[0_0_260px]">
      <Link
        to={`/watch/anime/${animeId}?ep=${episode.number}`}
        className="group flex flex-col bg-black/40 rounded-lg overflow-hidden shadow-lg border border-white/5 hover:border-white/20 transition-all duration-300 h-full"
        onMouseEnter={() => onHover && onHover(episode.image || animeCover)}
      >
        {/* Thumbnail */}
        <div className="relative w-full aspect-video">
          <Image
            src={episode.image || animeCover}
            className="object-cover w-full h-full opacity-90 group-hover:opacity-100 group-hover:scale-105 transition-all duration-300"
          />
          {/* Play button overlay on hover */}
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
            <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center backdrop-blur-sm border border-white/20">
              <PlayIcon className="text-white ml-0.5" size={20} />
            </div>
          </div>
          {/* Episode number badge */}
          <div className="absolute top-2 right-2">
            <div className="bg-black/60 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-md font-medium border border-white/10">
              EP {episode.number}
            </div>
          </div>
          {/* Gradient overlay */}
          <div className="absolute bottom-0 inset-x-0 h-16 bg-gradient-to-t from-black to-transparent"></div>
        </div>

        {/* Episode info */}
        <div className="p-3">
          <h3 className="text-sm font-medium line-clamp-1 group-hover:text-white transition-colors">
            {episode.title || `Episode ${episode.number}`}
          </h3>
          <div className="flex items-center gap-2 mt-1">
            <div className="text-xs text-white/50">{episode.duration || "24m"}</div>
          </div>
        </div>
      </Link>
    </div>
  );
});

// Memoized Episode Card for List View
const ListEpisodeCard = memo(({ episode, animeId, animeCover, onHover }) => {
  return (
    <div className="w-full sm:w-[calc(50%-6px)] md:w-[calc(33.333%-8px)] lg:w-[calc(25%-9px)] mb-3">
      <Link
        to={`/watch/anime/${animeId}?ep=${episode.number}`}
        className="group flex bg-black/40 rounded-lg overflow-hidden shadow-lg border border-white/5 hover:border-white/20 transition-all duration-300 h-20 w-full"
        onMouseEnter={() => onHover && onHover(episode.image || animeCover)}
      >
        {/* Thumbnail */}
        <div className="relative w-36 shrink-0">
          <Image
            src={episode.image || animeCover}
            className="object-cover w-full h-full opacity-90 group-hover:opacity-100 group-hover:scale-105 transition-all duration-300"
          />
          {/* Play button overlay on hover */}
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
            <div className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center backdrop-blur-sm border border-white/20">
              <PlayIcon className="text-white ml-0.5" size={14} />
            </div>
          </div>
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent to-black/20"></div>
        </div>

        {/* Episode info */}
        <div className="flex flex-col justify-center p-3 flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-0.5">
            <div className="bg-black/60 backdrop-blur-sm text-white text-[10px] px-1.5 py-0.5 rounded-md font-medium border border-white/10 w-fit">
              EP {episode.number}
            </div>
          </div>
          <h3 className="text-xs sm:text-sm font-medium line-clamp-1 group-hover:text-white transition-colors">
            {episode.title || `Episode ${episode.number}`}
          </h3>
        </div>
      </Link>
    </div>
  );
});

// Memoized Episode Card for Compact View
const CompactEpisodeCard = memo(({ episode, animeId, animeCover, onHover }) => {
  return (
    <Link
      to={`/watch/anime/${animeId}?ep=${episode.number}`}
      className="group flex items-center gap-3 p-2.5 rounded-lg bg-black/30 hover:bg-black/50 transition-all duration-300 border border-white/5 hover:border-white/20"
      onMouseEnter={() => onHover && onHover(episode.image || animeCover)}
    >
      <div className="w-10 h-10 bg-black/60 backdrop-blur-sm rounded-lg flex items-center justify-center shrink-0 group-hover:bg-black transition-colors border border-white/10 group-hover:border-white/20">
        <span className="text-xs font-medium">{episode.number}</span>
      </div>
      <div className="flex flex-col min-w-0 flex-1">
        <div className="text-xs line-clamp-1 text-white/90 group-hover:text-white transition-colors font-medium">
          {episode.title || `Episode ${episode.number}`}
        </div>
      </div>
    </Link>
  );
});

// Memoized Character Card
const CharacterCard = memo(({ character, animeCover, onHover }) => {
  return (
    <div className="flex-[0_0_150px] sm:flex-[0_0_170px]">
      <div
        className="group flex flex-col items-center text-center bg-black/30 backdrop-blur-sm p-3 rounded-xl border border-white/5 hover:border-white/20 transition-all duration-300 hover:bg-black/50 h-full"
        onMouseEnter={() => onHover && onHover(character.image || animeCover)}
      >
        <div className="w-20 h-20 rounded-full overflow-hidden mb-3 border border-white/10 bg-black/50 group-hover:border-white/30 transition-all duration-300 shadow-lg">
          <Image
            src={character.image || animeCover}
            className="object-cover w-full h-full group-hover:scale-105 transition-all duration-500"
          />
        </div>
        <div className="text-sm font-medium line-clamp-1 group-hover:text-white transition-colors">{character.name}</div>
        <div className="text-xs text-white/60 line-clamp-1 mt-0.5 px-2 py-0.5 bg-black/40 rounded-full border border-white/5 group-hover:bg-black/70 transition-all duration-300">
          {character.role || character.gender || "Character"}
        </div>
      </div>
    </div>
  );
});

// Main component
const AnimeInfo = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // Function to handle navigation to search results
  const handleSearchByTag = (tag, type) => {
    if (type === 'genre') {
      // Navigate to explore page with genre parameter
      navigate(`/explore?genre=${encodeURIComponent(tag)}&page=1`);
    } else if (type === 'studio') {
      // Navigate to explore page with studio parameter
      navigate(`/explore?studio=${encodeURIComponent(tag)}&page=1`);
    }
  };
  const [episodeSearch, setEpisodeSearch] = useState("");
  const [dynamicEpisodeCount, setDynamicEpisodeCount] = useState(null);
  const [apiEpisodes, setApiEpisodes] = useState([]);
  const [loadingEpisodes, setLoadingEpisodes] = useState(false);
  const [characters, setCharacters] = useState([]);
  const [loadingCharacters, setLoadingCharacters] = useState(false);
  // Initialize view style from localStorage or default to "grid"
  const [viewStyle, setViewStyle] = useState(() => {
    const savedStyle = localStorage.getItem("animeEpisodeViewStyle");
    return savedStyle || "grid"; // "grid", "list", or "compact"
  });

  // Embla carousel for grid view
  const [gridEmblaRef, gridEmblaApi] = useEmblaCarousel({
    dragFree: true,
    containScroll: "trimSnaps",
    align: "start"
  });

  // Embla carousel for characters
  const [charactersEmblaRef, charactersEmblaApi] = useEmblaCarousel({
    dragFree: true,
    containScroll: "trimSnaps",
    align: "start"
  });

  // Embla carousel for list view rows
  // Removed unused state variables

  // Navigation functions for grid carousel
  const scrollPrevGrid = useCallback(() => {
    if (gridEmblaApi) gridEmblaApi.scrollPrev();
  }, [gridEmblaApi]);

  const scrollNextGrid = useCallback(() => {
    if (gridEmblaApi) gridEmblaApi.scrollNext();
  }, [gridEmblaApi]);

  // Navigation functions for characters carousel
  const scrollPrevCharacters = useCallback(() => {
    if (charactersEmblaApi) charactersEmblaApi.scrollPrev();
  }, [charactersEmblaApi]);

  const scrollNextCharacters = useCallback(() => {
    if (charactersEmblaApi) charactersEmblaApi.scrollNext();
  }, [charactersEmblaApi]);

  const { titlePreference } = useTitlePreference();

  const { data: anime, isLoading } = useFetch({
    key: [`anime-info-${id}`],
    fun: async () => {
      return await getAnimeDetails(id);
    },
  });

  useEffect(() => {
    if (anime) {
      const title = titlePreference === TITLE_PREFERENCES.ENGLISH
        ? (anime.title || anime.titleRomaji)
        : (anime.titleRomaji || anime.title);
      document.title = `${title} - AnimeHQ`;
    } else {
      document.title = "Anime Info - AnimeHQ";
    }
  }, [anime, titlePreference]);

  // Save view style to localStorage when it changes
  useEffect(() => {
    localStorage.setItem("animeEpisodeViewStyle", viewStyle);
  }, [viewStyle]);

  // We'll use the API episodes count instead of fetching separately

  // Memoize the episodes list creation for better performance
  const episodesList = useMemo(() => {
    if (apiEpisodes.length > 0) {
      // If we have API episodes with images, use those
      return apiEpisodes.map(ep => ({
        number: parseInt(ep.number),
        image: ep.image,
        watchId: ep.watchId,
        title: ep.title || `Episode ${ep.number}`
      }));
    } else {
      // Fallback to generating episodes from count
      const episodeCount = dynamicEpisodeCount || anime?.episodes || 0;
      return Array.from({ length: episodeCount })
        .map((_, index) => ({
          number: index + 1,
          image: null,
          title: `Episode ${index + 1}`
        }));
    }
  }, [apiEpisodes, dynamicEpisodeCount, anime?.episodes]);

  // Filter episodes based on search - memoized for better performance
  const filteredEpisodes = useMemo(() => {
    return episodesList.filter(episode => {
      if (!episodeSearch) return true;
      return episode.number.toString().includes(episodeSearch) ||
             (episode.title && episode.title.toLowerCase().includes(episodeSearch.toLowerCase()));
    });
  }, [episodesList, episodeSearch]);

  // Fetch episode images and count from API
  useEffect(() => {
    if (!id) return;

    const fetchEpisodes = async () => {
      setLoadingEpisodes(true);
      try {
        const episodesList = await getAnimeEpisodes(id);
        setApiEpisodes(episodesList);

        // Set the dynamic episode count from the API response
        if (episodesList && episodesList.length > 0) {
          setDynamicEpisodeCount(episodesList.length);
          console.log(`Using API episode count: ${episodesList.length}`);
        }
      } catch (error) {
        console.error("Error fetching episode images:", error);
      } finally {
        setLoadingEpisodes(false);
      }
    };

    fetchEpisodes();
  }, [id]);

  // Function to fetch characters from Jikan API
  const fetchCharacters = async () => {
    if (!anime?.title) return;

    try {
      setLoadingCharacters(true);

      // First, we need to search for the anime on Jikan to get the MAL ID
      const searchResponse = await axios.get(`https://api.jikan.moe/v4/anime?q=${encodeURIComponent(anime.title)}&limit=1`);

      if (searchResponse.data.data && searchResponse.data.data.length > 0) {
        const malId = searchResponse.data.data[0].mal_id;

        // Now fetch the characters with the MAL ID
        const charactersResponse = await axios.get(`https://api.jikan.moe/v4/anime/${malId}/characters`);

        if (charactersResponse.data.data && charactersResponse.data.data.length > 0) {
          // Format the character data
          const formattedCharacters = charactersResponse.data.data.map(char => ({
            name: char.character.name,
            image: char.character.images.jpg.image_url,
            gender: char.character.gender || "Unknown",
            role: char.role
          }));

          setCharacters(formattedCharacters);
        }
      }
    } catch (error) {
      console.error("Error fetching characters:", error);
    } finally {
      setLoadingCharacters(false);
    }
  };

  // Fetch characters when anime data is available
  useEffect(() => {
    if (anime?.title) {
      fetchCharacters();
    }
  }, [anime?.title]);

  // State for touch indicators
  const [showTouchIndicator, setShowTouchIndicator] = useState(true);
  const [showCharactersTouchIndicator, setShowCharactersTouchIndicator] = useState(true);
  const [ambientImage, setAmbientImage] = useState(null);

  // Set initial ambient image when anime data is loaded
  useEffect(() => {
    if (anime?.images?.coverLarge) {
      setAmbientImage(anime.images.coverLarge);
    }
  }, [anime?.images?.coverLarge]);

  // Handle carousel initialization and cleanup
  useEffect(() => {
    // Initialize carousel when it's mounted
    if (gridEmblaApi) {
      // Add event listeners or additional configuration if needed
      const onSelect = () => {
        // Hide the touch indicator after user interacts with the carousel
        setShowTouchIndicator(false);
      };

      const onPointerDown = () => {
        // Hide the touch indicator when user starts dragging
        setShowTouchIndicator(false);
      };

      gridEmblaApi.on('select', onSelect);
      gridEmblaApi.on('pointerDown', onPointerDown);

      // Return cleanup function
      return () => {
        gridEmblaApi.off('select', onSelect);
        gridEmblaApi.off('pointerDown', onPointerDown);
      };
    }
  }, [gridEmblaApi]);

  // Reset touch indicator when view style changes
  useEffect(() => {
    setShowTouchIndicator(true);
  }, [viewStyle]);

  // Handle characters carousel initialization and cleanup
  useEffect(() => {
    if (charactersEmblaApi) {
      const onSelect = () => {
        setShowCharactersTouchIndicator(false);
      };

      const onPointerDown = () => {
        setShowCharactersTouchIndicator(false);
      };

      charactersEmblaApi.on('select', onSelect);
      charactersEmblaApi.on('pointerDown', onPointerDown);

      return () => {
        charactersEmblaApi.off('select', onSelect);
        charactersEmblaApi.off('pointerDown', onPointerDown);
      };
    }
  }, [charactersEmblaApi]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[70vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
      </div>
    );
  }

  if (!anime) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[70vh] gap-4">
        <h1 className="text-2xl font-bold">Anime not found</h1>
        <button
          onClick={() => navigate(-1)}
          className="flex items-center gap-2 bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg"
        >
          <ArrowLeft size={18} /> Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full gap-6">
      {/* Hero Section - Minimalist Layered Design */}
      <div className="w-full relative">
        {/* Main Content Card */}
        <div className="bg-gradient-to-br from-black/90 to-black/70 backdrop-blur-md rounded-xl overflow-hidden border border-white/10">
          {/* Top Banner Area */}
          <div className="relative h-40 sm:h-48 overflow-hidden">
            <Image
              src={anime?.images?.bannerLarge || anime?.images?.coverLarge}
              className="object-cover w-full h-full filter grayscale-[30%]"
            />
            <div className="absolute inset-0 bg-gradient-to-b from-black/30 to-black"></div>

            {/* Top Bar with Type and Status */}
            <div className="absolute top-0 inset-x-0 p-3 flex justify-between items-center">
              <div className="flex gap-2">
                {anime?.type && (
                  <span className="bg-white text-black text-xs px-2 py-0.5 font-medium">
                    {anime.type}
                  </span>
                )}
                {anime?.status && (
                  <span className="bg-white/10 backdrop-blur-sm text-white/90 text-xs px-2 py-0.5">
                    {anime.status}
                  </span>
                )}
              </div>

              {anime?.rating && (
                <div className="bg-white/10 backdrop-blur-sm px-2 py-0.5 rounded flex items-center gap-1">
                  <Star fill="white" color="white" size={12} />
                  <span className="text-xs font-medium">{anime.rating}</span>
                </div>
              )}
            </div>
          </div>

          {/* Content Area */}
          <div className="px-4 sm:px-6 pb-5">
            {/* Cover and Info Row */}
            <div className="flex flex-col sm:flex-row gap-4 -mt-20 sm:-mt-24 mb-5">
              {/* Cover Image */}
              <div className="flex-shrink-0 mx-auto sm:mx-0">
                <div className="relative w-[130px] h-[195px] sm:w-[140px] sm:h-[210px] rounded-md overflow-hidden border border-white/20 shadow-[0_5px_15px_rgba(0,0,0,0.35)]">
                  <Image
                    src={anime?.images?.coverLarge}
                    className="object-cover w-full h-full"
                  />

                  {/* Age Rating */}
                  <div className="absolute top-1 left-1">
                    <AgeRating isAdult={anime?.isAdult} genres={anime?.genres} compact={true} />
                  </div>
                </div>
              </div>

              {/* Title and Info Column */}
              <div className="flex-1 flex flex-col sm:justify-start sm:pt-24">
                {/* Title */}
                <h1 className="text-xl sm:text-2xl font-bold mb-3 text-white text-center sm:text-left">
                  {titlePreference === TITLE_PREFERENCES.ENGLISH
                    ? (anime?.title || anime?.titleRomaji || "Unknown Title")
                    : (anime?.titleRomaji || anime?.title || "Unknown Title")}
                </h1>

                {/* Quick Stats */}
                <div className="flex flex-wrap justify-center sm:justify-start gap-x-4 gap-y-1 text-xs text-white/70 mb-3">
                  {anime?.release_date && (
                    <div className="flex items-center gap-1">
                      <Calendar size={12} className="text-white/50" />
                      <span>{anime.release_date}</span>
                    </div>
                  )}

                  <div className="flex items-center gap-1">
                    <Clock size={12} className="text-white/50" />
                    <span>{dynamicEpisodeCount || anime?.episodes || 0} Episodes</span>
                  </div>

                  {anime?.studios && anime.studios.length > 0 && (
                    <div className="flex items-center gap-1">
                      <Building size={12} className="text-white/50" />
                      <button
                        onClick={() => handleSearchByTag(anime.studios[0].id, 'studio')}
                        className="hover:text-blue-400 transition-colors cursor-pointer"
                      >
                        {anime.studios[0].name}
                      </button>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex justify-center sm:justify-start gap-2">
                  <Link
                    to={`/watch/anime/${anime?.id}`}
                    className="bg-white hover:bg-white/90 text-black px-4 py-1.5 rounded flex items-center gap-1.5 font-medium transition-colors"
                  >
                    <PlayIcon size={16} /> Watch Now
                  </Link>
                  <AniListSync anime={anime} />
                </div>
              </div>
            </div>

            {/* Description Preview */}
            <div className="text-sm text-white/80 mb-5 line-clamp-3">
              {anime?.description?.replace(/<[^>]*>/g, "")}
            </div>

            {/* Genres - Clickable */}
            {anime?.genres && anime.genres.length > 0 && (
              <div className="mb-5">
                <div className="flex flex-wrap gap-1.5">
                  {anime.genres.map((genre, index) => (
                    <button
                      key={index}
                      onClick={() => handleSearchByTag(genre.name, 'genre')}
                      className="px-2 py-0.5 bg-white/5 hover:bg-white/10 text-xs text-white/80 rounded-sm cursor-pointer transition-colors"
                    >
                      {genre.name}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Tabs Navigation */}
            <div className="flex border-b border-white/10 text-sm">
              <div className="px-4 py-2 border-b-2 border-white font-medium">Overview</div>

            </div>

            {/* Information Grid */}
            <div className="mt-5">
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-3">
                <div className="bg-[#0f0f0f] rounded p-3 flex flex-col items-center">
                  <div className="text-xs text-white/60 mb-1">Type</div>
                  <div className="text-sm font-medium">{anime?.type || "ANIME"}</div>
                </div>

                <div className="bg-[#0f0f0f] rounded p-3 flex flex-col items-center">
                  <div className="text-xs text-white/60 mb-1">Episodes</div>
                  <div className="text-sm font-medium">{dynamicEpisodeCount || anime?.episodes || 0}</div>
                </div>

                <div className="bg-[#0f0f0f] rounded p-3 flex flex-col items-center">
                  <div className="text-xs text-white/60 mb-1">Status</div>
                  <div className="text-sm font-medium">{anime?.status || "RELEASING"}</div>
                </div>

                {anime?.studios && anime.studios.length > 0 && (
                  <div className="bg-[#0f0f0f] rounded p-3 flex flex-col items-center">
                    <div className="text-xs text-white/60 mb-1">Studio</div>
                    <button
                      onClick={() => handleSearchByTag(anime.studios[0].id, 'studio')}
                      className="text-sm font-medium truncate max-w-full hover:text-blue-400 transition-colors cursor-pointer"
                    >
                      {anime.studios[0].name}
                    </button>
                  </div>
                )}

                {anime?.release_date && (
                  <div className="bg-[#0f0f0f] rounded p-3 flex flex-col items-center">
                    <div className="text-xs text-white/60 mb-1">Released</div>
                    <div className="text-sm font-medium">{anime.release_date}</div>
                  </div>
                )}

                {anime?.rating && (
                  <div className="bg-[#0f0f0f] rounded p-3 flex flex-col items-center">
                    <div className="text-xs text-white/60 mb-1">Rating</div>
                    <div className="text-sm font-medium flex items-center gap-1">
                      <Star fill="white" color="white" size={12} />
                      <span>{anime.rating}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Details Section */}
      <div className="grid grid-cols-1 gap-4 sm:gap-6 px-2 sm:px-0">
        {/* Episodes */}
        <div className="flex flex-col gap-4">
          <div
            className="bg-gradient-to-br from-black to-[#0f0f0f] rounded-xl overflow-hidden border border-white/5 shadow-lg relative"
            onMouseLeave={() => setAmbientImage(null)}
          >
            {/* Ambient Background */}
            <div className="absolute inset-0 z-0 overflow-hidden">
              <div
                className={`absolute inset-0 scale-110 blur-xl transition-all duration-700 ${ambientImage ? 'opacity-30' : 'opacity-0'}`}
                style={{
                  backgroundImage: ambientImage ? `url(${ambientImage})` : 'none',
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  animation: 'subtle-pulse 8s ease-in-out infinite'
                }}
              ></div>
              <div className="absolute inset-0 bg-gradient-to-t from-black via-black/80 to-black/60 z-1"></div>
            </div>

            {/* Animation keyframes */}
            <style jsx>{`
              @keyframes subtle-pulse {
                0% { transform: scale(1.1); }
                50% { transform: scale(1.15); }
                100% { transform: scale(1.1); }
              }
            `}</style>
            <div className="p-4 sm:p-5">
              {/* Header with title and count */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="bg-white/10 p-2 rounded-lg">
                    <Layers size={20} className="text-white" />
                  </div>
                  <div>
                    <h2 className="text-lg sm:text-xl font-semibold">Episodes</h2>
                    <p className="text-xs text-white/60">
                      {dynamicEpisodeCount || anime?.episodes || 0} total episodes
                    </p>
                  </div>
                </div>

                {/* View style selector */}
                <div className="flex bg-black/40 backdrop-blur-sm rounded-lg overflow-hidden p-0.5 border border-white/10 shrink-0">
                  <button
                    onClick={() => setViewStyle("grid")}
                    className={`w-9 h-8 flex items-center justify-center transition-colors ${viewStyle === "grid" ? "bg-white/20 text-white rounded" : "text-gray-400 hover:text-white"}`}
                    title="Grid view"
                  >
                    <Grid size={16} />
                  </button>
                  <button
                    onClick={() => setViewStyle("list")}
                    className={`w-9 h-8 flex items-center justify-center transition-colors ${viewStyle === "list" ? "bg-white/20 text-white rounded" : "text-gray-400 hover:text-white"}`}
                    title="List view"
                  >
                    <List size={16} />
                  </button>
                  <button
                    onClick={() => setViewStyle("compact")}
                    className={`w-9 h-8 flex items-center justify-center transition-colors ${viewStyle === "compact" ? "bg-white/20 text-white rounded" : "text-gray-400 hover:text-white"}`}
                    title="Compact view"
                  >
                    <LayoutList size={16} />
                  </button>
                </div>
              </div>

              {/* Search input */}
              <div className="relative mb-4">
                <input
                  type="text"
                  placeholder="Search episodes..."
                  value={episodeSearch}
                  onChange={(e) => setEpisodeSearch(e.target.value)}
                  className="bg-black/40 backdrop-blur-sm border border-white/10 rounded-lg px-4 py-2.5 text-sm w-full focus:outline-none focus:ring-1 focus:ring-white/30 pr-9"
                />
                <div className="absolute right-3 top-1/2 -translate-y-1/2 text-white/50">
                  {episodeSearch ? (
                    <button
                      onClick={() => setEpisodeSearch('')}
                      className="hover:text-white"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                      </svg>
                    </button>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="11" cy="11" r="8"></circle>
                      <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                    </svg>
                  )}
                </div>
              </div>
            </div>
            <div className="p-4">
              {/* Loading state */}
              {loadingEpisodes ? (
                <div className="flex items-center justify-center py-12">
                  <div className="flex flex-col items-center gap-3">
                    <div className="relative w-12 h-12">
                      <div className="absolute inset-0 rounded-full border-t-2 border-white/20 animate-spin"></div>
                      <div className="absolute inset-0 rounded-full border-t-2 border-l-2 border-white/70 animate-spin"></div>
                      <Loader2 className="absolute inset-0 m-auto h-6 w-6 text-white/70" />
                    </div>
                    <div className="text-sm text-white/70">Loading episodes...</div>
                  </div>
                </div>
              ) : episodeSearch && filteredEpisodes.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <div className="w-16 h-16 bg-white/5 rounded-full flex items-center justify-center mb-4">
                    <AlertCircle className="h-8 w-8 text-white/50" />
                  </div>
                  <div className="text-white/70">No episodes matching "{episodeSearch}"</div>
                  <button
                    onClick={() => setEpisodeSearch('')}
                    className="mt-3 px-4 py-1.5 bg-white/10 hover:bg-white/20 rounded-full text-sm transition-colors"
                  >
                    Clear search
                  </button>
                </div>
              ) : (
                <>
                  {/* Filter count indicator */}
                  {episodeSearch && filteredEpisodes.length > 0 && filteredEpisodes.length !== episodesList.length && (
                    <div className="text-sm text-white/70 mb-3">
                      Showing {filteredEpisodes.length} of {episodesList.length} episodes
                    </div>
                  )}

                    {/* Carousel style for episodes */}
                    <div className="pb-4">
                      {/* Grid View - Embla Carousel with drag-to-scroll */}
                      {viewStyle === "grid" && (
                        <div className="relative">
                          {/* Carousel container */}
                          <div className="overflow-hidden" ref={gridEmblaRef}>
                            <div className="flex gap-4 pb-2">
                              {filteredEpisodes.map(episode => (
                                <GridEpisodeCard
                                  key={episode.number}
                                  episode={episode}
                                  animeId={anime?.id}
                                  animeCover={anime?.images?.coverLarge}
                                  onHover={setAmbientImage}
                                />
                              ))}
                            </div>
                          </div>

                          {/* Navigation buttons */}
                          <button
                            onClick={scrollPrevGrid}
                            className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 bg-black/70 hover:bg-black/90 w-10 h-10 rounded-full flex items-center justify-center border border-white/10 z-10 transition-all duration-300 opacity-70 hover:opacity-100"
                          >
                            <ChevronLeft size={20} />
                          </button>
                          <button
                            onClick={scrollNextGrid}
                            className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 bg-black/70 hover:bg-black/90 w-10 h-10 rounded-full flex items-center justify-center border border-white/10 z-10 transition-all duration-300 opacity-70 hover:opacity-100"
                          >
                            <ChevronRight size={20} />
                          </button>

                          {/* Drag indicator - only shown initially and on mobile */}
                          {showTouchIndicator && (
                            <div className="absolute bottom-0 left-1/2 -translate-x-1/2 bg-white/10 backdrop-blur-sm rounded-full px-3 py-1 text-xs text-white/70 border border-white/10 opacity-80 animate-pulse">
                              <div className="flex items-center gap-1.5">
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <path d="M21 12H3M3 12L10 5M3 12L10 19"></path>
                                </svg>
                                Slide to scroll
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <path d="M3 12H21M21 12L14 5M21 12L14 19"></path>
                                </svg>
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                      {/* List View - Single row Embla Carousel */}
                      {viewStyle === "list" && (
                        <div className="relative">
                          {/* Carousel container */}
                          <div className="overflow-hidden">
                            <div className="flex flex-wrap gap-3">
                              {filteredEpisodes.map(episode => (
                                <ListEpisodeCard
                                  key={episode.number}
                                  episode={episode}
                                  animeId={anime?.id}
                                  animeCover={anime?.images?.coverLarge}
                                  onHover={setAmbientImage}
                                />
                              ))}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Compact View - Grid layout */}
                      {viewStyle === "compact" && (
                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 pb-4">
                          {filteredEpisodes.map(episode => (
                            <CompactEpisodeCard
                              key={episode.number}
                              episode={episode}
                              animeId={anime?.id}
                              animeCover={anime?.images?.coverLarge}
                              onHover={setAmbientImage}
                            />
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Hide scrollbar styles */}
                    <style jsx>{`
                      .hide-scrollbar {
                        -ms-overflow-style: none;
                        scrollbar-width: none;
                      }
                      .hide-scrollbar::-webkit-scrollbar {
                        display: none;
                      }
                    `}</style>

                    {/* Custom scrollbar styles */}
                    <style jsx>{`
                      .custom-scrollbar::-webkit-scrollbar {
                        width: 8px;
                        height: 8px;
                      }
                      .custom-scrollbar::-webkit-scrollbar-track {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 10px;
                      }
                      .custom-scrollbar::-webkit-scrollbar-thumb {
                        background: rgba(255, 255, 255, 0.2);
                        border-radius: 10px;
                      }
                      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                        background: rgba(255, 255, 255, 0.3);
                      }
                    `}</style>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Characters Section */}
      <div className="mt-6 px-2 sm:px-0">
        <div
          className="bg-gradient-to-br from-black to-[#0f0f0f] rounded-xl overflow-hidden border border-white/5 shadow-lg relative"
          onMouseLeave={() => setAmbientImage(null)}
        >
          {/* Ambient Background */}
          <div className="absolute inset-0 z-0 overflow-hidden">
            <div
              className={`absolute inset-0 scale-110 blur-xl transition-all duration-700 ${ambientImage ? 'opacity-30' : 'opacity-0'}`}
              style={{
                backgroundImage: ambientImage ? `url(${ambientImage})` : 'none',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                animation: 'subtle-pulse 8s ease-in-out infinite'
              }}
            ></div>
            <div className="absolute inset-0 bg-gradient-to-t from-black via-black/80 to-black/60 z-1"></div>
          </div>

          <div className="p-4 sm:p-5 relative z-10">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="bg-white/10 p-2 rounded-lg">
                  <Users size={20} className="text-white" />
                </div>
                <div>
                  <h2 className="text-lg sm:text-xl font-semibold">Characters</h2>
                  <p className="text-xs text-white/60">
                    Main cast and voice actors
                  </p>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="mt-2">
              {loadingCharacters ? (
                <div className="flex items-center justify-center py-12">
                  <div className="flex flex-col items-center gap-3">
                    <div className="relative w-12 h-12">
                      <div className="absolute inset-0 rounded-full border-t-2 border-white/20 animate-spin"></div>
                      <div className="absolute inset-0 rounded-full border-t-2 border-l-2 border-white/70 animate-spin"></div>
                      <Loader2 className="absolute inset-0 m-auto h-6 w-6 text-white/70" />
                    </div>
                    <div className="text-sm text-white/70">Loading characters...</div>
                  </div>
                </div>
              ) : characters && characters.length > 0 ? (
                <div className="relative">
                  {/* Carousel container */}
                  <div className="overflow-hidden" ref={charactersEmblaRef}>
                    <div className="flex gap-4 pb-2">
                      {characters.map((character, index) => (
                        <CharacterCard
                          key={index}
                          character={character}
                          animeCover={anime?.images?.coverLarge}
                          onHover={setAmbientImage}
                        />
                      ))}
                    </div>
                  </div>

                  {/* Navigation buttons */}
                  <button
                    onClick={scrollPrevCharacters}
                    className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 bg-black/70 hover:bg-black/90 w-10 h-10 rounded-full flex items-center justify-center border border-white/10 z-10 transition-all duration-300 opacity-70 hover:opacity-100"
                  >
                    <ChevronLeft size={20} />
                  </button>
                  <button
                    onClick={scrollNextCharacters}
                    className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 bg-black/70 hover:bg-black/90 w-10 h-10 rounded-full flex items-center justify-center border border-white/10 z-10 transition-all duration-300 opacity-70 hover:opacity-100"
                  >
                    <ChevronRight size={20} />
                  </button>

                  {/* Drag indicator */}
                  {showCharactersTouchIndicator && (
                    <div className="absolute bottom-0 left-1/2 -translate-x-1/2 bg-white/10 backdrop-blur-sm rounded-full px-3 py-1 text-xs text-white/70 border border-white/10 opacity-80 animate-pulse">
                      <div className="flex items-center gap-1.5">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M21 12H3M3 12L10 5M3 12L10 19"></path>
                        </svg>
                        Slide to scroll
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M3 12H21M21 12L14 5M21 12L14 19"></path>
                        </svg>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="py-12 text-center text-white/60 bg-black/20 rounded-lg border border-white/5">
                  <div className="w-16 h-16 bg-black/30 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Users size={24} className="text-white/40" />
                  </div>
                  <p>No character information available</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Related Anime */}
      {anime?.relations && anime.relations.length > 0 && (
        <div className="mt-6 px-2 sm:px-0">
          <div className="bg-gradient-to-br from-black to-[#0f0f0f] rounded-xl overflow-hidden border border-white/5 shadow-lg">
            <div className="p-4 sm:p-5">
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="bg-white/10 p-2 rounded-lg">
                    <Building size={20} className="text-white" />
                  </div>
                  <div>
                    <h2 className="text-lg sm:text-xl font-semibold">Related Anime</h2>
                    <p className="text-xs text-white/60">
                      Sequels, prequels and spin-offs
                    </p>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="mt-2">
                <AnimeRelations relations={anime.relations} />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Recommendations */}
      {anime?.recommendations && anime.recommendations.length > 0 && (
        <div className="mt-6 px-2 sm:px-0">
          <div className="bg-gradient-to-br from-black to-[#0f0f0f] rounded-xl overflow-hidden border border-white/5 shadow-lg">
            <div className="p-4 sm:p-5">
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="bg-white/10 p-2 rounded-lg">
                    <Star size={20} className="text-white" />
                  </div>
                  <div>
                    <h2 className="text-lg sm:text-xl font-semibold">You May Also Like</h2>
                    <p className="text-xs text-white/60">
                      Similar anime recommendations
                    </p>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="mt-2">
                <Strip
                  title=""
                  data={anime.recommendations}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Export the memoized component for better performance
export default memo(AnimeInfo);
