import { useState, useEffect } from 'react';
import { Loader2, MessageSquare } from 'lucide-react';
import { getComments } from '@/api/comments';
import CommentItem from './CommentItem';
import NewComment from './NewComment';
import { useAniList } from '@/hooks/useAniList';

const CommentList = ({ animeId, episodeNumber, animeTitle, animeBanner }) => {
  const { user, isAuthenticated, login } = useAniList();
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchComments = async () => {
    try {
      setLoading(true);
      const data = await getComments(animeId, episodeNumber);
      setComments(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching comments:', err);
      setError('Failed to load comments. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchComments();
  }, [animeId, episodeNumber]);

  const handleCommentAdded = (newComment) => {
    setComments([newComment, ...comments]);
  };

  const handleCommentUpdated = (updatedComment) => {
    setComments(comments.map(comment =>
      comment._id === updatedComment._id ? updatedComment : comment
    ));
  };

  const handleCommentDeleted = (commentId) => {
    setComments(comments.filter(comment => comment._id !== commentId));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-10">
        <div className="flex flex-col items-center">
          <Loader2 className="animate-spin mb-3" size={30} />
          <span className="text-gray-300">Loading discussion...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-10 text-red-400">
        <p className="text-lg mb-2">{error}</p>
        <button
          onClick={fetchComments}
          className="mt-2 px-5 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-sm font-medium transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6 h-full">
      {/* Comment input section - fixed at top */}
      <div className="flex-shrink-0">
        {isAuthenticated && user ? (
          <NewComment
            animeId={animeId}
            episodeNumber={episodeNumber}
            animeTitle={animeTitle}
            animeBanner={animeBanner}
            user={user}
            onCommentAdded={handleCommentAdded}
          />
        ) : (
          <div className="text-center py-6 bg-white/5 hover:bg-white/8 transition-colors duration-200 rounded-xl border border-white/10">
            <p className="text-gray-300 mb-3">Join the discussion by logging in with your AniList account</p>
            <button
              onClick={login}
              className="px-6 py-2.5 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm font-medium transition-colors flex items-center gap-2 mx-auto"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 192 192" fill="currentColor">
                <path d="M64,32 L64,160 L128,160 L128,32 L64,32 Z M32,32 L32,160 L48,160 L48,32 L32,32 Z M144,32 L144,160 L160,160 L160,32 L144,32 Z" />
              </svg>
              Login with AniList
            </button>
          </div>
        )}
      </div>

      {/* Comments list - scrollable */}
      <div className="flex-grow overflow-y-auto pr-1 custom-scrollbar">
        {comments.length === 0 ? (
          <div className="text-center py-10 bg-white/5 hover:bg-white/8 transition-colors duration-200 rounded-xl border border-dashed border-white/10">
            <MessageSquare size={40} className="mx-auto mb-3 text-gray-500 opacity-50" />
            <p className="text-gray-300 font-medium">No comments yet</p>
            <p className="text-gray-400 text-sm mt-1">Be the first to share your thoughts on this episode!</p>
          </div>
        ) : (
          <div className="flex flex-col gap-5">
            <div className="flex items-center justify-between px-1 sticky top-0 py-2 z-10">
              <h3 className="text-sm font-medium text-gray-300">
                {comments.length} {comments.length === 1 ? 'Comment' : 'Comments'}
              </h3>
              <button
                onClick={fetchComments}
                className="text-xs text-gray-400 hover:text-blue-400 transition-colors flex items-center gap-1"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
                  <path d="M3 3v5h5"></path>
                  <path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16"></path>
                  <path d="M16 21h5v-5"></path>
                </svg>
                Refresh
              </button>
            </div>

            <div className="flex flex-col gap-3 pb-4">
              {comments.map(comment => (
                <CommentItem
                  key={comment._id}
                  comment={comment}
                  onCommentUpdated={handleCommentUpdated}
                  onCommentDeleted={handleCommentDeleted}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CommentList;
