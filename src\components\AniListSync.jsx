import { useState, useEffect } from "react";
import { useAniList } from "@/hooks/useAniList";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, ListPlus, Star, Check } from "lucide-react";

// Helper function to format status for display
const formatStatus = (status) => {
  if (!status) return null;

  const statusMap = {
    "CURRENT": "Watching",
    "PLANNING": "Plan to Watch",
    "COMPLETED": "Completed",
    "PAUSED": "Paused",
    "DROPPED": "Dropped"
  };

  return statusMap[status] || status;
};

const AniListSync = ({ anime, className = "" }) => {
  const { isAuthenticated, updateAnimeStatus, deleteAnimeFromList, login, checkAnimeInList } = useAniList();
  const [status, setStatus] = useState("PLANNING");
  const [progress, setProgress] = useState(0);
  const [isUpdating, setIsUpdating] = useState(false);
  const [open, setOpen] = useState(false);
  const [animeStatus, setAnimeStatus] = useState(null);
  const [isChecking, setIsChecking] = useState(false);

  // Check if anime is in user's list when component mounts or when authentication changes
  useEffect(() => {
    const checkStatus = async () => {
      if (isAuthenticated && anime?.id) {
        setIsChecking(true);
        const result = await checkAnimeInList(anime.id);
        if (result) {
          setAnimeStatus(result);
          setStatus(result.status);
          setProgress(result.progress || 0);
        } else {
          setAnimeStatus(null);
        }
        setIsChecking(false);
      }
    };

    checkStatus();
  }, [isAuthenticated, anime?.id, checkAnimeInList]);

  // Handle status update
  const handleUpdateStatus = async () => {
    if (!isAuthenticated) {
      login();
      return;
    }

    setIsUpdating(true);
    const success = await updateAnimeStatus(anime.id, status, parseInt(progress));

    if (success) {
      // Refresh the anime status
      const result = await checkAnimeInList(anime.id);
      if (result) {
        setAnimeStatus(result);
      }
    }

    setIsUpdating(false);
    setOpen(false);
  };

  // Handle removing from list
  const handleRemoveFromList = async () => {
    if (!isAuthenticated) {
      login();
      return;
    }

    setIsUpdating(true);
    await deleteAnimeFromList(anime.id);
    setIsUpdating(false);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className={`flex items-center gap-1 ${animeStatus
            ? 'bg-blue-600 text-white hover:bg-blue-700 hover:text-white'
            : 'bg-white/10 border border-white/20 text-white hover:bg-white/20'} ${className}`}
          onClick={() => {
            if (!isAuthenticated) {
              login();
            }
          }}
        >
          {isChecking ? (
            <Loader2 size={18} className="animate-spin" />
          ) : animeStatus ? (
            <Check size={18} />
          ) : (
            <ListPlus size={18} />
          )}

          {!isAuthenticated
            ? "Login to AniList"
            : isChecking
              ? "Checking..."
              : animeStatus
                ? formatStatus(animeStatus.status)
                : "Add to AniList"}
        </Button>
      </DialogTrigger>

      {isAuthenticated && (
        <DialogContent className="sm:max-w-[425px] bg-zinc-900 border-white/20 p-0 overflow-hidden">
          <div className="p-6 pb-4">
            <DialogHeader className="mb-4">
              <DialogTitle className="text-white text-xl">Update {anime?.title}</DialogTitle>
            </DialogHeader>

            <div className="space-y-5">
              <div className="flex flex-col space-y-2">
                <label htmlFor="status" className="text-white text-sm font-medium">
                  Status
                </label>
                <Select
                  value={status}
                  onValueChange={setStatus}
                >
                  <SelectTrigger id="status" className="bg-zinc-800 border-white/20 text-white w-full">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent className="bg-zinc-800 border-white/20">
                    <SelectItem value="CURRENT">Watching</SelectItem>
                    <SelectItem value="PLANNING">Plan to Watch</SelectItem>
                    <SelectItem value="COMPLETED">Completed</SelectItem>
                    <SelectItem value="PAUSED">Paused</SelectItem>
                    <SelectItem value="DROPPED">Dropped</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex flex-col space-y-2">
                <div className="flex justify-between">
                  <label htmlFor="progress" className="text-white text-sm font-medium">
                    Episodes
                  </label>
                  <span className="text-gray-300 text-sm">{anime?.episodes || "?"} total</span>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    id="progress"
                    type="number"
                    min="0"
                    max={anime?.episodes || 9999}
                    value={progress}
                    onChange={(e) => setProgress(e.target.value)}
                    className="bg-zinc-800 border border-white/20 px-3 py-2 rounded-md w-full text-white"
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-between p-4 bg-zinc-800/50 border-t border-white/10">
            <Button
              variant="destructive"
              onClick={handleRemoveFromList}
              disabled={isUpdating}
              className="bg-red-600 hover:bg-red-700"
            >
              {isUpdating ? <Loader2 className="h-4 w-4 animate-spin" /> : "Remove"}
            </Button>

            <div className="flex gap-2">
              <DialogClose asChild>
                <Button variant="outline" className="bg-transparent border-white/20 text-white hover:bg-white/10">
                  Cancel
                </Button>
              </DialogClose>
              <Button
                onClick={handleUpdateStatus}
                disabled={isUpdating}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {isUpdating ? <Loader2 className="h-4 w-4 animate-spin" /> : "Save"}
              </Button>
            </div>
          </div>
        </DialogContent>
      )}
    </Dialog>
  );
};

export default AniListSync;
