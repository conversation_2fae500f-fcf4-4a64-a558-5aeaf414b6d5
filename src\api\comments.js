import axios from 'axios';
import { API_URLS } from '@/config/api';

const API_BASE_URL = API_URLS.BACKEND_API;

/**
 * Get recent comments across all anime episodes
 * @param {number} limit - Maximum number of comments to return
 * @returns {Promise<Array>} - Array of recent comments
 */
export const getRecentComments = async (limit = 10) => {
  try {
    // Try to get real data first
    try {
      const response = await axios.get(`${API_BASE_URL}/comments/recent?limit=${limit}`);
      if (response.data && response.data.length > 0) {
        return response.data;
      }
    } catch (apiError) {
      console.warn('API error, falling back to mock data:', apiError);
    }

    // If API fails or returns empty, use mock data for testing
    console.log('Using mock comment data for testing');

    // Get current user ID from AniList if available
    let currentUserId = null;
    try {
      const userDataStr = localStorage.getItem('anilist-user');
      if (userDataStr) {
        const userData = JSON.parse(userDataStr);
        currentUserId = userData.id;
      }
    } catch (e) {
      console.error('Error getting user data:', e);
    }

    // Generate mock data
    const mockComments = [
      {
        id: '1',
        animeId: '21',
        episodeId: '1',
        content: 'This episode was amazing! The animation was top-notch.',
        username: 'AnimeExpert',
        userId: '123',
        timestamp: Date.now() - 1000 * 60 * 5, // 5 minutes ago
        likes: 15,
        replyTo: currentUserId ? { userId: currentUserId } : null
      },
      {
        id: '2',
        animeId: '21',
        episodeId: '1',
        content: 'I disagree, I thought the pacing was off in this episode.',
        username: 'CriticFan',
        userId: '456',
        timestamp: Date.now() - 1000 * 60 * 15, // 15 minutes ago
        likes: 3,
        replyTo: null
      },
      {
        id: '3',
        animeId: '21',
        episodeId: '1',
        content: 'The fight scene at the end was epic!',
        username: 'ActionLover',
        userId: '789',
        timestamp: Date.now() - 1000 * 60 * 30, // 30 minutes ago
        likes: 8,
        replyTo: currentUserId ? { userId: currentUserId } : null
      }
    ];

    return mockComments.slice(0, limit);
  } catch (error) {
    console.error('Error fetching recent comments:', error);
    return [];
  }
};

/**
 * Get comments for a specific anime episode
 * @param {string} animeId - The anime ID
 * @param {number} episodeNumber - The episode number
 * @returns {Promise<Array>} - Array of comments
 */
export const getComments = async (animeId, episodeNumber) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/comments/${animeId}/${episodeNumber}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching comments:', error);
    return [];
  }
};

/**
 * Add a new comment
 * @param {Object} commentData - Comment data
 * @param {string} commentData.animeId - The anime ID
 * @param {number} commentData.episodeNumber - The episode number
 * @param {string} commentData.userId - The user ID
 * @param {string} commentData.userName - The user name
 * @param {string} commentData.userAvatar - The user avatar URL
 * @param {string} commentData.content - The comment content
 * @param {boolean} [commentData.hasSpoiler] - Whether the comment contains spoilers
 * @returns {Promise<Object>} - The saved comment
 */
export const addComment = async (commentData) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/comments`, commentData);
    return response.data;
  } catch (error) {
    console.error('Error adding comment:', error);
    throw error;
  }
};

/**
 * Update a comment
 * @param {string} commentId - The comment ID
 * @param {Object} updateData - Data to update
 * @param {string} updateData.content - The updated content
 * @param {string} updateData.userId - The user ID (for authorization)
 * @returns {Promise<Object>} - The updated comment
 */
export const updateComment = async (commentId, updateData) => {
  try {
    const response = await axios.put(`${API_BASE_URL}/comments/${commentId}`, updateData);
    return response.data;
  } catch (error) {
    console.error('Error updating comment:', error);
    throw error;
  }
};

/**
 * Delete a comment
 * @param {string} commentId - The comment ID
 * @param {string} userId - The user ID (for authorization)
 * @returns {Promise<Object>} - Success message
 */
export const deleteComment = async (commentId, userId) => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/comments/${commentId}`, {
      data: { userId }
    });
    return response.data;
  } catch (error) {
    console.error('Error deleting comment:', error);
    throw error;
  }
};

/**
 * Like a comment
 * @param {string} commentId - The comment ID
 * @returns {Promise<Object>} - The updated comment
 */
export const likeComment = async (commentId) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/comments/${commentId}/like`);
    return response.data;
  } catch (error) {
    console.error('Error liking comment:', error);
    throw error;
  }
};

/**
 * Add a reply to a comment
 * @param {string} commentId - The comment ID
 * @param {Object} replyData - Reply data
 * @param {string} replyData.userId - The user ID
 * @param {string} replyData.userName - The user name
 * @param {string} replyData.userAvatar - The user avatar URL
 * @param {string} replyData.content - The reply content
 * @param {boolean} [replyData.hasSpoiler] - Whether the reply contains spoilers
 * @returns {Promise<Object>} - The updated comment with the new reply
 */
export const addReply = async (commentId, replyData) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/comments/${commentId}/reply`, replyData);
    return response.data;
  } catch (error) {
    console.error('Error adding reply:', error);
    throw error;
  }
};

/**
 * Update a reply
 * @param {string} commentId - The comment ID
 * @param {string} replyId - The reply ID
 * @param {Object} updateData - Data to update
 * @param {string} updateData.content - The updated content
 * @param {string} updateData.userId - The user ID (for authorization)
 * @param {boolean} [updateData.hasSpoiler] - Whether the comment contains spoilers
 * @returns {Promise<Object>} - The updated comment
 */
export const updateReply = async (commentId, replyId, updateData) => {
  try {
    const response = await axios.put(`${API_BASE_URL}/comments/${commentId}/reply/${replyId}`, updateData);
    return response.data;
  } catch (error) {
    console.error('Error updating reply:', error);
    throw error;
  }
};

/**
 * Delete a reply
 * @param {string} commentId - The comment ID
 * @param {string} replyId - The reply ID
 * @param {string} userId - The user ID (for authorization)
 * @returns {Promise<Object>} - Success message
 */
export const deleteReply = async (commentId, replyId, userId) => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/comments/${commentId}/reply/${replyId}`, {
      data: { userId }
    });
    return response.data;
  } catch (error) {
    console.error('Error deleting reply:', error);
    throw error;
  }
};

/**
 * Like a reply
 * @param {string} commentId - The comment ID
 * @param {string} replyId - The reply ID
 * @returns {Promise<Object>} - The updated comment
 */
export const likeReply = async (commentId, replyId) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/comments/${commentId}/reply/${replyId}/like`);
    return response.data;
  } catch (error) {
    console.error('Error liking reply:', error);
    throw error;
  }
};

export default {
  getRecentComments,
  getComments,
  addComment,
  updateComment,
  deleteComment,
  likeComment,
  addReply,
  updateReply,
  deleteReply,
  likeReply
};
