import React, { useEffect, useState, useCallback, useRef } from 'react';
import { MediaPlayer, isHLSProvider, MediaProvider } from '@vidstack/react';
import { defaultLayoutIcons, DefaultVideoLayout } from '@vidstack/react/player/layouts/default';
import '@vidstack/react/player/styles/default/theme.css';
import '@vidstack/react/player/styles/default/layouts/video.css';
import Hls from 'hls.js';
import { getAnimeEpisodes, getAnimeSource } from '@/api/aninow';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { useWatchHistory } from '@/contexts/WatchHistoryContext';
import { trackWatchEpisode } from '@/utils/activityTracking';
import ScrollingText from '@/components/ui/ScrollingText';

// Custom styles for player controls
const playerStyles = `
  /* Base styles for the player */
  .vds-video-layout {
    --video-controls-transition: 0.2s ease-out;
    cursor: pointer; /* Always show pointer cursor over the player */
  }

  /* Controls visibility */
  .vds-video-layout .vds-controls {
    opacity: 0;
    transform: translateY(10px);
    transition: opacity var(--video-controls-transition),
                transform var(--video-controls-transition);
  }

  /* Show controls on hover - targeting the entire player */
  .vds-video-layout:hover .vds-controls {
    opacity: 1;
    transform: translateY(0);
  }

  /* Always show controls when paused */
  .vds-video-layout[data-paused] .vds-controls {
    opacity: 1;
    transform: translateY(0);
  }

  /* Ensure controls are visible when scrubbing */
  .vds-video-layout[data-scrubbing] .vds-controls {
    opacity: 1;
    transform: translateY(0);
  }

  /* Additional styles for better visibility */
  .vds-controls {
    --video-controls-background: rgba(0, 0, 0, 0.7);
    background: var(--video-controls-background);
    border-radius: 8px;
    padding: 4px;
  }

  /* Make sure the entire player area triggers control visibility */
  .vds-video-layout .vds-video-container {
    cursor: pointer;
  }
`;
import { debounce } from 'lodash';
import useHistory from '@/hooks/useHistory';
import { Loader2, AlertCircle, RefreshCw } from 'lucide-react';
import Image from '@/components/ui/Image';

// API endpoints are now imported from aninow.js

const VidstackPlayer = ({ details, episode = 1, episodes = [], onEpisodeChange }) => {
  // Parse episode number immediately to ensure it's a number
  const parsedEpisode = parseInt(episode) || 1;

  // Log initial props for debugging
  console.log('VidstackPlayer initialized with:', {
    animeId: details?.id,
    requestedEpisode: parsedEpisode,
    rawEpisodeFromProps: episode,
    availableEpisodes: episodes.length
  });

  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [episodeData, setEpisodeData] = useState(null);
  const [videoSource, setVideoSource] = useState(null);
  const [isDub, setIsDub] = useState(false);
  const [episodeNum, setEpisodeNum] = useState(parsedEpisode);
  const [isHls, setIsHls] = useState(false);
  const [apiEpisodes, setApiEpisodes] = useState([]);
  const [skipIntro, setSkipIntro] = useState(null);
  const [skipOutro, setSkipOutro] = useState(null);
  const [showSkipButton, setShowSkipButton] = useState(false);
  const [skipType, setSkipType] = useState(null); // 'intro' or 'outro'
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showHeader, setShowHeader] = useState(true);
  const [showSaveIndicator, setShowSaveIndicator] = useState(false); // Progress save indicator
  const headerTimeoutRef = useRef(null);
  const playerRef = useRef(null);
  // Progress tracking refs
  const lastProgressUpdateRef = useRef(0);
  const lastProgressPercentRef = useRef(0);

  // History hooks
  const { addToHistory } = useHistory();
  const { updateProgress, getProgress } = useWatchHistory();

  // Handle provider change to detect HLS
  const handleProviderChange = useCallback((provider) => {
    const usingHls = isHLSProvider(provider);
    console.log('Is using HLS provider:', usingHls);
    setIsHls(usingHls);

    if (usingHls) {
      // Set the HLS library directly on the provider
      // This is the recommended approach from Vidstack documentation
      provider.library = Hls;

      // Configure HLS.js with settings to handle problematic streams
      provider.config = {
        // Increase retries for loading segments
        maxLoadingRetry: 5,
        // Adjust buffer settings
        maxBufferLength: 30,
        maxMaxBufferLength: 60,
        // Enable low latency mode
        lowLatencyMode: true,
        // Critical for handling audio buffer append errors
        enableWorker: true,
        // Handle audio codec issues
        disableAudioSwitch: false,
        // Add error recovery options
        fragLoadingMaxRetry: 6,
        fragLoadingRetryDelay: 500,
        // Handle discontinuities better
        startFragPrefetch: true,
        // Add debug logs
        debug: false,
        // Handle audio codec issues
        defaultAudioCodec: undefined,
        // Disable XHR abort errors
        xhrSetup: (xhr) => {
          xhr.addEventListener('error', () => {
            console.warn('XHR error detected, attempting to continue playback');
          });
        }
      };

      // Add event listeners to handle specific HLS errors
      if (provider.instance) {
        const hls = provider.instance;

        // Listen for HLS errors
        hls.on(Hls.Events.ERROR, (_, data) => {
          console.warn('HLS Error:', data);

          if (data.fatal) {
            switch (data.type) {
              case Hls.ErrorTypes.NETWORK_ERROR:
                console.warn('Fatal network error, attempting recovery');
                hls.startLoad();
                break;
              case Hls.ErrorTypes.MEDIA_ERROR:
                console.warn('Fatal media error, attempting recovery');
                hls.recoverMediaError();
                break;
              default:
                console.error('Fatal error, cannot recover:', data);
                break;
            }
          } else {
            // Non-fatal errors
            if (data.details === 'bufferAppendError') {
              console.warn('Buffer append error detected, attempting recovery...');

              // Try to recover by seeking slightly
              const player = playerRef.current;
              if (player && player.currentTime > 0) {
                // Seek to current time to force buffer refresh
                const currentTime = player.currentTime;
                setTimeout(() => {
                  player.currentTime = currentTime + 0.1;
                }, 100);
              }
            }
          }
        });
      }

      console.log('HLS provider configured with enhanced error handling');
    }
  }, []);

  // Debounced history update
  const updateHistory = debounce((currentTime, duration) => {
    if (!details) return;

    const historyItem = {
      ...details,
      currentTime,
      duration,
      episode: episodeNum,
      currentEpisode: episodeNum,
    };

    addToHistory(historyItem);
  }, 5000);

  // Add player styles to the document
  useEffect(() => {
    // Create a style element
    const styleElement = document.createElement('style');
    styleElement.textContent = playerStyles;
    document.head.appendChild(styleElement);

    // Cleanup function
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  // State to track current progress percentage for the indicator
  const [currentProgressPercent, setCurrentProgressPercent] = useState(0);

  // Custom function to save progress with visual indicator
  const saveProgressWithIndicator = (animeId, episodeNum, progress) => {
    // Update current progress percentage for the indicator
    setCurrentProgressPercent(progress);

    // Show save indicator
    setShowSaveIndicator(true);

    // Call the actual update function
    updateProgress(animeId, episodeNum, progress);

    // Hide indicator after 1.5 seconds
    setTimeout(() => {
      setShowSaveIndicator(false);
    }, 1500);

    // Update last progress update time and percentage
    lastProgressUpdateRef.current = Date.now();
    lastProgressPercentRef.current = progress;
  };

  // Initialize progress tracking refs and set up auto-save timer
  useEffect(() => {
    // Set initial values for progress tracking
    lastProgressUpdateRef.current = Date.now();
    lastProgressPercentRef.current = 0;

    console.log('Progress tracking initialized');

    // Set up a timer to force save progress every 10 seconds
    const autoSaveInterval = setInterval(() => {
      try {
        if (details?.id && playerRef.current) {
          const currentTime = playerRef.current.currentTime;
          const duration = playerRef.current.duration;

          if (currentTime > 0 && duration > 0) {
            const progressPercent = Math.round((currentTime / duration) * 100);
            if (progressPercent > 0) {
              const animeIdStr = String(details.id);
              const episodeNumStr = String(episodeNum);
              console.log(`Auto-save timer: Saving progress: ${progressPercent}%`);

              // Use the indicator function
              saveProgressWithIndicator(animeIdStr, episodeNumStr, progressPercent);
            }
          }
        }
      } catch (error) {
        console.warn('Error in auto-save interval:', error);
        // Don't throw the error, just log it
      }
    }, 10000); // Save every 10 seconds

    // Clean up the interval when component unmounts
    return () => {
      clearInterval(autoSaveInterval);
    };
  }, [details?.id, episodeNum, updateProgress]);

  // Clean up all timeouts when component unmounts
  useEffect(() => {
    return () => {
      // Clear controls timeout
      if (window.controlsTimeout) {
        clearTimeout(window.controlsTimeout);
        window.controlsTimeout = null;
      }

      // Clear header timeout
      if (headerTimeoutRef.current) {
        clearTimeout(headerTimeoutRef.current);
        headerTimeoutRef.current = null;
      }
    };
  }, []);

  // Save progress when component unmounts
  useEffect(() => {
    return () => {
      try {
        // Force save progress when component unmounts
        if (details?.id && playerRef.current) {
          // Check if currentTime and duration properties exist
          if (typeof playerRef.current.currentTime !== 'undefined' &&
              typeof playerRef.current.duration !== 'undefined') {

            const currentTime = playerRef.current.currentTime;
            const duration = playerRef.current.duration;

            if (currentTime > 0 && duration > 0) {
              const progressPercent = Math.round((currentTime / duration) * 100);
              if (progressPercent > 0) {
                const animeIdStr = String(details.id);
                const episodeNumStr = String(episodeNum);
                console.log(`Component unmounting. Final progress save: ${progressPercent}%`);
                updateProgress(animeIdStr, episodeNumStr, progressPercent);
              }
            }
          }
        }
      } catch (error) {
        console.warn('Error saving progress on unmount:', error);
        // Don't throw the error, just log it
      }
    };
  }, [details?.id, episodeNum, updateProgress]);

  // Add global event listeners for player controls
  useEffect(() => {
    try {
      const playerContainer = document.getElementById('player-container');

      // Skip if container not found
      if (!playerContainer) return;

      let controlsTimeout;

      const showControlsHandler = () => {
        try {
          const playerElement = document.querySelector('.media-player');
          if (playerElement) {
            playerElement.classList.add('force-controls-visible');

            // Clear any existing timeout
            clearTimeout(controlsTimeout);

            // Set a new timeout to hide controls after inactivity
            controlsTimeout = setTimeout(() => {
              try {
                if (playerElement &&
                    playerRef.current &&
                    typeof playerRef.current.paused !== 'undefined' &&
                    !playerRef.current.paused) {
                  playerElement.classList.remove('force-controls-visible');
                }
              } catch (error) {
                console.warn('Error in controls timeout:', error);
              }
            }, 2500);
          }
        } catch (error) {
          console.warn('Error in showControlsHandler:', error);
        }
      };

      const hideControlsHandler = () => {
        try {
          const playerElement = document.querySelector('.media-player');
          if (playerElement &&
              playerRef.current &&
              typeof playerRef.current.paused !== 'undefined' &&
              !playerRef.current.paused) {
            playerElement.classList.remove('force-controls-visible');
          }
        } catch (error) {
          console.warn('Error in hideControlsHandler:', error);
        }
      };

      // Add event listeners
      playerContainer.addEventListener('mouseenter', showControlsHandler);
      playerContainer.addEventListener('mousemove', showControlsHandler);
      playerContainer.addEventListener('mouseleave', hideControlsHandler);

      // Store event handler references for pause and play
      let pauseHandler, playHandler;

      // Always show controls when paused
      if (playerRef.current) {
        pauseHandler = () => {
          try {
            const playerElement = document.querySelector('.media-player');
            if (playerElement) {
              playerElement.classList.add('force-controls-visible');
            }
          } catch (error) {
            console.warn('Error in pause handler:', error);
          }
        };

        playHandler = () => {
          try {
            showControlsHandler(); // Show controls briefly when playing starts
          } catch (error) {
            console.warn('Error in play handler:', error);
          }
        };

        // Add event listeners safely
        try {
          playerRef.current.addEventListener('pause', pauseHandler);
          playerRef.current.addEventListener('play', playHandler);
        } catch (error) {
          console.warn('Error adding player event listeners:', error);
        }
      }

      // Cleanup function
      return () => {
        try {
          clearTimeout(controlsTimeout);

          // Remove container event listeners
          if (playerContainer) {
            playerContainer.removeEventListener('mouseenter', showControlsHandler);
            playerContainer.removeEventListener('mousemove', showControlsHandler);
            playerContainer.removeEventListener('mouseleave', hideControlsHandler);
          }

          // Remove player event listeners
          if (playerRef.current && pauseHandler && playHandler) {
            try {
              playerRef.current.removeEventListener('pause', pauseHandler);
              playerRef.current.removeEventListener('play', playHandler);
            } catch (error) {
              console.warn('Error removing player event listeners:', error);
            }
          }
        } catch (error) {
          console.warn('Error in cleanup function:', error);
        }
      };
    } catch (error) {
      console.warn('Error setting up player controls:', error);
      return () => {}; // Return empty cleanup function
    }
  }, [playerRef.current]); // Re-run when player reference changes





  // Fetch episode data when details or episode changes
  useEffect(() => {
    if (!details?.id) return;

    // Force episodeNum to match the requested episode number from props
    const requestedEpNum = parseInt(episode);
    console.log('INITIAL: Requested episode number from URL:', requestedEpNum);

    // Immediately update episodeNum to match the requested episode
    if (episodeNum !== requestedEpNum) {
      console.log('INITIAL: Forcing episodeNum state to match requested episode:', requestedEpNum);
      setEpisodeNum(requestedEpNum);
    }

    const fetchEpisodeData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch episode data from API using the imported function
        console.log('Fetching episodes for anime ID:', details.id);

        const episodesList = await getAnimeEpisodes(details.id);
        console.log('Episodes data:', episodesList);
        setApiEpisodes(episodesList);

        // Use the episode number directly from props, not from state
        console.log('Looking for episode number (from props):', requestedEpNum);

        // Find the requested episode
        const episodeInfo = episodesList.find(ep => {
          const epNumber = parseInt(ep.number);
          return epNumber === requestedEpNum;
        });

        if (!episodeInfo) {
          console.error('Episode not found in list. Available episodes:', episodesList.map(ep => ep.number));
          console.error('Requested episode:', requestedEpNum);

          // If episode not found but we have episodes, use the first one
          if (episodesList.length > 0) {
            console.log('Using first available episode instead:', episodesList[0]);
            setEpisodeData(episodesList[0]);
            setEpisodeNum(episodesList[0].number);

            // Fetch video source for the first episode
            await fetchVideoSource(details.id, episodesList[0].watchId, episodesList[0].number, isDub);
            return;
          }

          throw new Error(`Episode ${requestedEpNum} not found`);
        }

        console.log('Selected episode:', episodeInfo);
        setEpisodeData(episodeInfo);
        setEpisodeNum(episodeInfo.number);

        // Fetch video source
        await fetchVideoSource(details.id, episodeInfo.watchId, episodeInfo.number, isDub);
      } catch (error) {
        console.error('Error fetching episode data:', error);
        setError(error.message || 'Failed to load episode data');
        setLoading(false);
      }
    };

    fetchEpisodeData();
  }, [details?.id, episode, isDub]);

  // Fetch video source with retry mechanism
  const fetchVideoSource = async (animeId, watchId, num, dub = false, retryCount = 0) => {
    try {
      console.log('Fetching source for anime ID:', animeId, 'watchId:', watchId, 'episode:', num, 'dub:', dub, 'retry:', retryCount);

      // Use the imported function to get the video source
      const sourceData = await getAnimeSource(animeId, watchId, num, dub);
      console.log('Source data received:', sourceData);

      // Check if we have a valid URL
      if (sourceData && sourceData.url && typeof sourceData.url === 'string') {
        console.log('Valid URL found:', sourceData.url);

        // Make sure the URL is properly formatted
        let finalUrl = sourceData.url;
        if (!finalUrl.startsWith('http')) {
          finalUrl = `https:${finalUrl}`;
        }

        console.log('Setting video source to:', finalUrl);
        setVideoSource(finalUrl);

        // Set skip intro/outro data if available
        if (sourceData.skipIntro) {
          console.log('Setting skip intro data:', sourceData.skipIntro);
          setSkipIntro(sourceData.skipIntro);
        } else {
          setSkipIntro(null);
        }

        if (sourceData.skipOutro) {
          console.log('Setting skip outro data:', sourceData.skipOutro);
          setSkipOutro(sourceData.skipOutro);
        } else {
          setSkipOutro(null);
        }

        setLoading(false);
      } else {
        console.error('Invalid source data:', sourceData);

        // Retry logic - try up to 3 times
        if (retryCount < 3) {
          console.log(`Retrying (${retryCount + 1}/3)...`);
          // Wait a bit before retrying
          await new Promise(resolve => setTimeout(resolve, 1000));
          return fetchVideoSource(animeId, watchId, num, dub, retryCount + 1);
        }

        throw new Error('No valid video source found in response after multiple attempts');
      }
    } catch (error) {
      console.error('Error fetching video source:', error);

      // Retry on network errors
      if (error.message.includes('network') && retryCount < 3) {
        console.log(`Network error, retrying (${retryCount + 1}/3)...`);
        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 1000));
        return fetchVideoSource(animeId, watchId, num, dub, retryCount + 1);
      }

      setError(error.message || 'Failed to load video source');
      setLoading(false);
    }
  };



  // Toggle dub/sub
  const toggleDub = () => {
    setIsDub(!isDub);
    setLoading(true);
    // Source will be refetched due to the useEffect dependency on isDub
  };

  // Retry loading with improved error handling
  const handleRetry = () => {
    setLoading(true);
    setError(null);

    if (details?.id && episodeData) {
      console.log('Retrying with existing episode data');
      // Reset video source before retrying
      setVideoSource(null);
      // Start fresh with retry count 0
      fetchVideoSource(details.id, episodeData.watchId, episodeData.number, isDub, 0);
    } else if (details?.id) {
      console.log('Retrying by refetching episode data');
      // Refetch episode data if we don't have it
      const fetchEpisodeData = async () => {
        try {
          const episodesList = await getAnimeEpisodes(details.id);
          console.log('Refetched episodes list:', episodesList);
          setApiEpisodes(episodesList);

          if (!episodesList || episodesList.length === 0) {
            throw new Error('No episodes found for this anime');
          }

          // Get the requested episode number directly from props
          const requestedEpNum = parseInt(episode);
          console.log('Retry: Looking for episode number (from props):', requestedEpNum);

          // Force episodeNum to match the requested episode
          console.log('Retry: Forcing episodeNum state to match requested episode:', requestedEpNum);
          setEpisodeNum(requestedEpNum);

          const episodeInfo = episodesList.find(ep => parseInt(ep.number) === requestedEpNum);

          // If requested episode not found, use the first available episode
          if (!episodeInfo && episodesList.length > 0) {
            console.log(`Episode ${requestedEpNum} not found, using first available episode instead`);
            setEpisodeData(episodesList[0]);
            setEpisodeNum(episodesList[0].number);
            await fetchVideoSource(details.id, episodesList[0].watchId, episodesList[0].number, isDub, 0);
            return;
          } else if (!episodeInfo) {
            throw new Error(`Episode ${requestedEpNum} not found`);
          }

          console.log('Using episode info:', episodeInfo);
          setEpisodeData(episodeInfo);
          setEpisodeNum(episodeInfo.number);

          // Reset video source before retrying
          setVideoSource(null);
          await fetchVideoSource(details.id, episodeInfo.watchId, episodeInfo.number, isDub, 0);
        } catch (error) {
          console.error('Error retrying:', error);
          setError(error.message || 'Failed to load episode data');
          setLoading(false);
        }
      };

      fetchEpisodeData();
    } else {
      setError('Missing anime details. Please try refreshing the page.');
      setLoading(false);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="size-full flex items-center justify-center bg-black">
        <div className="flex flex-col items-center">
          <Loader2 className="animate-spin text-blue-500 mb-2" size={40} />
          <p className="text-white text-sm">Loading video...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="size-full relative bg-black flex items-center justify-center">
        {details?.poster && (
          <div className="absolute inset-0 opacity-20">
            <Image
              src={details.poster}
              alt={details.title}
              className="w-full h-full object-cover"
            />
          </div>
        )}
        <div className="z-10 flex flex-col items-center p-4 text-center">
          <AlertCircle className="text-red-500 mb-4" size={40} />
          <div className="text-white text-center mb-4">
            <p className="text-lg font-medium mb-1">Error Loading Episode</p>
            <p className="text-sm text-gray-300">{error}</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={handleRetry}
              className="flex items-center justify-center gap-2 bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <RefreshCw size={16} />
              Retry
            </button>

            <button
              onClick={() => window.location.reload()}
              className="flex items-center justify-center gap-2 bg-blue-600/80 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Reload Page
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Handle time updates for history tracking and skip intro/outro
  const handleTimeUpdate = (event) => {
    const currentTime = event.currentTime;
    const duration = event.duration;

    // Update both history systems
    if (currentTime > 0 && duration > 0) {
      // Update the legacy history system
      updateHistory(currentTime, duration);

      // Update the new watch history system
      if (details?.id) {
        // Calculate progress percentage (0-100)
        const progressPercent = Math.round((currentTime / duration) * 100);

        // Track episode watching for task verification when progress reaches 10%
        // We'll track this more aggressively to ensure the task can be completed
        if (progressPercent >= 10) {
          const userId = localStorage.getItem('anilist') ? JSON.parse(localStorage.getItem('anilist')).id : null;
          if (userId) {
            // Track the episode watching for task verification
            trackWatchEpisode(userId, {
              animeId: details.id,
              episodeNumber: episodeNum,
              title: details.title,
              progress: progressPercent,
              isAiring: details.status === 'RELEASING' || details.status === 'CURRENTLY_RELEASING'
            });

            // Log that we've tracked the episode
            console.log(`Tracked episode watching for task verification: ${progressPercent}% complete`);
          }
        }

        // Only update if progress is meaningful (avoid excessive updates)
        if (progressPercent > 0) {
          const animeIdStr = String(details.id);
          const episodeNumStr = String(episodeNum);

          // Get current time to check if we should update
          const now = Date.now();

          // Only update progress every 5 seconds or if progress changed by more than 5%
          const timeSinceLastUpdate = now - lastProgressUpdateRef.current;
          const progressDiff = Math.abs(progressPercent - lastProgressPercentRef.current);

          // Update progress if:
          // 1. It's been at least 5 seconds since last update, OR
          // 2. Progress changed by more than 5%, OR
          // 3. We're at the beginning (0-5%) or end (95-100%) of the video
          if (timeSinceLastUpdate > 5000 ||
              progressDiff > 5 ||
              progressPercent < 5 ||
              progressPercent > 95) {

            console.log(`Auto-saving progress from timeUpdate: ${progressPercent}%`);

            // Use the indicator function instead of direct updateProgress
            saveProgressWithIndicator(animeIdStr, episodeNumStr, progressPercent);
          }
        }
      }
    }

    // Check for skip intro/outro
    if (skipIntro && currentTime >= skipIntro.start && currentTime < skipIntro.end) {
      setShowSkipButton(true);
      setSkipType('intro');
    } else if (skipOutro && currentTime >= skipOutro.start && currentTime < skipOutro.end) {
      setShowSkipButton(true);
      setSkipType('outro');
    } else {
      setShowSkipButton(false);
    }
  };

  // Handle skip intro/outro
  const handleSkip = () => {
    if (!playerRef.current) return;

    if (skipType === 'intro' && skipIntro) {
      playerRef.current.currentTime = skipIntro.end;
    } else if (skipType === 'outro' && skipOutro) {
      playerRef.current.currentTime = skipOutro.end;
    }

    setShowSkipButton(false);
  };

  // Controls visibility management
  const showControls = () => {
    try {
      const controlsEl = document.querySelector('.vds-controls');
      if (controlsEl) {
        controlsEl.style.opacity = '1';
        controlsEl.style.visibility = 'visible';
        controlsEl.style.transform = 'none';
      }
    } catch (error) {
      console.warn('Error in showControls:', error);
      // Don't throw the error, just log it
    }
  };

  const hideControls = () => {
    try {
      // Only hide controls if player exists, is initialized, and is playing
      if (playerRef.current &&
          typeof playerRef.current.duration !== 'undefined' &&
          typeof playerRef.current.paused !== 'undefined' &&
          !playerRef.current.paused) {
        const controlsEl = document.querySelector('.vds-controls');
        if (controlsEl) {
          controlsEl.style.opacity = '0';
          controlsEl.style.visibility = 'hidden';
        }
      }
    } catch (error) {
      console.warn('Error in hideControls:', error);
      // Don't throw the error, just log it
    }
  };

  // Set up a timer to hide controls after inactivity
  const setupControlsTimer = () => {
    try {
      // Clear any existing timeout
      if (window.controlsTimeout) {
        clearTimeout(window.controlsTimeout);
        window.controlsTimeout = null;
      }

      // Show controls immediately
      showControls();

      // Show header if it was hidden
      setShowHeader(true);

      // Clear any existing header timeout
      if (headerTimeoutRef.current) {
        clearTimeout(headerTimeoutRef.current);
        headerTimeoutRef.current = null;
      }

      // Only set up hide controls timeout if player exists and has valid properties
      if (playerRef.current &&
          typeof playerRef.current.duration !== 'undefined' &&
          typeof playerRef.current.paused !== 'undefined') {

        // Hide controls after 3 seconds if video is playing
        window.controlsTimeout = setTimeout(() => {
          try {
            // Double-check player state before hiding controls
            // Make sure player still exists and has valid properties
            if (playerRef.current &&
                typeof playerRef.current.paused !== 'undefined' &&
                !playerRef.current.paused) {
              hideControls();
            }
          } catch (error) {
            console.warn('Error in controls timeout callback:', error);
            // Don't throw the error, just log it
          }
        }, 3000);
      }

      // Hide header after 3 seconds if in fullscreen
      if (isFullscreen) {
        headerTimeoutRef.current = setTimeout(() => {
          setShowHeader(false);
        }, 3000);
      }
    } catch (error) {
      console.warn('Error in setupControlsTimer:', error);
      // Don't throw the error, just log it
    }
  };





  // Player ready state
  return (
    <div className="w-full h-full relative bg-black/90 backdrop-blur-sm rounded-xl overflow-hidden">
      {/* Background image when paused */}
      <div className="absolute inset-0 z-0 opacity-30">
        <Image
          src={details?.poster || episodeData?.image || ''}
          className="object-cover w-full h-full blur-sm"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/80 to-transparent"></div>
      </div>

      {/* Vidstack Player */}
      <div
        className="relative z-10 w-full h-full group player-container"
        id="player-container"
        style={{ isolation: 'isolate' }}
        onMouseMove={setupControlsTimer}
        onTouchStart={setupControlsTimer}
      >
        {/* Transparent overlay to trigger controls - removed to avoid interference with buttons */}

        <MediaPlayer
          ref={playerRef}
          className="w-full h-full cursor-pointer object-contain group"
          title={`${details?.title || 'Anime'} - Episode ${episodeNum}`}
          src={videoSource}
          poster={details?.poster || episodeData?.image || ''}
          crossOrigin
          playsInline
          autoPlay
          onTimeUpdate={handleTimeUpdate}
          onProviderChange={handleProviderChange}
          onLoadedData={() => {
            // Restore playback position from watch history
            if (details?.id && playerRef.current) {
              // Always use string IDs for consistency
              const animeIdStr = String(details.id);
              const episodeNumStr = String(episodeNum);

              console.log(`Video loaded. Checking saved progress for anime ID: ${animeIdStr}, episode: ${episodeNumStr}`);
              const savedProgress = getProgress(animeIdStr, episodeNumStr);
              console.log(`Retrieved saved progress: ${savedProgress}%`);

              if (savedProgress > 0 && savedProgress < 98) { // Don't restore if almost finished
                const duration = playerRef.current.duration;
                console.log(`Video duration: ${duration}s`);

                if (duration) {
                  // Convert percentage back to seconds
                  const seekTime = (savedProgress / 100) * duration;
                  console.log(`Calculated seek time: ${seekTime.toFixed(2)}s from ${savedProgress}%`);

                  // Only seek if the time is valid
                  if (seekTime > 0 && seekTime < duration - 10) { // Don't seek to the very end
                    console.log(`Restoring playback position to: ${seekTime.toFixed(2)}s (${savedProgress}%)`);
                    playerRef.current.currentTime = seekTime;
                  } else {
                    console.log(`Not restoring position: seek time ${seekTime.toFixed(2)}s is invalid or too close to the end`);
                  }
                } else {
                  console.warn('Cannot restore position: duration is not available');
                }
              } else {
                console.log(`Not restoring position: progress is ${savedProgress}% (0 or >98%)`);
              }
            } else {
              console.warn('Cannot restore position: missing anime ID or player reference');
            }
          }}
          onPausedChange={(isPaused) => {
            if (isPaused) {
              showControls();

              // Force save progress when paused
              if (details?.id && playerRef.current) {
                const currentTime = playerRef.current.currentTime;
                const duration = playerRef.current.duration;

                if (currentTime > 0 && duration > 0) {
                  const progressPercent = Math.round((currentTime / duration) * 100);
                  if (progressPercent > 0) {
                    const animeIdStr = String(details.id);
                    const episodeNumStr = String(episodeNum);
                    console.log(`Video paused. Saving progress: ${progressPercent}%`);
                    // Use the indicator function instead of direct updateProgress
                    saveProgressWithIndicator(animeIdStr, episodeNumStr, progressPercent);
                  }
                }
              }
            } else {
              setupControlsTimer();
            }
          }}
          onFullscreenChange={(isFullscreenActive) => {
            // Update fullscreen state
            setIsFullscreen(isFullscreenActive);

            // Always show header when entering/exiting fullscreen
            setShowHeader(true);

            // Reset controls visibility after fullscreen change
            setTimeout(() => {
              setupControlsTimer();

              // If entering fullscreen, set timeout to hide header
              if (isFullscreenActive) {
                if (headerTimeoutRef.current) {
                  clearTimeout(headerTimeoutRef.current);
                }
                headerTimeoutRef.current = setTimeout(() => {
                  setShowHeader(false);
                }, 3000);
              }
            }, 500);
          }}
          onError={(error) => {
            console.error('Vidstack player error:', error);

            // Check if it's an HLS error
            if (error.message && error.message.includes('HLS')) {
              console.warn('HLS error detected in player, attempting recovery...');

              // Try to recover by reloading the source after a short delay
              setTimeout(() => {
                if (playerRef.current) {
                  // Store current time
                  const currentTime = playerRef.current.currentTime;

                  // Force reload of the source
                  setVideoSource('');
                  setTimeout(() => {
                    setVideoSource(videoSource);

                    // Restore playback position after source is loaded
                    setTimeout(() => {
                      if (playerRef.current && currentTime > 0) {
                        playerRef.current.currentTime = currentTime;
                      }
                    }, 1000);
                  }, 100);
                }
              }, 500);
            }
          }}
        >
          <MediaProvider />

          {/* Use Vidstack's default layout */}
          <DefaultVideoLayout
            icons={defaultLayoutIcons}
          />

          {/* Custom overlay for title, episode info, and dub/sub toggle */}
          <div
            className={`absolute top-0 left-0 right-0 z-[9999] p-3 flex justify-between items-start transition-opacity duration-300 ${
              isFullscreen && !showHeader ? 'opacity-0 pointer-events-none' : 'opacity-100'
            }`}
          >
            {/* Left side - title and episode info */}
            <div className="flex items-center">
              <div className="bg-black/60 backdrop-blur-sm text-white text-xs py-1.5 px-3 rounded-full shadow-lg border border-white/10">
                <div className="flex items-center gap-2">
                  <div className="max-w-[150px] overflow-hidden">
                    <ScrollingText
                      text={details?.title || ""}
                      className="font-medium"
                      maxWidth={150}
                      speed={30}
                    />
                  </div>
                  <span className="bg-white/20 h-4 w-[1px]"></span>
                  <span>EP {episodeNum}</span>
                  {isHls && <span className="ml-1 text-green-400 text-[10px] uppercase font-bold">HLS</span>}
                </div>
              </div>
            </div>

            {/* Right side - watermark and dub/sub toggle */}
            <div className="flex items-center gap-2">
              {/* Site watermark */}
              <div className="bg-black/60 backdrop-blur-sm px-3 py-1.5 rounded-full text-white/70 text-xs font-medium border border-white/10 shadow-lg hidden lg:block">
                <span className="hidden xl:inline">AnimeHQ Platform</span>
                <span className="xl:hidden">AHQ</span>
              </div>

              {/* Dub/Sub toggle button */}
              <button
                onClick={toggleDub}
                className="bg-black/60 backdrop-blur-sm hover:bg-black/80 px-3 py-1.5 rounded-full transition-all duration-300 text-white text-xs font-medium border border-white/10 shadow-lg flex items-center gap-1.5 relative z-[9999]"
                style={{ pointerEvents: 'auto' }}
              >
                <span className={`w-2 h-2 rounded-full ${isDub ? 'bg-yellow-400' : 'bg-blue-400'}`}></span>
                {isDub ? 'DUB' : 'SUB'}
              </button>
            </div>
          </div>

          {/* Skip intro/outro button */}
          {showSkipButton && (
            <div
              className={`absolute bottom-24 right-4 z-[9999] transition-opacity duration-300 ${
                isFullscreen && !showHeader ? 'opacity-0 pointer-events-none' : 'opacity-100'
              }`}
            >
              <button
                onClick={handleSkip}
                className="bg-white/90 hover:bg-white text-black font-medium text-sm px-4 py-2 rounded-md shadow-lg transition-all duration-200 flex items-center gap-2"
                style={{ pointerEvents: 'auto' }}
              >
                Skip {skipType === 'intro' ? 'Intro' : 'Outro'}
                <ArrowRight size={16} />
              </button>
            </div>
          )}
        </MediaPlayer>
      </div>

      {/* Episode navigation buttons - shown on the sides */}
      <div className="absolute inset-0 z-15 pointer-events-none flex items-center justify-between px-4">
        {/* Previous episode button - only show if not first episode */}
        {Number(episodeNum) > 1 && (
          <button
            onClick={() => {
              try {
                // Save current progress before navigating
                if (details?.id && playerRef.current) {
                  // Check if currentTime and duration properties exist
                  if (typeof playerRef.current.currentTime !== 'undefined' &&
                      typeof playerRef.current.duration !== 'undefined') {

                    const currentTime = playerRef.current.currentTime;
                    const duration = playerRef.current.duration;

                    if (currentTime > 0 && duration > 0) {
                      const progressPercent = Math.round((currentTime / duration) * 100);
                      if (progressPercent > 0) {
                        const animeIdStr = String(details.id);
                        const episodeNumStr = String(episodeNum);
                        console.log(`Navigating to previous episode. Saving progress: ${progressPercent}%`);
                        // Use the indicator function instead of direct updateProgress
                        saveProgressWithIndicator(animeIdStr, episodeNumStr, progressPercent);
                      }
                    }
                  }
                }

                // Navigate to previous episode
                const prevEp = Number(episodeNum) - 1;
                if (onEpisodeChange) {
                  // Use callback if provided (no page reload)
                  onEpisodeChange(prevEp);
                } else {
                  // Fallback to page reload method
                  window.location.href = `/watch/anime/${details?.id}?ep=${prevEp}`;
                }
              } catch (error) {
                console.warn('Error navigating to previous episode:', error);
                // Try to navigate anyway
                try {
                  const prevEp = Number(episodeNum) - 1;
                  window.location.href = `/watch/anime/${details?.id}?ep=${prevEp}`;
                } catch (navError) {
                  console.error('Failed to navigate to previous episode:', navError);
                }
              }
            }}
            className="bg-black/40 hover:bg-black/60 backdrop-blur-sm w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 pointer-events-auto opacity-0 hover:opacity-100 group"
          >
            <ArrowLeft size={20} className="text-white group-hover:scale-110 transition-transform" />
          </button>
        )}

        {/* Next episode button - only show if not last episode */}
        {episodeData && apiEpisodes && apiEpisodes.length > 0 && Number(episodeNum) < apiEpisodes.length && (
          <button
            onClick={() => {
              try {
                // Save current progress before navigating
                if (details?.id && playerRef.current) {
                  // Check if currentTime and duration properties exist
                  if (typeof playerRef.current.currentTime !== 'undefined' &&
                      typeof playerRef.current.duration !== 'undefined') {

                    const currentTime = playerRef.current.currentTime;
                    const duration = playerRef.current.duration;

                    if (currentTime > 0 && duration > 0) {
                      const progressPercent = Math.round((currentTime / duration) * 100);
                      if (progressPercent > 0) {
                        const animeIdStr = String(details.id);
                        const episodeNumStr = String(episodeNum);
                        console.log(`Navigating to next episode. Saving progress: ${progressPercent}%`);
                        // Use the indicator function instead of direct updateProgress
                        saveProgressWithIndicator(animeIdStr, episodeNumStr, progressPercent);
                      }
                    }
                  }
                }

                // Navigate to next episode
                const nextEp = Number(episodeNum) + 1;
                if (onEpisodeChange) {
                  // Use callback if provided (no page reload)
                  onEpisodeChange(nextEp);
                } else {
                  // Fallback to page reload method
                  window.location.href = `/watch/anime/${details?.id}?ep=${nextEp}`;
                }
              } catch (error) {
                console.warn('Error navigating to next episode:', error);
                // Try to navigate anyway
                try {
                  const nextEp = Number(episodeNum) + 1;
                  window.location.href = `/watch/anime/${details?.id}?ep=${nextEp}`;
                } catch (navError) {
                  console.error('Failed to navigate to next episode:', navError);
                }
              }
            }}
            className="bg-black/40 hover:bg-black/60 backdrop-blur-sm w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 pointer-events-auto opacity-0 hover:opacity-100 group"
          >
            <ArrowRight size={20} className="text-white group-hover:scale-110 transition-transform" />
          </button>
        )}
      </div>

      {/* Skip intro/outro button is now in the CustomVideoControls component */}

      {/* Progress save indicator */}
      {showSaveIndicator && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-[9999] bg-green-500/90 text-white text-xs font-medium px-3 py-1.5 rounded-full shadow-lg flex items-center gap-1.5 animate-fade-in-out">
          <div className="w-2 h-2 rounded-full bg-white animate-pulse"></div>
          Progress Saved: {currentProgressPercent}%
        </div>
      )}
    </div>
  );
};

export default VidstackPlayer;
