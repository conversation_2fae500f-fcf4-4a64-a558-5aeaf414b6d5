import { useState, useEffect, useMemo, memo } from 'react';
import { Carousel, CarouselContent, CarouselItem } from "@/components/ui/carousel";
import { Link } from "react-router-dom";
import { Play, Clock, Eye } from "lucide-react";
import Image from "@/components/ui/Image";
import { useWatchHistory } from "@/contexts/WatchHistoryContext";
import { getAnimeDetails } from "@/api/anilist";

// Card component for continue watching items
const ContinueWatchingCard = memo(({ anime, progress, episodeNumber }) => {
  // Format time since last watched
  const formatTimeSince = (timestamp) => {
    if (!timestamp) return '';

    const now = Date.now();
    const diff = now - timestamp;

    // Convert to appropriate time unit
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  const lastWatchedTime = anime.lastWatched ? formatTimeSince(anime.lastWatched) : '';

  return (
    <div className="flex size-full flex-col group cursor-pointer">
      {/* Card Container */}
      <div className="relative w-full h-full transition-all duration-500 group-hover:z-10">
        {/* Main Card */}
        <div className="relative w-full aspect-[16/9] rounded-xl overflow-hidden shadow-xl border border-white/10 group-hover:border-white/30 transition-all duration-300">
          {/* Background Image */}
          <div className="absolute inset-0 w-full h-full">
            <Image
              src={anime?.images?.coverLarge || anime?.images?.coverMedium || anime?.images?.coverSmall}
              quality="high"
              className="!object-cover !w-full !h-full transition-all duration-700 group-hover:scale-105 group-hover:brightness-110 filter brightness-[0.9]"
              loading="lazy"
            />
          </div>

          {/* Cinematic vignette overlay */}
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_transparent_0%,_rgba(0,0,0,0.6)_80%)] opacity-70 group-hover:opacity-50 transition-all duration-500"></div>

          {/* Bottom gradient for text readability */}
          <div className="absolute inset-0 bg-gradient-to-t from-black via-black/30 to-transparent opacity-90 group-hover:opacity-80 transition-all duration-500"></div>

          {/* Glassy overlay on hover */}
          <div className="absolute inset-0 bg-white/0 group-hover:bg-white/5 backdrop-blur-0 group-hover:backdrop-blur-sm transition-all duration-500"></div>

          {/* Content Container */}
          <Link
            to={anime?.id ? `/watch/anime/${anime?.id}?ep=${episodeNumber}` : ""}
            className="absolute inset-0 flex flex-col justify-between p-3 z-10"
          >
            {/* Top Section */}
            <div className="flex justify-between items-start">
              {/* Episode Badge */}
              <div className="bg-black/60 backdrop-blur-sm text-white text-xs py-1 px-3 rounded-md border border-white/20 shadow-lg transform translate-y-0 group-hover:translate-y-1 transition-all duration-300">
                Episode {episodeNumber}
              </div>

              {/* Last Watched Time */}
              {lastWatchedTime && (
                <div className="flex items-center gap-1.5 bg-black/60 backdrop-blur-sm text-white px-3 py-1 rounded-md text-xs shadow-lg transform translate-y-0 group-hover:translate-y-1 transition-all duration-300 border border-white/20">
                  <Clock size={10} className="text-white/80 group-hover:text-white transition-colors duration-300" />
                  <span>{lastWatchedTime}</span>
                </div>
              )}
            </div>

            {/* Bottom Section */}
            <div className="mt-auto transform translate-y-0 group-hover:translate-y-0 transition-all duration-300">
              {/* Title */}
              <h3 className="text-sm font-bold line-clamp-1 text-white mb-2 group-hover:text-white transition-colors duration-300 tracking-wide">
                {anime?.title}
              </h3>

              {/* Progress Bar */}
              <div className="w-full h-1 bg-black/50 overflow-hidden mb-2 rounded-full">
                <div
                  className="h-full bg-white/70 group-hover:bg-white transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>

              {/* Progress Text */}
              <div className="flex justify-between items-center text-[10px] text-white/70 group-hover:text-white/90 transition-colors duration-300">
                <span className="font-medium">{progress}% complete</span>
                {anime?.episodes && (
                  <span>{episodeNumber}/{anime.episodes}</span>
                )}
              </div>
            </div>

            {/* Play Button Overlay */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-16 h-16 rounded-full bg-black/0 group-hover:bg-black/40 backdrop-blur-0 group-hover:backdrop-blur-md flex items-center justify-center transition-all duration-500 transform scale-0 group-hover:scale-100 border border-white/0 group-hover:border-white/20">
                <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center transform group-hover:scale-100 transition-all duration-300 shadow-lg border border-white/40">
                  <Play fill="white" size={20} className="ml-1" />
                </div>
              </div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
});

// Main ContinueWatching component
const ContinueWatching = memo(() => {
  const { watchHistory } = useWatchHistory();
  const [animeDetails, setAnimeDetails] = useState({});
  const [loading, setLoading] = useState(true);

  // Extract and sort continue watching items
  const continueWatchingItems = useMemo(() => {
    if (!watchHistory || Object.keys(watchHistory).length === 0) {
      return [];
    }

    // Convert watchHistory object to array of items
    const items = Object.entries(watchHistory).map(([animeId, episodes]) => {
      // Find the most recently watched episode
      let lastWatchedEpisode = null;
      let lastWatchedTime = 0;
      let highestProgress = 0;

      Object.entries(episodes).forEach(([episodeNum, data]) => {
        if (data.lastWatched > lastWatchedTime) {
          lastWatchedEpisode = episodeNum;
          lastWatchedTime = data.lastWatched;
          highestProgress = data.progress;
        }
      });

      // Only include if we have a valid episode and it's not fully watched (progress < 98%)
      if (lastWatchedEpisode && highestProgress < 98) {
        return {
          animeId,
          episodeNumber: lastWatchedEpisode,
          progress: highestProgress,
          lastWatched: lastWatchedTime
        };
      }
      return null;
    }).filter(Boolean);

    // Sort by last watched time (most recent first)
    return items.sort((a, b) => b.lastWatched - a.lastWatched);
  }, [watchHistory]);

  // Fetch anime details for all items
  useEffect(() => {
    const fetchAnimeDetails = async () => {
      if (continueWatchingItems.length === 0) {
        setLoading(false);
        return;
      }

      setLoading(true);
      const details = { ...animeDetails };
      let hasNewData = false;

      // Fetch details for each anime that we don't already have
      for (const item of continueWatchingItems) {
        if (!details[item.animeId]) {
          try {
            const animeData = await getAnimeDetails(item.animeId);
            if (animeData) {
              details[item.animeId] = animeData;
              hasNewData = true;
            }
          } catch (error) {
            console.error(`Error fetching details for anime ${item.animeId}:`, error);
          }
        }
      }

      if (hasNewData) {
        setAnimeDetails(details);
      }
      setLoading(false);
    };

    fetchAnimeDetails();
  }, [continueWatchingItems, animeDetails]);

  // Don't render if there are no items to show
  if (continueWatchingItems.length === 0 || loading) {
    return null;
  }

  return (
    <div className="w-full flex flex-col gap-4">
      {/* Section Header with glassy black and white styling */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-3">
          <div className="bg-black/40 backdrop-blur-sm p-2.5 rounded-lg border border-white/10 shadow-md">
            <Play size={18} className="text-white/80" />
          </div>
          <div className="flex flex-col">
            <h2 className="text-xl lg:text-2xl font-semibold text-white">
              Continue Watching
            </h2>
            <p className="text-xs text-gray-400 hidden sm:block">
              Pick up where you left off
            </p>
          </div>
        </div>

        <Link
          to="/history"
          className="bg-black/40 backdrop-blur-sm hover:bg-black/60 text-white px-4 py-2 rounded-lg text-sm transition-all duration-300 flex items-center gap-2 border border-white/10 hover:border-white/30 shadow-md hover:shadow-lg"
        >
          <span>View All</span>
          <Eye size={14} className="text-white/70" />
        </Link>
      </div>

      {/* Carousel with glassy black and white styling */}
      <div className="w-full bg-black/20 backdrop-blur-sm p-4 rounded-xl border border-white/5 shadow-inner">
        <Carousel
          opts={{
            dragFree: true,
            dragThreshold: 50,
            align: "start"
          }}
          className="w-full"
        >
          <CarouselContent className="w-full">
            {continueWatchingItems.map((item) => {
              const anime = animeDetails[item.animeId];
              if (!anime) return null;

              return (
                <CarouselItem
                  key={`${item.animeId}-${item.episodeNumber}`}
                  className="basis-[85%] sm:basis-[45%] md:basis-[33.333%] lg:basis-[25%] xl:basis-[20%] p-[.35rem] sm:p-[.4rem] md:p-1.5"
                >
                  <ContinueWatchingCard
                    anime={anime}
                    progress={item.progress}
                    episodeNumber={item.episodeNumber}
                  />
                </CarouselItem>
              );
            })}
          </CarouselContent>
        </Carousel>

        {/* Subtle scroll indicator */}
        <div className="flex justify-center mt-4">
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <div className="w-8 h-0.5 bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-full"></div>
            <span>Scroll for more</span>
            <div className="w-8 h-0.5 bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-full"></div>
          </div>
        </div>
      </div>
    </div>
  );
});

export default ContinueWatching;
