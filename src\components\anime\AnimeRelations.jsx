import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Image from '../ui/Image';
import { ChevronDown, ChevronUp, Star, ArrowLeft, ArrowRight } from 'lucide-react';

// Map relation types to more user-friendly labels
const relationLabels = {
  'PREQUEL': 'Prequel',
  'SEQUEL': 'Sequel',
  'PARENT': 'Parent Story',
  'SIDE_STORY': 'Side Story',
  'CHARACTER': 'Shares Characters',
  'SUMMARY': 'Summary',
  'ALTERNATIVE': 'Alternative Version',
  'SPIN_OFF': 'Spin-off',
  'OTHER': 'Related',
  'SOURCE': 'Source Material',
  'ADAPTATION': 'Adaptation',
  'CONTAINS': 'Contains',
  'COMPILATION': 'Compilation',
};

// Map anime formats to more user-friendly labels
const formatLabels = {
  'TV': 'TV Series',
  'TV_SHORT': 'TV Short',
  'MOVIE': 'Movie',
  'SPECIAL': 'Special',
  'OVA': 'OVA',
  'ONA': 'ONA',
  'MUSIC': 'Music Video',
  'MANGA': 'Manga',
  'NOVEL': 'Novel',
  'ONE_SHOT': 'One Shot',
};

const AnimeRelations = ({ relations }) => {
  const [activeType, setActiveType] = useState(null);
  const carouselRef = useRef(null);

  if (!relations || relations.length === 0) return null;

  // Group relations by type
  const groupedRelations = relations.reduce((acc, relation) => {
    const type = relation.relationType;
    if (!acc[type]) {
      acc[type] = [];
    }
    acc[type].push(relation);
    return acc;
  }, {});

  // Order of relation types to display
  const relationOrder = [
    'PREQUEL', 'SEQUEL', 'PARENT', 'SIDE_STORY', 'ALTERNATIVE',
    'SPIN_OFF', 'ADAPTATION', 'SOURCE', 'SUMMARY', 'CHARACTER',
    'CONTAINS', 'COMPILATION', 'OTHER'
  ];

  // Sort the relation types according to the order
  const sortedRelationTypes = Object.keys(groupedRelations).sort(
    (a, b) => relationOrder.indexOf(a) - relationOrder.indexOf(b)
  );

  // Set the first type as active by default
  useEffect(() => {
    if (sortedRelationTypes.length > 0 && !activeType) {
      setActiveType(sortedRelationTypes[0]);
    }
  }, [sortedRelationTypes, activeType]);

  // Scroll handlers for carousel
  const scrollLeft = () => {
    if (carouselRef.current) {
      carouselRef.current.scrollBy({ left: -200, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (carouselRef.current) {
      carouselRef.current.scrollBy({ left: 200, behavior: 'smooth' });
    }
  };

  // Count total relations
  const totalRelations = relations.length;

  return (
    <div className="bg-black/30 backdrop-blur-sm rounded-lg p-4 border border-white/5">
      {/* Header with tabs */}
      <div className="flex flex-col gap-4 mb-4">
        <div className="flex justify-between items-center">
          <h2 className="text-lg font-bold uppercase tracking-wide">Related Anime</h2>
          <span className="text-xs text-white/60">{totalRelations} Total</span>
        </div>

        {/* Relation type tabs */}
        <div className="relative">
          <div className="flex items-center gap-2 mb-1">
            <button
              onClick={scrollLeft}
              className="w-6 h-6 bg-white/5 hover:bg-white/10 flex items-center justify-center rounded-full"
            >
              <ArrowLeft size={14} />
            </button>
            <div
              ref={carouselRef}
              className="flex overflow-x-auto hide-scrollbar gap-2 pb-1"
            >
              {sortedRelationTypes.map(type => (
                <button
                  key={type}
                  onClick={() => setActiveType(type)}
                  className={`px-3 py-1 text-xs whitespace-nowrap rounded-full transition-colors ${
                    activeType === type
                      ? 'bg-white text-black font-medium'
                      : 'bg-white/10 hover:bg-white/20 text-white/80'
                  }`}
                >
                  {relationLabels[type] || type}
                  <span className="ml-1 text-[10px] opacity-80">
                    ({groupedRelations[type].length})
                  </span>
                </button>
              ))}
            </div>
            <button
              onClick={scrollRight}
              className="w-6 h-6 bg-white/5 hover:bg-white/10 flex items-center justify-center rounded-full"
            >
              <ArrowRight size={14} />
            </button>
          </div>
        </div>
      </div>

      {/* Active relation type content */}
      {activeType && (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {groupedRelations[activeType].map(relation => (
            <Link
              key={relation.id}
              to={`/anime/${relation.id}`}
              className="group relative flex sm:block bg-black/40 hover:bg-black/50 rounded-lg overflow-hidden transition-all duration-300 border border-white/5 hover:border-white/20 sm:h-[140px]"
            >
              {/* Image - Poster on mobile, Banner on larger screens */}
              <div className="w-[80px] h-[110px] sm:hidden shrink-0 overflow-hidden">
                <Image
                  src={relation.images?.coverMedium}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
              </div>

              {/* Banner image for larger screens */}
              <div className="hidden sm:block w-full h-[140px] shrink-0 overflow-hidden absolute inset-0">
                <Image
                  src={relation.images?.bannerLarge || relation.images?.coverLarge}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent"></div>

                {/* Relation type badge for desktop - Top left */}
                <div className="absolute top-3 left-3 bg-black/70 backdrop-blur-sm px-2 py-1 rounded text-[10px] font-bold text-white uppercase tracking-wider">
                  {relationLabels[relation.relationType] || relation.relationType}
                </div>

                {/* Status badge - Top right */}
                {relation.status && (
                  <div className="absolute top-3 right-3 bg-black/70 backdrop-blur-sm px-2 py-1 rounded text-[10px] text-white/90">
                    {relation.status.replace(/_/g, ' ')}
                  </div>
                )}
              </div>

              {/* Content for mobile (next to poster) */}
              <div className="flex flex-col p-3 flex-grow min-w-0 sm:hidden">
                <h3 className="text-sm font-bold text-white group-hover:text-white/90 transition-colors line-clamp-2">
                  {relation.title}
                </h3>

                <div className="mt-auto pt-2">
                  <div className="flex flex-wrap gap-2 text-[10px]">
                    {relation.format && (
                      <span className="px-1.5 py-0.5 bg-black/40 rounded-sm text-white/70">
                        {formatLabels[relation.format] || relation.format}
                      </span>
                    )}

                    {relation.episodes > 0 && (
                      <span className="px-1.5 py-0.5 bg-black/40 rounded-sm text-white/70">
                        {relation.episodes} eps
                      </span>
                    )}

                    {relation.status && (
                      <span className="px-1.5 py-0.5 bg-black/40 rounded-sm text-white/70">
                        {relation.status.replace(/_/g, ' ')}
                      </span>
                    )}
                  </div>

                  {relation.rating && (
                    <div className="flex items-center gap-1 mt-1.5 text-[10px] text-white/70">
                      <Star fill="white" color="white" size={10} />
                      <span>{relation.rating}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Content for desktop (overlaid on banner) - Bottom area */}
              <div className="hidden sm:flex flex-col justify-end p-3 relative z-10 w-full h-full">
                <div className="mt-auto">
                  <h3 className="text-base font-bold text-white group-hover:text-white/90 transition-colors line-clamp-1 mb-1">
                    {relation.title}
                  </h3>

                  <div className="flex items-center gap-3">
                    {relation.format && (
                      <span className="text-xs text-white/80">
                        {formatLabels[relation.format] || relation.format}
                      </span>
                    )}

                    {relation.episodes > 0 && (
                      <span className="text-xs text-white/80">
                        {relation.episodes} eps
                      </span>
                    )}

                    {relation.rating && (
                      <div className="flex items-center gap-1 ml-auto text-xs text-white/80">
                        <Star fill="white" color="white" size={12} />
                        <span>{relation.rating}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Hover effect overlay */}
              <div className="absolute inset-0 bg-white/0 group-hover:bg-white/5 transition-colors pointer-events-none"></div>
            </Link>
          ))}
        </div>
      )}
    </div>
  );
};

export default AnimeRelations;
