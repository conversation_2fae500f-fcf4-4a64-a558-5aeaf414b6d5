import UserProfile from '../models/UserProfile.js';

// Helper functions for achievements and ranks
async function checkStreakAchievements(userProfile, streak) {
  // Define streak milestones
  const streakMilestones = [3, 7, 14, 30, 60, 100, 365];

  // Check if current streak hits a milestone
  const milestone = streakMilestones.find(m => streak === m);
  
  if (milestone) {
    // Calculate XP reward (higher for longer streaks)
    const xpReward = streak * 5;
    
    // Create achievement
    const newAchievement = {
      id: `streak-${streak}`,
      title: `${streak} Day Streak!`,
      description: `You've visited the site for ${streak} consecutive days!`,
      date: new Date(),
      type: 'streak'
    };
    
    // Create reward
    const newReward = {
      id: `streak-reward-${streak}`,
      title: `${streak} Day Streak Reward`,
      description: `+${xpReward} XP`,
      date: new Date(),
      value: xpReward,
      type: 'xp'
    };
    
    // Add achievement and reward if they don't already exist
    if (!userProfile.achievements.some(a => a.id === newAchievement.id)) {
      userProfile.achievements.push(newAchievement);
      userProfile.rewards.push(newReward);
      
      // Add XP
      userProfile.xp += xpReward;
      
      // Update level based on new XP
      updateLevel(userProfile);
      
      return true;
    }
  }
  
  return false;
}

async function checkLikeMilestones(userProfile, likes) {
  // Define like milestones
  const likeMilestones = [10, 50, 100, 500, 1000];
  
  // Check if current likes hits a milestone
  const milestone = likeMilestones.find(m => likes === m);
  
  if (milestone) {
    // Calculate XP reward
    const xpReward = milestone;
    
    // Create achievement
    const newAchievement = {
      id: `likes-${milestone}`,
      title: `${milestone} Likes!`,
      description: `Your profile has received ${milestone} likes!`,
      date: new Date(),
      type: 'likes'
    };
    
    // Create reward
    const newReward = {
      id: `likes-reward-${milestone}`,
      title: `${milestone} Likes Reward`,
      description: `+${xpReward} XP`,
      date: new Date(),
      value: xpReward,
      type: 'xp'
    };
    
    // Add achievement and reward if they don't already exist
    if (!userProfile.achievements.some(a => a.id === newAchievement.id)) {
      userProfile.achievements.push(newAchievement);
      userProfile.rewards.push(newReward);
      
      // Add XP
      userProfile.xp += xpReward;
      
      // Update level based on new XP
      updateLevel(userProfile);
      
      return true;
    }
  }
  
  return false;
}

function updateLevel(userProfile) {
  // Calculate level based on XP (more gradual progression)
  // Formula: level = (xp/100)^0.4 * 1.5
  const newLevel = Math.max(1, Math.floor(Math.pow(userProfile.xp / 100, 0.4) * 1.5));
  
  // Check if user leveled up
  if (newLevel > userProfile.level) {
    // Add level-up bonus XP
    const bonusXp = newLevel * 10;
    userProfile.xp += bonusXp;
    
    // Create level-up achievement
    const levelAchievement = {
      id: `level-${newLevel}`,
      title: `Reached Level ${newLevel}!`,
      description: `You've reached level ${newLevel} on your anime journey!`,
      date: new Date(),
      type: 'level'
    };
    
    const levelReward = {
      id: `level-reward-${newLevel}`,
      title: `Level ${newLevel} Reward`,
      description: `+${bonusXp} XP`,
      date: new Date(),
      value: bonusXp,
      type: 'xp'
    };
    
    // Add achievement and reward if they don't already exist
    if (!userProfile.achievements.some(a => a.id === levelAchievement.id)) {
      userProfile.achievements.push(levelAchievement);
      userProfile.rewards.push(levelReward);
    }
    
    // Update level
    userProfile.level = newLevel;
  }
}

// Controller methods
const userProfileController = {
  // Get user profile
  getUserProfile: async (req, res) => {
    try {
      const { userId } = req.params;
      
      // Find user profile or create if it doesn't exist
      let userProfile = await UserProfile.findOne({ anilistId: userId });
      
      if (!userProfile) {
        userProfile = new UserProfile({
          anilistId: userId
        });
        await userProfile.save();
      }
      
      res.status(200).json(userProfile);
    } catch (error) {
      console.error('Error getting user profile:', error);
      res.status(500).json({ message: error.message });
    }
  },
  
  // Update user profile
  updateUserProfile: async (req, res) => {
    try {
      const { userId } = req.params;
      const updateData = req.body;
      
      // Find user profile
      let userProfile = await UserProfile.findOne({ anilistId: userId });
      
      if (!userProfile) {
        userProfile = new UserProfile({
          anilistId: userId,
          ...updateData
        });
      } else {
        // Update fields
        Object.keys(updateData).forEach(key => {
          userProfile[key] = updateData[key];
        });
      }
      
      await userProfile.save();
      
      res.status(200).json(userProfile);
    } catch (error) {
      console.error('Error updating user profile:', error);
      res.status(500).json({ message: error.message });
    }
  },
  
  // Record a visit and update streak
  recordVisit: async (req, res) => {
    try {
      const { userId } = req.params;
      
      // Find user profile
      let userProfile = await UserProfile.findOne({ anilistId: userId });
      
      if (!userProfile) {
        userProfile = new UserProfile({
          anilistId: userId
        });
      }
      
      // Get today's date (reset to midnight)
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      // Get yesterday's date
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      
      // Check if we already recorded a visit today
      const lastVisitDate = userProfile.lastVisit ? new Date(userProfile.lastVisit) : null;
      if (lastVisitDate && lastVisitDate.getTime() === today.getTime()) {
        return res.status(200).json({
          message: 'Already recorded a visit today',
          streak: userProfile.streak,
          longestStreak: userProfile.longestStreak
        });
      }
      
      // Initialize visit dates array if it doesn't exist
      if (!userProfile.visitDates) {
        userProfile.visitDates = [];
      }
      
      // Add today to visit dates if not already there
      if (!userProfile.visitDates.some(date => new Date(date).getTime() === today.getTime())) {
        userProfile.visitDates.push(today);
      }
      
      // Update streak logic
      let newStreak = userProfile.streak || 0;
      
      if (lastVisitDate && lastVisitDate.getTime() === yesterday.getTime()) {
        // Consecutive day, increment streak
        newStreak += 1;
      } else if (!lastVisitDate || lastVisitDate.getTime() !== today.getTime()) {
        // Not consecutive and not today already, reset streak
        newStreak = 1;
      }
      
      // For demonstration purposes, let's set a minimum streak of 1
      newStreak = Math.max(1, newStreak);
      
      // Update longest streak if current streak is longer
      const newLongestStreak = Math.max(userProfile.longestStreak || 0, newStreak);
      
      // Save updates
      userProfile.streak = newStreak;
      userProfile.longestStreak = newLongestStreak;
      userProfile.lastVisit = today;
      
      // Add XP for daily visit (10 XP)
      userProfile.xp = (userProfile.xp || 0) + 10;
      
      // Update level based on new XP
      updateLevel(userProfile);
      
      // Check for streak achievements
      await checkStreakAchievements(userProfile, newStreak);
      
      await userProfile.save();
      
      res.status(200).json({
        message: 'Visit recorded successfully',
        streak: userProfile.streak,
        longestStreak: userProfile.longestStreak,
        xp: userProfile.xp,
        level: userProfile.level
      });
    } catch (error) {
      console.error('Error recording visit:', error);
      res.status(500).json({ message: error.message });
    }
  },
  
  // Add XP
  addXp: async (req, res) => {
    try {
      const { userId } = req.params;
      const { amount, source } = req.body;
      
      if (!amount || isNaN(amount)) {
        return res.status(400).json({ message: 'Invalid XP amount' });
      }
      
      // Find user profile
      let userProfile = await UserProfile.findOne({ anilistId: userId });
      
      if (!userProfile) {
        userProfile = new UserProfile({
          anilistId: userId
        });
      }
      
      // Add XP
      userProfile.xp = (userProfile.xp || 0) + parseInt(amount);
      
      // Update level based on new XP
      updateLevel(userProfile);
      
      await userProfile.save();
      
      res.status(200).json({
        message: 'XP added successfully',
        xp: userProfile.xp,
        level: userProfile.level
      });
    } catch (error) {
      console.error('Error adding XP:', error);
      res.status(500).json({ message: error.message });
    }
  },
  
  // Add like
  addLike: async (req, res) => {
    try {
      const { userId } = req.params;
      const { likerUserId } = req.body;
      
      // Find user profile
      let userProfile = await UserProfile.findOne({ anilistId: userId });
      
      if (!userProfile) {
        userProfile = new UserProfile({
          anilistId: userId
        });
      }
      
      // Initialize likedBy array if it doesn't exist
      if (!userProfile.likedBy) {
        userProfile.likedBy = [];
      }
      
      // Check if this user has already liked the profile
      if (likerUserId && userProfile.likedBy.includes(likerUserId)) {
        return res.status(400).json({ message: 'User has already liked this profile' });
      }
      
      // Add liker to likedBy array if provided
      if (likerUserId) {
        userProfile.likedBy.push(likerUserId);
      }
      
      // Increment likes
      userProfile.likes = (userProfile.likes || 0) + 1;
      
      // Add XP for receiving a like (5 XP)
      userProfile.xp = (userProfile.xp || 0) + 5;
      
      // Update level based on new XP
      updateLevel(userProfile);
      
      // Check for like milestones
      await checkLikeMilestones(userProfile, userProfile.likes);
      
      await userProfile.save();
      
      // If liker is provided, update their profile too
      if (likerUserId) {
        let likerProfile = await UserProfile.findOne({ anilistId: likerUserId });
        
        if (likerProfile) {
          // Initialize likedProfiles array if it doesn't exist
          if (!likerProfile.likedProfiles) {
            likerProfile.likedProfiles = [];
          }
          
          // Add liked profile to likedProfiles array
          if (!likerProfile.likedProfiles.includes(userId)) {
            likerProfile.likedProfiles.push(userId);
            await likerProfile.save();
          }
        }
      }
      
      res.status(200).json({
        message: 'Like added successfully',
        likes: userProfile.likes
      });
    } catch (error) {
      console.error('Error adding like:', error);
      res.status(500).json({ message: error.message });
    }
  },
  
  // Get daily tasks
  getDailyTasks: async (req, res) => {
    try {
      const { userId } = req.params;
      
      // Find user profile
      let userProfile = await UserProfile.findOne({ anilistId: userId });
      
      if (!userProfile) {
        userProfile = new UserProfile({
          anilistId: userId
        });
        await userProfile.save();
      }
      
      // Check if tasks need to be reset (new day)
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (!userProfile.tasks || !userProfile.tasks.lastReset || new Date(userProfile.tasks.lastReset).getTime() < today.getTime()) {
        // Reset tasks for new day
        userProfile.tasks = {
          completedTasks: [],
          lastReset: today
        };
        await userProfile.save();
      }
      
      res.status(200).json({
        completedTasks: userProfile.tasks.completedTasks || [],
        lastReset: userProfile.tasks.lastReset
      });
    } catch (error) {
      console.error('Error getting daily tasks:', error);
      res.status(500).json({ message: error.message });
    }
  },
  
  // Complete a task
  completeTask: async (req, res) => {
    try {
      const { userId, taskId } = req.params;
      
      // Find user profile
      let userProfile = await UserProfile.findOne({ anilistId: userId });
      
      if (!userProfile) {
        userProfile = new UserProfile({
          anilistId: userId
        });
      }
      
      // Initialize tasks if it doesn't exist
      if (!userProfile.tasks) {
        userProfile.tasks = {
          completedTasks: [],
          lastReset: new Date()
        };
      }
      
      // Check if task is already completed
      if (userProfile.tasks.completedTasks && userProfile.tasks.completedTasks.includes(taskId)) {
        return res.status(400).json({ message: 'Task already completed' });
      }
      
      // Define task rewards
      const taskRewards = {
        'visit': 10,
        'watch-episode': 15,
        'add-anime': 20,
        'rate-anime': 25,
        'update-progress': 15,
        'explore-genres': 20,
        'watch-seasonal': 30,
        'complete-all': 50
      };
      
      // Get XP reward for this task
      const xpReward = taskRewards[taskId] || 10;
      
      // Add task to completed tasks
      if (!userProfile.tasks.completedTasks) {
        userProfile.tasks.completedTasks = [];
      }
      userProfile.tasks.completedTasks.push(taskId);
      
      // Add XP for completing task
      userProfile.xp = (userProfile.xp || 0) + xpReward;
      
      // Update level based on new XP
      updateLevel(userProfile);
      
      await userProfile.save();
      
      res.status(200).json({
        message: 'Task completed successfully',
        completedTasks: userProfile.tasks.completedTasks,
        xpEarned: xpReward,
        xp: userProfile.xp,
        level: userProfile.level
      });
    } catch (error) {
      console.error('Error completing task:', error);
      res.status(500).json({ message: error.message });
    }
  },
  
  // Unlock achievement
  unlockAchievement: async (req, res) => {
    try {
      const { userId } = req.params;
      const { achievementId } = req.body;
      
      if (!achievementId) {
        return res.status(400).json({ message: 'Achievement ID is required' });
      }
      
      // Find user profile
      let userProfile = await UserProfile.findOne({ anilistId: userId });
      
      if (!userProfile) {
        userProfile = new UserProfile({
          anilistId: userId
        });
      }
      
      // Check if achievement is already unlocked
      if (userProfile.achievements && userProfile.achievements.some(a => a.id === achievementId)) {
        return res.status(400).json({ message: 'Achievement already unlocked' });
      }
      
      // Create achievement object
      const achievement = {
        id: achievementId,
        title: achievementId, // This should be replaced with a proper title
        description: 'Achievement unlocked', // This should be replaced with a proper description
        date: new Date(),
        type: 'custom'
      };
      
      // Add achievement
      if (!userProfile.achievements) {
        userProfile.achievements = [];
      }
      userProfile.achievements.push(achievement);
      
      await userProfile.save();
      
      res.status(200).json({
        message: 'Achievement unlocked successfully',
        achievement
      });
    } catch (error) {
      console.error('Error unlocking achievement:', error);
      res.status(500).json({ message: error.message });
    }
  }
};

export default userProfileController;
