import axios from 'axios';

/**
 * Middleware to verify Ani<PERSON>ist token
 */
export const verifyAniListToken = async (req, res, next) => {
  // Skip token verification in development mode
  if (process.env.NODE_ENV === 'development') {
    console.log('Development mode: Skipping token verification');

    // Extract user ID from URL parameter
    const userId = req.params.userId;
    if (userId) {
      req.user = { id: userId };
      return next();
    }
  }

  // Get token from Authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'Authorization token required' });
  }

  const token = authHeader.split(' ')[1];

  try {
    // Verify token with AniList by making a simple query
    const response = await axios.post('https://graphql.anilist.co', {
      query: `{ Viewer { id name avatar { medium } } }`
    }, {
      headers: { 'Authorization': `<PERSON><PERSON> ${token}` }
    });

    if (response.data.data.Viewer) {
      // Attach user data to request
      req.user = response.data.data.Viewer;
      next();
    } else {
      res.status(401).json({ message: 'Invalid token' });
    }
  } catch (error) {
    console.error('Token verification error:', error.message);
    res.status(401).json({ message: 'Token verification failed' });
  }
};

/**
 * Middleware to check if the user is accessing their own data
 */
export const checkUserMatch = (req, res, next) => {
  // Skip user match check in development mode
  if (process.env.NODE_ENV === 'development') {
    console.log('Development mode: Skipping user match check');
    return next();
  }

  // Check if the user ID in the URL matches the authenticated user's ID
  if (req.user && req.user.id && req.params.userId && req.user.id.toString() === req.params.userId) {
    next();
  } else {
    res.status(403).json({ message: 'Unauthorized: You can only access your own data' });
  }
};
