/* Custom styles for tooltips */
.tooltip-portal {
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  overflow: visible;
  z-index: 9999;
  pointer-events: none;
}

.tooltip-portal > div {
  pointer-events: auto;
}

/* Add animation for tooltip */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.tooltip-portal > div {
  animation: fadeIn 0.2s ease-in-out;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}
