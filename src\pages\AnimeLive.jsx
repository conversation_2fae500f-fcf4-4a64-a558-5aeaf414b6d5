import { useEffect, useState, useRef } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import "@vidstack/react/player/styles/default/theme.css";
import "@vidstack/react/player/styles/default/layouts/audio.css";
import "@vidstack/react/player/styles/default/layouts/video.css";
import { MediaPlayer, MediaProvider, Poster, Track } from "@vidstack/react";
import { DefaultVideoLayout, defaultLayoutIcons } from "@vidstack/react/player/layouts/default";
import { Globe, Volume2, ArrowLeft, Share2, ExternalLink } from "lucide-react";

// Language options for the live stream
const LANGUAGE_OPTIONS = [
  { id: "japanese", label: "Japanese", icon: "🇯🇵", youtubeId: "RsvEf1kK5VI", note: "Uses proxy" },
  { id: "english", label: "English", icon: "🇬🇧", youtubeId: "RsvEf1kK5VI", note: "Uses proxy" },
  { id: "hindi", label: "Hindi", icon: "🇮🇳", youtubeId: "TnO79b6A6sg" },
];

const AnimeLive = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedLanguage, setSelectedLanguage] = useState(
    searchParams.get("lang") || "japanese"
  );
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const playerRef = useRef(null);
  const navigate = useNavigate();

  // Set page title
  useEffect(() => {
    document.title = "Anime Live Stream | AnimeHQ";
    return () => {
      document.title = "AnimeHQ"; // Reset title when component unmounts
    };
  }, []);

  // Reset loading state when language changes
  useEffect(() => {
    setIsLoading(true);
    setError(null);

    // Set a timeout to hide loading state if it takes too long
    const timeout = setTimeout(() => {
      setIsLoading(false);
    }, 10000); // 10 seconds timeout

    return () => clearTimeout(timeout);
  }, [selectedLanguage]);

  // Handle player events
  const handlePlayerReady = () => {
    if (playerRef.current) {
      const player = playerRef.current;

      // Listen for fullscreen change
      player.addEventListener('fullscreenchange', (event) => {
        setIsFullscreen(!!event.detail);
      });

      // Listen for loading events
      player.addEventListener('loadstart', () => {
        setIsLoading(true);
      });

      player.addEventListener('loadeddata', () => {
        setIsLoading(false);
      });

      // Listen for error events
      player.addEventListener('error', (event) => {
        console.error('Player error:', event);
        setError('Failed to load the live stream. Please try again or select a different language.');
      });
    }
  };

  // Initialize player when it's available
  useEffect(() => {
    const player = playerRef.current;
    if (player) {
      handlePlayerReady();
    }
  }, []);

  // Handle language change
  const handleLanguageChange = (langId) => {
    setSelectedLanguage(langId);
    setSearchParams({ lang: langId });
    setIsLoading(true);
    setError(null);
  };

  // Get current language option
  const currentLanguage = LANGUAGE_OPTIONS.find(
    (lang) => lang.id === selectedLanguage
  ) || LANGUAGE_OPTIONS[0];

  // Handle fullscreen detection
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(
        document.fullscreenElement !== null ||
        document.webkitFullscreenElement !== null
      );
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
    };
  }, []);

  // Handle back button click
  const handleBack = () => {
    navigate(-1);
  };

  // Handle share button click
  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: "Anime Live Stream",
          text: `Check out this anime live stream in ${currentLanguage.label}!`,
          url: window.location.href,
        });
      } else {
        // Fallback for browsers that don't support the Web Share API
        navigator.clipboard.writeText(window.location.href);
        alert("Link copied to clipboard!");
      }
    } catch (error) {
      console.error("Error sharing:", error);
    }
  };

  // Handle external link click
  const handleExternalLink = () => {
    // Open the YouTube video in a new tab
    window.open(`https://www.youtube.com/watch?v=${currentLanguage.youtubeId}`, "_blank");
  };

  return (
    <div className="relative min-h-screen bg-black">
      {/* Back button (only visible when not in fullscreen) */}
      {!isFullscreen && (
        <button
          onClick={handleBack}
          className="absolute top-4 left-4 z-10 bg-black/50 backdrop-blur-md p-2 rounded-full text-white hover:bg-black/70 transition-colors"
          aria-label="Go back"
        >
          <ArrowLeft size={24} />
        </button>
      )}

      {/* Video Player */}
      <div className="w-full h-screen flex items-center justify-center bg-black">
        <div className="w-full max-w-screen-2xl mx-auto">
          <MediaPlayer
            className="w-full aspect-video max-h-screen"
            title={`Anime Live Stream (${currentLanguage.label})`}
            src={`youtube/${currentLanguage.youtubeId}`}
            viewType="video"
            streamType="live"
            logLevel="warn"
            crossOrigin
            playsInline
            autoPlay
            ref={playerRef}
            onLoadedMetadata={() => setIsLoading(false)}
          >
            <MediaProvider>
              <Poster className="vds-poster" src="https://i.imgur.com/8dEqXZC.jpg" alt="Anime Live Stream Thumbnail" />
            </MediaProvider>
            <DefaultVideoLayout icons={defaultLayoutIcons} />
          </MediaPlayer>
        </div>
      </div>

      {/* Language selector (only visible when not in fullscreen) */}
      {!isFullscreen && (
        <div className="absolute bottom-24 left-1/2 transform -translate-x-1/2 z-10 bg-black/70 backdrop-blur-md rounded-xl px-3 py-2 border border-white/20">
          <div className="flex flex-col items-center gap-2">
            <div className="flex items-center gap-1">
              <Globe size={16} className="text-white/70 mr-1" />
              <span className="text-white/80 text-sm">Select Language:</span>
            </div>
            <div className="flex items-center gap-1">
              {LANGUAGE_OPTIONS.map((lang) => (
                <button
                  key={lang.id}
                  onClick={() => handleLanguageChange(lang.id)}
                  className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors relative ${
                    selectedLanguage === lang.id
                      ? "bg-white/20 text-white"
                      : "text-white/70 hover:text-white hover:bg-white/10"
                  }`}
                  aria-label={`Switch to ${lang.label}`}
                >
                  <span className="flex items-center gap-1.5">
                    <span>{lang.icon}</span>
                    <span>{lang.label}</span>
                  </span>
                  {lang.note && selectedLanguage === lang.id && (
                    <span className="absolute -top-2 -right-2 bg-white/20 text-white text-[10px] px-1.5 py-0.5 rounded-full">
                      {lang.note}
                    </span>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Action buttons (only visible when not in fullscreen) */}
      {!isFullscreen && (
        <div className="absolute bottom-4 right-4 z-10 flex gap-2">
          <button
            onClick={handleShare}
            className="bg-black/50 backdrop-blur-md p-2 rounded-full text-white hover:bg-black/70 transition-colors"
            aria-label="Share"
          >
            <Share2 size={20} />
          </button>
          <button
            onClick={handleExternalLink}
            className="bg-black/50 backdrop-blur-md p-2 rounded-full text-white hover:bg-black/70 transition-colors"
            aria-label="Open in YouTube"
          >
            <ExternalLink size={20} />
          </button>
        </div>
      )}

      {/* Loading overlay */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/80 z-20">
          <div className="flex flex-col items-center">
            <div className="w-16 h-16 border-4 border-white/20 border-t-white rounded-full animate-spin mb-4"></div>
            <p className="text-white/80 text-lg">Loading live stream...</p>
          </div>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/80 z-20">
          <div className="bg-black/80 backdrop-blur-md border border-white/10 rounded-xl p-6 max-w-md text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-white/10 flex items-center justify-center">
              <Volume2 size={32} className="text-white/70" />
            </div>
            <h3 className="text-xl font-bold mb-2 text-white">Stream Error</h3>
            <p className="text-white/70 mb-4">{error}</p>
            <div className="flex flex-wrap gap-2 justify-center">
              {LANGUAGE_OPTIONS.map((lang) => (
                <button
                  key={lang.id}
                  onClick={() => handleLanguageChange(lang.id)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    selectedLanguage === lang.id
                      ? "bg-white/20 text-white"
                      : "bg-white/10 text-white/70 hover:text-white hover:bg-white/15"
                  }`}
                >
                  <span className="flex items-center gap-1.5">
                    <span>{lang.icon}</span>
                    <span>{lang.label}</span>
                  </span>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnimeLive;
