import { Filter, Loader2, <PERSON>, Star, X } from "lucide-react";
import { useMainContext } from "@/hooks/useContexts";
import { useMemo, useRef, useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import useFetch from "@/hooks/useFetch";
import { searchAnime } from "@/api/anilist";
import Image from "../ui/Image";
import { Dialog, DialogContent, DialogTrigger } from "../ui/dialog";

const SearchSnippet = ({ closeSidebar, closeDialog }) => {
  const n = useNavigate();
  const input_ref = useRef();
  const dialogTriggerRef = useRef();
  const [open, setOpen] = useState(false);
  const [query, setQuery] = useState("");

  // Add keyboard shortcut to open search dialog
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Check if the key is "/" and not in an input or textarea
      if (
        e.key === "/" &&
        document.activeElement.tagName !== "INPUT" &&
        document.activeElement.tagName !== "TEXTAREA"
      ) {
        e.preventDefault();
        if (dialogTriggerRef.current) {
          dialogTriggerRef.current.click();
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, []);
  const { data, isLoading } = useFetch({
    key: ["anime-search", query],
    fun: async () => {
      return await searchAnime(query);
    },
    enabled: query?.length ? true : false,
  });
  const { results } = useMemo(() => {
    return { results: data?.results || [] };
  }, [data]);
  return (
    <div className="relative group flex items-center">
      <Dialog
        open={open}
        modal={true}
        onOpenChange={(e) => {
          setOpen(e);
          if (e) {
            const t = setTimeout(() => {
              input_ref?.current?.focus();
            }, 300);
            return () => clearTimeout(t);
          }
        }}
      >
        <DialogTrigger ref={dialogTriggerRef}>
          <div className="flex items-center justify-between px-3 py-1.5 bg-black/60 hover:bg-black/80 rounded-md border border-white/30 hover:border-white/50 relative overflow-hidden w-full transition-all duration-200 shadow-sm">
            <div className="flex items-center gap-2">
              <span className="text-white/70">
                <Search size={14} />
              </span>
              <span className="text-white/70 text-xs">Search Shows...</span>
            </div>
            <span className="text-[10px] bg-black/80 text-white/70 px-1 py-0.5 rounded border border-white/30">/</span>
          </div>
        </DialogTrigger>
        <DialogContent className="p-0 border-white/10 bg-transparent">
          <div className="flex gap-3 flex-col w-full bg-gradient-to-b from-gray-900/95 to-black/95 backdrop-blur-md p-4 rounded-xl overflow-hidden max-h-[80vh] border border-white/10 shadow-xl">
            <div className="flex bg-white/10 w-full rounded-lg overflow-hidden shrink-0">
              <div className="flex relative w-full items-center">
                <span className="top-1/2 -translate-y-1/2 absolute left-3 pointer-events-none text-blue-400">
                  <Search size={18} />
                </span>
                <input
                  type="text"
                  ref={input_ref}
                  value={query}
                  onChange={(e) => setQuery(e?.target?.value)}
                  className="bg-transparent pr-4 pl-10 py-3 text-sm w-full focus:outline-none"
                  placeholder="Search anime..."
                  autoFocus
                />
                {query?.length ? (
                  <button
                    onClick={() => setQuery("")}
                    className="flex shrink-0 mr-3 text-gray-400 hover:text-white transition-colors"
                  >
                    <X size={18} />
                  </button>
                ) : (
                  <button
                    onClick={() => {
                      n(`/explore`);
                      setOpen(false);
                      if (closeSidebar) closeSidebar();
                      if (closeDialog) closeDialog();
                    }}
                    title="Advanced Search"
                    className="p-2 mr-1 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
                  >
                    <Filter size={18} />
                  </button>
                )}
              </div>
            </div>

            <div className="flex flex-col gap-2 min-h-32 max-h-[30rem] overflow-auto w-full">
              {isLoading ? (
                <div className="flex items-center justify-center py-10">
                  <div className="flex flex-col items-center gap-3">
                    <Loader2 className="animate-spin text-blue-400" size={24} />
                    <span className="text-sm text-gray-400">Searching anime...</span>
                  </div>
                </div>
              ) : results?.length ? (
                <div className="grid grid-cols-1 gap-2">
                  {results?.map((r) => (
                    <button
                      key={r?.id}
                      onClick={() => {
                        n(`/watch/anime/${r?.id}`);
                        setOpen(false);
                        if (closeSidebar) closeSidebar();
                        if (closeDialog) closeDialog();
                      }}
                      className="flex w-full h-20 shrink-0 bg-white/5 hover:bg-white/10 rounded-lg overflow-hidden gap-3 transition-colors"
                    >
                      <div className="flex h-full aspect-video bg-black/30 overflow-hidden shrink-0 relative">
                        <Image src={r?.images?.coverSmall} className="object-cover" />
                        <div className="absolute inset-0 bg-gradient-to-r from-black/50 to-transparent"></div>
                      </div>
                      <div className="flex h-full flex-col py-2 pr-2 justify-center flex-1 overflow-hidden">
                        <div className="text-sm font-medium !line-clamp-1 tracking-wide">
                          {r?.title}
                        </div>
                        <div className="flex mt-1 text-xs text-gray-400 gap-3 items-center overflow-hidden">
                          {Number(r?.rating) > 0 && (
                            <span className="flex items-center gap-1 shrink-0">
                              <Star fill="gold" color="gold" size={12} />
                              <span>{Number(r?.rating)?.toFixed(1) || "n/a"}</span>
                            </span>
                          )}
                          {r?.type && (
                            <span className="flex items-center uppercase shrink-0">
                              {r?.type}
                            </span>
                          )}
                          {r?.release_date && (
                            <span className="flex items-center shrink-0">
                              {r?.release_date}
                            </span>
                          )}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              ) : (
                <div className="flex items-center justify-center py-10">
                  <div className="flex flex-col items-center gap-2 text-center">
                    <Search size={24} className="text-gray-500 mb-1" />
                    <span className="text-gray-300 font-medium">
                      {query?.length ? "No Results Found" : "Search for anime"}
                    </span>
                    <span className="text-xs text-gray-500 max-w-[250px]">
                      {query?.length
                        ? "Try a different search term or browse the explore page"
                        : "Type to search for your favorite anime"}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SearchSnippet;
