import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronRight } from 'lucide-react';

const SectionHeader = ({ 
  title, 
  icon: Icon, 
  viewAllLink, 
  description,
  iconColor = "text-black",
  iconBgColor = "bg-white"
}) => {
  return (
    <div className="flex flex-col gap-1">
      <div className="flex items-center gap-2">
        {Icon && (
          <div className={`${iconBgColor} p-1.5 rounded-md`}>
            <Icon size={16} className={iconColor} />
          </div>
        )}
        <h2 className="text-xl font-medium">{title}</h2>
        <div className="flex-grow"></div>
        {viewAllLink && (
          <Link
            to={viewAllLink}
            className="text-sm text-white hover:underline flex items-center gap-1"
          >
            View All <ChevronRight size={14} />
          </Link>
        )}
      </div>
      {description && (
        <p className="text-sm text-gray-400 ml-8">{description}</p>
      )}
    </div>
  );
};

export default SectionHeader;
