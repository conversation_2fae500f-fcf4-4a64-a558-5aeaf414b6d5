import { useNotifications } from '@/context/NotificationContext';
import { X, Check, Trash2, MessageSquare, Film, Bell } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { formatTimeAgo } from '@/utils/formatDate';
import Image from '@/components/ui/Image';
import { useEffect, useState } from 'react';

const NotificationPanel = ({ onClose }) => {
  const { notifications, markAsRead, markAllAsRead, clearAll } = useNotifications();
  const navigate = useNavigate();
  const [isMobile, setIsMobile] = useState(false);

  // Check if device is mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    // Initial check
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);

    // Cleanup
    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  const handleNotificationClick = (notification) => {
    markAsRead(notification.id);

    // Navigate based on notification type
    if (notification.type === 'episode') {
      navigate(`/watch/anime/${notification.data.animeId}?ep=${notification.data.episodeNumber}`);
    } else if (notification.type === 'comment') {
      navigate(`/watch/anime/${notification.data.animeId}?ep=${notification.data.episodeId}`);
    }

    onClose();
  };

  return (
    <div className="bg-black/80 backdrop-blur-xl border border-white/10 rounded-xl shadow-xl overflow-hidden relative z-[9999]">
      {/* Glass effect elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-10 -left-10 w-40 h-40 bg-white/5 rounded-full blur-xl opacity-30"></div>
        <div className="absolute -bottom-20 -right-20 w-60 h-60 bg-white/5 rounded-full blur-xl opacity-20"></div>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-white/10 relative">
        <h3 className="text-white font-medium text-sm flex items-center gap-1.5">
          <Bell size={14} className="text-blue-400" />
          Notifications
        </h3>
        <div className="flex items-center gap-1">
          <button
            onClick={markAllAsRead}
            className="p-1.5 rounded-md hover:bg-white/10 text-white/70 hover:text-white transition-colors"
            title="Mark all as read"
          >
            <Check size={12} />
          </button>
          <button
            onClick={clearAll}
            className="p-1.5 rounded-md hover:bg-white/10 text-white/70 hover:text-white transition-colors"
            title="Clear all notifications"
          >
            <Trash2 size={12} />
          </button>
          {isMobile && (
            <button
              onClick={onClose}
              className="p-1.5 rounded-md hover:bg-white/10 text-white/70 hover:text-white transition-colors"
              title="Close"
            >
              <X size={12} />
            </button>
          )}
        </div>
      </div>

      {/* Notification list */}
      <div className="max-h-[60vh] lg:max-h-[50vh] overflow-y-auto custom-scrollbar">
        {notifications.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 px-4 text-center">
            <div className="relative">
              <div className="absolute inset-0 bg-blue-500/10 blur-xl rounded-full"></div>
              <div className="relative bg-white/10 rounded-full p-3 mb-3 border border-white/10">
                <Bell size={22} className="text-blue-400" />
              </div>
            </div>
            <p className="text-white/80 text-xs font-medium">No notifications yet</p>
            <p className="text-white/50 text-[10px] mt-1 max-w-[200px]">
              You'll see notifications about new episodes and comment replies here
            </p>
          </div>
        ) : (
          <div className="divide-y divide-white/10">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                onClick={() => handleNotificationClick(notification)}
                className={`relative p-3 hover:bg-white/5 cursor-pointer transition-colors ${
                  notification.read ? 'opacity-70' : ''
                } ${!notification.read ? 'bg-white/5 backdrop-blur-sm' : ''}`}
              >
                {/* Glass effect for unread notifications */}
                {!notification.read && (
                  <div className="absolute left-0 top-0 h-full w-1 bg-blue-500/80"></div>
                )}

                <div className="flex items-start gap-2.5">
                  {/* Icon based on notification type */}
                  <div className={`flex-shrink-0 rounded-full p-1.5 ${
                    notification.type === 'episode'
                      ? 'bg-blue-500/20 text-blue-400'
                      : 'bg-green-500/20 text-green-400'
                  }`}>
                    {notification.type === 'episode' ? (
                      <Film size={14} />
                    ) : (
                      <MessageSquare size={14} />
                    )}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between gap-2">
                      <p className="text-xs font-medium text-white">
                        {notification.title}
                      </p>
                      <span className="text-[9px] text-white/50 whitespace-nowrap">
                        {formatTimeAgo(notification.timestamp, true)}
                      </span>
                    </div>

                    {/* Show small thumbnail for episode notifications */}
                    {notification.type === 'episode' && notification.data.image && (
                      <div className="mt-1.5 flex items-center gap-2">
                        <div className="rounded-md overflow-hidden h-8 w-8 flex-shrink-0 border border-white/10">
                          <Image
                            src={notification.data.image}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex-1">
                          <p className="text-[11px] text-white/70 line-clamp-2">
                            {notification.message}
                          </p>
                          <span className="text-[10px] text-blue-400/80 mt-0.5 block">
                            Episode {notification.data.episodeNumber}
                          </span>
                        </div>
                      </div>
                    )}

                    {/* For non-episode notifications, just show the message */}
                    {notification.type !== 'episode' && (
                      <p className="text-[11px] text-white/70 mt-0.5 line-clamp-2">
                        {notification.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationPanel;
