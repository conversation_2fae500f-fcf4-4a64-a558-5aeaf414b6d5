import { useState } from "react";
import { Link } from "react-router-dom";
import {
  Home,
  Calendar,
  Compass,
  Menu,
  X,
  Bookmark,
  Clock,
  Flame,
  Heart,
  Sword,
  Sparkles,
  Film,
  Shuffle
} from "lucide-react";

const MobileNavigation = () => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const closeMenu = () => {
    setIsOpen(false);
  };

  // Navigation items - simplified to match header
  const navItems = [
    { name: "Home", icon: <Home size={20} />, path: "/" },
    { name: "Shorts", icon: <Film size={20} />, path: "/shorts" },
    { name: "Explore", icon: <Compass size={20} />, path: "/explore" },
    { name: "Trending", icon: <Flame size={20} />, path: "/explore?sort=TRENDING_DESC" },
    { name: "Random", icon: <Shuffle size={20} />, path: "/random" },
    { name: "Schedule", icon: <Calendar size={20} />, path: "/schedule" },
    { name: "History", icon: <Clock size={20} />, path: "/history" },
    { name: "Watchlist", icon: <Bookmark size={20} />, path: "/watchlist" },
    { name: "Romance", icon: <Heart size={20} />, path: "/explore?genre=Romance" },
  ];

  return (
    <>
      {/* Floating button */}
      <button
        onClick={toggleMenu}
        className="fixed bottom-6 right-6 z-50 bg-white/20 text-white rounded-full p-3.5 shadow-xl lg:hidden backdrop-blur-md border border-white/30"
        aria-label={isOpen ? "Close navigation menu" : "Open navigation menu"}
      >
        {isOpen ? <X size={24} /> : <Menu size={24} />}
      </button>

      {/* Navigation menu */}
      <div
        className={`fixed inset-0 z-40 bg-black/60 backdrop-blur-sm transition-opacity duration-300 lg:hidden ${
          isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        }`}
        onClick={closeMenu}
      >
        <div
          className={`fixed bottom-20 right-6 z-50 bg-white/10 border border-white/20 backdrop-blur-xl rounded-xl shadow-xl p-4 transition-transform duration-300 ${
            isOpen ? "translate-y-0" : "translate-y-20 opacity-0"
          }`}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="grid grid-cols-3 gap-4 w-[280px]">
            {navItems.map((item) => (
              <Link
                key={item.name}
                to={item.path}
                className="flex flex-col items-center justify-center gap-1 p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-colors"
                onClick={closeMenu}
              >
                <div className="text-white">{item.icon}</div>
                <span className="text-xs text-white">{item.name}</span>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default MobileNavigation;
