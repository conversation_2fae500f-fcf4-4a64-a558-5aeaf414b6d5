import axios from "axios";
import { formatAnimeDetails } from "./format";

// Create an axios instance for AniList API
export const anilist = axios.create({
  baseURL: "https://graphql.anilist.co",
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }
});

// GraphQL query for trending anime
const TRENDING_ANIME_QUERY = `
  query {
    Page(page: 1, perPage: 20) {
      media(sort: TRENDING_DESC, type: ANIME) {
        id
        title {
          romaji
          english
          native
        }
        description
        coverImage {
          extraLarge
          large
          medium
        }
        bannerImage
        episodes
        status
        genres
        averageScore
        seasonYear
        isAdult
        startDate {
          year
          month
          day
        }
        trailer {
          id
          site
          thumbnail
        }
      }
    }
  }
`;

// GraphQL query for popular anime
const POPULAR_ANIME_QUERY = `
  query {
    Page(page: 1, perPage: 20) {
      media(sort: POPULARITY_DESC, type: ANIME) {
        id
        title {
          romaji
          english
          native
        }
        description
        coverImage {
          extraLarge
          large
          medium
        }
        bannerImage
        episodes
        status
        genres
        averageScore
        seasonYear
        isAdult
        startDate {
          year
          month
          day
        }
        trailer {
          id
          site
          thumbnail
        }
      }
    }
  }
`;

// GraphQL query for anime details
const ANIME_DETAILS_QUERY = `
  query ($id: Int) {
    Media(id: $id, type: ANIME) {
      id
      title {
        romaji
        english
        native
      }
      description
      coverImage {
        extraLarge
        large
        medium
      }
      bannerImage
      episodes
      status
      genres
      averageScore
      popularity
      seasonYear
      startDate {
        year
        month
        day
      }
      endDate {
        year
        month
        day
      }
      nextAiringEpisode {
        airingAt
        timeUntilAiring
        episode
      }
      studios {
        nodes {
          id
          name
        }
      }
      characters(sort: ROLE, perPage: 10) {
        nodes {
          id
          name {
            full
          }
          image {
            medium
          }
          gender
        }
      }
      trailer {
        id
        site
        thumbnail
      }
      recommendations(perPage: 10) {
        nodes {
          mediaRecommendation {
            id
            title {
              romaji
              english
            }
            coverImage {
              extraLarge
              large
              medium
            }
            averageScore
          }
        }
      }
      relations {
        edges {
          relationType
          node {
            id
            title {
              romaji
              english
            }
            type
            format
            status
            episodes
            coverImage {
              large
              medium
            }
            averageScore
          }
        }
      }
    }
  }
`;

// GraphQL query for searching anime
const SEARCH_ANIME_QUERY = `
  query ($search: String) {
    Page(page: 1, perPage: 20) {
      media(search: $search, type: ANIME) {
        id
        title {
          romaji
          english
          native
        }
        description
        coverImage {
          extraLarge
          large
          medium
        }
        bannerImage
        episodes
        status
        genres
        averageScore
        seasonYear
      }
    }
  }
`;

// GraphQL query for exploring anime with filters
const EXPLORE_ANIME_QUERY = `
  query ($page: Int, $perPage: Int, $sort: [MediaSort], $genre: String, $year: Int, $search: String) {
    Page(page: $page, perPage: $perPage) {
      pageInfo {
        total
        currentPage
        lastPage
        hasNextPage
        perPage
      }
      media(type: ANIME, sort: $sort, genre: $genre, seasonYear: $year, search: $search) {
        id
        title {
          romaji
          english
          native
        }
        description
        coverImage {
          extraLarge
          large
          medium
        }
        bannerImage
        episodes
        status
        genres
        averageScore
        seasonYear
        isAdult
        studios {
          nodes {
            id
            name
          }
        }
        startDate {
          year
          month
          day
        }
      }
    }
  }
`;

// Function to get trending anime
export const getTrendingAnime = async () => {
  try {
    const { data } = await anilist.post('', {
      query: TRENDING_ANIME_QUERY
    });
    return formatAnimeDetails(data?.data?.Page?.media || []);
  } catch (e) {
    console.log("trending anime", e?.message);
    return null;
  }
};

// Function to get popular anime
export const getPopularAnime = async () => {
  try {
    const { data } = await anilist.post('', {
      query: POPULAR_ANIME_QUERY
    });
    return formatAnimeDetails(data?.data?.Page?.media || []);
  } catch (e) {
    console.log("popular anime", e?.message);
    return null;
  }
};

// Function to get anime details by ID
export const getAnimeDetails = async (id) => {
  try {
    const { data } = await anilist.post('', {
      query: ANIME_DETAILS_QUERY,
      variables: { id: parseInt(id) }
    });
    return formatAnimeDetails(data?.data?.Media || null);
  } catch (e) {
    console.log("anime details", e?.message);
    return null;
  }
};

// Function to search anime
export const searchAnime = async (query) => {
  try {
    const { data } = await anilist.post('', {
      query: SEARCH_ANIME_QUERY,
      variables: { search: query }
    });
    return {
      results: formatAnimeDetails(data?.data?.Page?.media || [])
    };
  } catch (e) {
    console.log("search anime", e?.message);
    return { results: [] };
  }
};

// Function to explore anime with filters
export const exploreAnime = async (filters = {}) => {
  try {
    const {
      page = 1,
      perPage = 20,
      sort = "POPULARITY_DESC",
      genre,
      year,
      studio,
      query
    } = filters;

    const variables = {
      page,
      perPage,
      sort: [sort]
    };

    if (genre && genre !== "any") variables.genre = genre;
    if (year && year !== "any") variables.year = parseInt(year);
    if (query && query.trim()) variables.search = query.trim();

    console.log("Explore anime variables:", variables);

    const { data } = await anilist.post('', {
      query: EXPLORE_ANIME_QUERY,
      variables
    });

    // If studio filter is applied, filter the results client-side
    let results = data?.data?.Page?.media || [];

    if (studio && studio !== "any") {
      results = results.filter(anime =>
        anime.studios?.nodes?.some(s => s.id.toString() === studio)
      );
    }

    return {
      results: formatAnimeDetails(results),
      pageInfo: data?.data?.Page?.pageInfo || {}
    };
  } catch (e) {
    console.log("explore anime", e?.message);
    return {
      results: [],
      pageInfo: {
        total: 0,
        currentPage: 1,
        lastPage: 1,
        hasNextPage: false
      }
    };
  }
};

// GraphQL query for getting the latest episode count for specific anime
const ANIME_EPISODE_COUNT_QUERY = `
  query ($id: Int) {
    Media(id: $id, type: ANIME) {
      id
      episodes
      status
      nextAiringEpisode {
        episode
      }
    }
  }
`;

// Cache for episode counts to avoid excessive API calls
const episodeCountCache = {};

// Function to get the latest episode count for a specific anime
export const getLatestEpisodeCount = async (id) => {
  try {
    // Check if we have a cached value that's less than 24 hours old
    const cachedData = episodeCountCache[id];
    if (cachedData && (Date.now() - cachedData.timestamp) < 24 * 60 * 60 * 1000) {
      return cachedData.count;
    }

    // Special case for One Piece (ID: 21) - known to have issues with AniList API
    if (id === 21) {
      // Fetch from a more reliable source or use a hardcoded value that's updated regularly
      // For now, we'll use the current count of 1124, but this should be updated
      const count = 1124;

      // Cache the result
      episodeCountCache[id] = {
        count,
        timestamp: Date.now()
      };

      return count;
    }

    // For other anime, try to get the count from AniList
    const { data } = await anilist.post('', {
      query: ANIME_EPISODE_COUNT_QUERY,
      variables: { id: parseInt(id) }
    });

    const mediaData = data?.data?.Media;
    let episodeCount = mediaData?.episodes;

    // If the anime is currently airing and has a next episode, use that information
    if (mediaData?.status === "RELEASING" && mediaData?.nextAiringEpisode) {
      episodeCount = mediaData.nextAiringEpisode.episode - 1;
    }

    // Cache the result
    episodeCountCache[id] = {
      count: episodeCount,
      timestamp: Date.now()
    };

    return episodeCount;
  } catch (e) {
    console.log("get episode count", e?.message);
    return null;
  }
};

// GraphQL query for upcoming anime
const UPCOMING_ANIME_QUERY = `
  query {
    Page(page: 1, perPage: 20) {
      media(status: NOT_YET_RELEASED, type: ANIME, sort: POPULARITY_DESC) {
        id
        title {
          romaji
          english
          native
        }
        description
        coverImage {
          extraLarge
          large
          medium
        }
        bannerImage
        episodes
        status
        genres
        averageScore
        seasonYear
        isAdult
        startDate {
          year
          month
          day
        }
        trailer {
          id
          site
          thumbnail
        }
      }
    }
  }
`;

// GraphQL query for top rated anime
const TOP_RATED_ANIME_QUERY = `
  query {
    Page(page: 1, perPage: 20) {
      media(sort: SCORE_DESC, type: ANIME) {
        id
        title {
          romaji
          english
          native
        }
        description
        coverImage {
          extraLarge
          large
          medium
        }
        bannerImage
        episodes
        status
        genres
        averageScore
        seasonYear
        isAdult
        startDate {
          year
          month
          day
        }
        trailer {
          id
          site
          thumbnail
        }
      }
    }
  }
`;

// GraphQL query for action anime
const ACTION_ANIME_QUERY = `
  query {
    Page(page: 1, perPage: 20) {
      media(genre: "Action", type: ANIME, sort: POPULARITY_DESC) {
        id
        title {
          romaji
          english
          native
        }
        description
        coverImage {
          extraLarge
          large
          medium
        }
        bannerImage
        episodes
        status
        genres
        averageScore
        seasonYear
        isAdult
        startDate {
          year
          month
          day
        }
        trailer {
          id
          site
          thumbnail
        }
      }
    }
  }
`;

// GraphQL query for romance anime
const ROMANCE_ANIME_QUERY = `
  query {
    Page(page: 1, perPage: 20) {
      media(genre: "Romance", type: ANIME, sort: POPULARITY_DESC) {
        id
        title {
          romaji
          english
          native
        }
        description
        coverImage {
          extraLarge
          large
          medium
        }
        bannerImage
        episodes
        status
        genres
        averageScore
        seasonYear
        isAdult
        startDate {
          year
          month
          day
        }
        trailer {
          id
          site
          thumbnail
        }
      }
    }
  }
`;

// Function to get upcoming anime
export const getUpcomingAnime = async () => {
  try {
    const { data } = await anilist.post('', {
      query: UPCOMING_ANIME_QUERY
    });
    return formatAnimeDetails(data?.data?.Page?.media || []);
  } catch (e) {
    console.log("upcoming anime", e?.message);
    return null;
  }
};

// Function to get top rated anime
export const getTopRatedAnime = async () => {
  try {
    const { data } = await anilist.post('', {
      query: TOP_RATED_ANIME_QUERY
    });
    return formatAnimeDetails(data?.data?.Page?.media || []);
  } catch (e) {
    console.log("top rated anime", e?.message);
    return null;
  }
};

// Function to get action anime
export const getActionAnime = async () => {
  try {
    const { data } = await anilist.post('', {
      query: ACTION_ANIME_QUERY
    });
    return formatAnimeDetails(data?.data?.Page?.media || []);
  } catch (e) {
    console.log("action anime", e?.message);
    return null;
  }
};

// Function to get romance anime
export const getRomanceAnime = async () => {
  try {
    const { data } = await anilist.post('', {
      query: ROMANCE_ANIME_QUERY
    });
    return formatAnimeDetails(data?.data?.Page?.media || []);
  } catch (e) {
    console.log("romance anime", e?.message);
    return null;
  }
};

// Function to get anime data for home page
export const getAnimeHomeData = async () => {
  try {
    const [trending, popular, topRated, upcoming, action, romance] = await Promise.all([
      getTrendingAnime(),
      getPopularAnime(),
      getTopRatedAnime(),
      getUpcomingAnime(),
      getActionAnime(),
      getRomanceAnime()
    ]);

    return [
      { title: "Trending Now", data: trending },
      { title: "Popular Anime", data: popular },
      { title: "Top Rated", data: topRated },
      { title: "Coming Soon", data: upcoming },
      { title: "Action Anime", data: action },
      { title: "Romance Anime", data: romance }
    ];
  } catch (e) {
    console.log("anime home", e?.message);
    return [];
  }
};
