import { useState, useEffect, useMemo, memo } from 'react';
import { getRecentComments } from '@/api/comments';
import { Link } from 'react-router-dom';
import { MessageSquare, Loader2, AlertTriangle, MessageCircle } from 'lucide-react';
import { formatTimeAgo } from '@/utils/formatDate';

// Array of attractive colors for anime titles
const titleColors = [
  'text-blue-400',
  'text-purple-400',
  'text-pink-400',
  'text-rose-400',
  'text-indigo-400',
  'text-cyan-400',
  'text-teal-400',
  'text-emerald-400',
  'text-amber-400',
  'text-orange-400',
];

// Create a memoized CommentCard component to prevent unnecessary re-renders
const CommentCard = memo(({ comment, getAnimeColor }) => {
  return (
    <Link
      key={comment._id}
      to={`/watch/anime/${comment.animeId}?ep=${comment.episodeNumber}`}
      className="flex flex-col gap-2 p-3.5 bg-white/5 hover:bg-white/8 transition-all duration-300 rounded-xl border border-white/10 relative overflow-hidden group hover:scale-[1.02] hover:shadow-lg h-[140px] sm:h-[160px]"
      style={{
        backgroundImage: comment.animeBanner ? `url(${comment.animeBanner})` : 'none',
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}
    >
      {/* Simplified overlay - removed backdrop-blur for better performance */}
      <div className="absolute inset-0 bg-gradient-to-r from-black/85 to-black/75 group-hover:from-black/70 group-hover:to-black/60 transition-all duration-300"></div>

      {/* Content positioned above the overlay */}
      <div className="relative z-10 flex flex-col h-full">
        {/* Anime title and episode */}
        <div className="flex items-center gap-1.5 flex-wrap">
          <span className={`text-sm font-medium ${getAnimeColor(comment.animeId)} line-clamp-1 group-hover:drop-shadow-sm transition-all duration-300`}>{comment.animeTitle}</span>
          <div className="flex items-center gap-1">
            <span className="text-xs text-gray-400">•</span>
            <span className="text-xs text-gray-400">Ep. <span className={`${getAnimeColor(comment.animeId)} font-medium`}>{comment.episodeNumber}</span></span>
          </div>
        </div>

        {/* Comment content */}
        <div className="mt-2 flex-grow">
          {comment.hasSpoiler ? (
            <div className="group/spoiler cursor-pointer">
              <p className="text-sm text-gray-300 line-clamp-3 blur-[4px] group-hover/spoiler:blur-none transition-all duration-300">
                {comment.content}
              </p>
              <div className="absolute inset-0 flex items-center justify-center opacity-100 group-hover/spoiler:opacity-0 transition-opacity duration-300 pointer-events-none">
                <div className="bg-black/60 px-2 py-1 rounded-md text-xs border border-yellow-500/30 flex items-center gap-1.5">
                  <AlertTriangle size={12} className="text-yellow-500" />
                  <span className="text-yellow-500">Click to reveal spoiler</span>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-sm text-gray-300 line-clamp-3">{comment.content}</p>
          )}
        </div>

        {/* User info and metadata at the bottom */}
        <div className="flex items-center justify-between mt-auto pt-2 border-t border-white/10">
          {/* User info */}
          <div className="flex items-center gap-1.5">
            <img
              src={comment.userAvatar || 'https://i.imgur.com/6VBx3io.png'}
              alt={comment.userName}
              className="w-4 h-4 rounded-full object-cover border border-white/20"
            />
            <span className="text-xs font-medium truncate max-w-[80px]">{comment.userName}</span>
            <div className={`w-1.5 h-1.5 rounded-full ${getAnimeColor(comment.animeId).replace('text-', 'bg-')}`}></div>
            <span className="text-xs text-gray-400">{formatTimeAgo(comment.createdAt, true)}</span>
          </div>

          {/* Reply count */}
          <div className="flex items-center gap-1 text-xs text-gray-400">
            <MessageSquare size={12} />
            <span>{comment.replies?.length || 0}</span>
          </div>
        </div>
      </div>
    </Link>
  );
});

const RecentComments = memo(() => {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Function to get a consistent color for each anime based on its ID
  const getAnimeColor = (animeId) => {
    // Use the sum of character codes in the animeId to determine the color index
    const charSum = animeId.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);
    const colorIndex = charSum % titleColors.length;
    return titleColors[colorIndex];
  };

  useEffect(() => {
    const fetchRecentComments = async () => {
      try {
        setLoading(true);
        const data = await getRecentComments(6); // Get 6 recent comments
        setComments(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching recent comments:', err);
        setError('Failed to load recent comments');
      } finally {
        setLoading(false);
      }
    };

    fetchRecentComments();
  }, []);

  if (loading) {
    return (
      <div className="w-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-medium">Recent Discussions</h2>
        </div>
        <div className="flex justify-center items-center py-10 bg-white/5 rounded-xl">
          <div className="flex flex-col items-center">
            <Loader2 className="animate-spin mb-3" size={30} />
            <span className="text-gray-300">Loading recent comments...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-medium">Recent Discussions</h2>
        </div>
        <div className="flex justify-center items-center py-10 bg-white/5 rounded-xl">
          <div className="flex flex-col items-center text-center">
            <AlertTriangle className="mb-3 text-yellow-500" size={30} />
            <span className="text-gray-300">{error}</span>
            <button
              onClick={() => window.location.reload()}
              className="mt-3 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (comments.length === 0) {
    return (
      <div className="w-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-medium">Recent Discussions</h2>
        </div>
        <div className="flex justify-center items-center py-10 bg-white/5 rounded-xl border border-dashed border-white/10">
          <div className="flex flex-col items-center text-center">
            <MessageSquare size={40} className="mb-3 text-gray-500 opacity-50" />
            <p className="text-gray-300 font-medium">No comments yet</p>
            <p className="text-gray-400 text-sm mt-1">Be the first to start a discussion!</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-2">
          <div className="bg-black/40 p-2 rounded-md border border-white/10">
            <MessageCircle size={16} className="text-white" />
          </div>
          <h2 className="text-xl font-medium">Recent Discussions</h2>
        </div>
        <Link to="/explore" className="text-sm text-primary hover:text-primary/80 transition-colors">
          View All
        </Link>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {comments.map((comment) => (
          <CommentCard
            key={comment._id}
            comment={comment}
            getAnimeColor={getAnimeColor}
          />
        ))}
      </div>
    </div>
  );
});

export default RecentComments;
