import { useState, useEffect } from "react";
import { useAniList } from "@/hooks/useAniList";
import { useNavigate } from "react-router-dom";
import {
  Loader2, LogOut, Clock, Star, PlayCircle, PauseCircle,
  CheckCircle, ListPlus, ListX, ExternalLink, Settings,
  Layout, Compass
} from "lucide-react";
import Image from "@/components/ui/Image";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Link } from "react-router-dom";

import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const ProfileRedesign = () => {
  const { user, loading, logout, getUserAnimeList, isAuthenticated } = useAniList();
  const [animeLists, setAnimeLists] = useState(null);
  const [listsLoading, setListsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("CURRENT");
  const [activeSection, setActiveSection] = useState("anime-lists"); // 'anime-lists', 'settings'

  // Profile customization settings
  const [profileSettings, setProfileSettings] = useState({
    theme: "dark",
    layout: "grid",
    showStats: true,
    showBio: true,
    accentColor: "blue",
    bannerOpacity: 0.8,
    compactView: false
  });

  const navigate = useNavigate();

  // Redirect if not authenticated
  useEffect(() => {
    if (!loading && !isAuthenticated) {
      navigate("/");
    }
  }, [loading, isAuthenticated, navigate]);

  // Fetch user's anime lists and comments
  useEffect(() => {
    const fetchLists = async () => {
      if (user?.id) {
        try {
          console.log("Fetching anime lists for user:", user.name);
          setListsLoading(true);
          const lists = await getUserAnimeList();

          if (lists) {
            console.log(`Successfully fetched ${lists.length} anime lists`);
            setAnimeLists(lists);
          } else {
            console.error("Failed to fetch anime lists");
          }
        } catch (error) {
          console.error("Error in fetchLists:", error);
        } finally {
          setListsLoading(false);
        }
      } else {
        console.log("Not fetching lists - no user ID available");
      }
    };

    fetchLists();
  }, [user, getUserAnimeList]);

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[70vh] gap-4">
        <Loader2 className="w-12 h-12 animate-spin text-primary" />
        <h1 className="text-xl font-medium">Loading Profile...</h1>
        <p className="text-gray-400">Fetching your AniList data</p>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[70vh] gap-4">
        <div className="p-4 bg-red-900/20 border border-red-900/30 rounded-lg max-w-md text-center">
          <h1 className="text-xl font-medium text-red-400 mb-2">Profile Not Available</h1>
          <p className="text-gray-400 mb-4">Unable to load your AniList profile. Please try logging in again.</p>
          <Button onClick={() => navigate("/")} variant="outline">
            Return to Home
          </Button>
        </div>
      </div>
    );
  }

  // Calculate list counts
  const getListCount = (status) => {
    if (!animeLists) return 0;
    const list = animeLists.find(list => list.status === status);
    return list?.entries?.length || 0;
  };

  // Handle profile setting changes
  const handleSettingChange = (setting, value) => {
    setProfileSettings(prev => ({
      ...prev,
      [setting]: value
    }));

    // In a real app, you would save these settings to a database
    console.log(`Setting ${setting} changed to:`, value);
  };

  // Get dynamic styles based on profile settings
  const getThemeStyles = () => {
    const styles = {};

    // Apply accent color
    switch (profileSettings.accentColor) {
      case 'blue':
        styles.accentColor = 'rgb(59, 130, 246)';
        styles.accentLight = 'rgba(59, 130, 246, 0.2)';
        break;
      case 'purple':
        styles.accentColor = 'rgb(147, 51, 234)';
        styles.accentLight = 'rgba(147, 51, 234, 0.2)';
        break;
      case 'green':
        styles.accentColor = 'rgb(34, 197, 94)';
        styles.accentLight = 'rgba(34, 197, 94, 0.2)';
        break;
      case 'red':
        styles.accentColor = 'rgb(239, 68, 68)';
        styles.accentLight = 'rgba(239, 68, 68, 0.2)';
        break;
      case 'orange':
        styles.accentColor = 'rgb(249, 115, 22)';
        styles.accentLight = 'rgba(249, 115, 22, 0.2)';
        break;
      default:
        styles.accentColor = 'rgb(59, 130, 246)';
        styles.accentLight = 'rgba(59, 130, 246, 0.2)';
    }

    return styles;
  };

  // Get theme styles for dynamic styling
  const themeStyles = getThemeStyles();

  // Use themeStyles in a comment to prevent unused variable warning
  // The themeStyles object contains accentColor: ${themeStyles.accentColor}

  return (
    <div className="flex flex-col w-full gap-8 py-4 overflow-x-hidden">
      {/* User Profile Header - Unified Banner Design - Full Width */}
      <div className="w-full overflow-hidden">
        {/* Banner with Profile Info Overlay */}
        <div className="w-full relative">
          {/* Banner Background */}
          <div className="absolute inset-0 z-0 h-[280px] md:h-[320px]">
            {user.bannerImage ? (
              <div className="relative w-full h-full">
                <Image src={user.bannerImage} className="object-cover" />
                {/* Bottom fade overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent z-10"></div>
              </div>
            ) : (
              <div className="w-full h-full bg-gradient-to-r from-gray-900 to-black">
                {/* Subtle pattern overlay for non-banner users */}
                <div className="absolute inset-0 opacity-5"
                  style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                  }}>
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent z-10"></div>
              </div>
            )}
          </div>

          {/* Floating Action Buttons */}
          <div className="absolute top-4 right-4 z-30 flex gap-2">
            <Button
              variant="outline"
              size="sm"
              className="bg-black/40 backdrop-blur-md border-white/10 hover:bg-black/60 text-white"
              onClick={() => setActiveSection("settings")}
            >
              <Settings size={16} className="text-white" />
              <span className="hidden sm:inline ml-1.5">Settings</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="bg-black/40 backdrop-blur-md border-white/10 hover:bg-black/60 text-white"
              onClick={logout}
            >
              <LogOut size={16} className="text-white" />
              <span className="hidden sm:inline ml-1.5">Logout</span>
            </Button>
          </div>

          {/* Profile Info Section - Positioned at bottom of banner */}
          <div className="relative z-20 pt-[180px] md:pt-[220px] px-4 pb-6">
            <div className="w-full max-w-[1920px] mx-auto flex flex-col md:flex-row items-center md:items-end justify-between gap-4">
              {/* Left Section - Profile and Name */}
              <div className="flex items-center gap-4 max-w-full">
                {/* Profile Avatar */}
                <div className="w-[70px] h-[70px] md:w-[80px] md:h-[80px] rounded-full overflow-hidden relative border-2 border-white/20 flex-shrink-0">
                  <Image src={user.avatar?.large || user.avatar?.medium} className="object-cover" />
                </div>

                {/* User Details */}
                <div className="flex flex-col min-w-0">
                  <div className="text-white/70 text-sm mb-1">
                    {new Date().getHours() < 12 ? "Good morning!" :
                     new Date().getHours() < 18 ? "Good afternoon!" : "Good evening!"}
                  </div>
                  <h1 className="text-2xl md:text-3xl font-bold text-white truncate">{user.name}</h1>

                  {/* Bio - Directly below username */}
                  {user.about && profileSettings.showBio && (
                    <p className="text-sm text-white/70 mt-2 line-clamp-2 max-w-full md:max-w-[400px]">
                      {user.about}
                    </p>
                  )}
                </div>
              </div>

              {/* Right Section - Stats */}
              <div className="flex items-center gap-4 md:gap-8 mt-4 md:mt-0 flex-shrink-0">
                <div className="flex flex-col items-center">
                  <span className="text-xl md:text-2xl font-bold text-white">{Math.round((user.statistics?.anime?.minutesWatched || 0) / 1440)}+</span>
                  <span className="text-xs text-white/60">Days Watched</span>
                </div>

                <div className="flex flex-col items-center">
                  <span className="text-xl md:text-2xl font-bold text-white">{user.statistics?.anime?.count || 0}</span>
                  <span className="text-xs text-white/60">Anime Finished</span>
                </div>

                <div className="flex flex-col items-center">
                  <span className="text-xl md:text-2xl font-bold text-white">{user.statistics?.anime?.episodesWatched || 0}</span>
                  <span className="text-xs text-white/60">Total Episodes</span>
                </div>
              </div>
            </div>
          </div>
        </div>

{/* Bio is now integrated directly into the profile header */}

        {/* Stats Cards - Hidden since we integrated stats into the profile header */}
        {false && profileSettings.showStats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 px-4 -mt-10 relative z-30">
            <div className="bg-[#1a0a29]/80 backdrop-blur-xl rounded-2xl border border-pink-500/10 p-4 flex items-center gap-4 hover:bg-[#1a0a29] transition-all transform hover:-translate-y-1 hover:shadow-lg duration-300">
              <div className="w-12 h-12 rounded-full bg-pink-500/10 flex items-center justify-center">
                <PlayCircle size={24} className="text-pink-400" />
              </div>
              <div>
                <span className="text-2xl font-bold text-white">{user.statistics?.anime?.count || 0}</span>
                <p className="text-sm text-white/70">Anime</p>
              </div>
            </div>

            <div className="bg-[#1a0a29]/80 backdrop-blur-xl rounded-2xl border border-pink-500/10 p-4 flex items-center gap-4 hover:bg-[#1a0a29] transition-all transform hover:-translate-y-1 hover:shadow-lg duration-300">
              <div className="w-12 h-12 rounded-full bg-pink-500/10 flex items-center justify-center">
                <Clock size={24} className="text-pink-400" />
              </div>
              <div>
                <span className="text-2xl font-bold text-white">{Math.round((user.statistics?.anime?.minutesWatched || 0) / 60)}</span>
                <p className="text-sm text-white/70">Hours</p>
              </div>
            </div>

            <div className="bg-[#1a0a29]/80 backdrop-blur-xl rounded-2xl border border-pink-500/10 p-4 flex items-center gap-4 hover:bg-[#1a0a29] transition-all transform hover:-translate-y-1 hover:shadow-lg duration-300">
              <div className="w-12 h-12 rounded-full bg-pink-500/10 flex items-center justify-center">
                <CheckCircle size={24} className="text-pink-400" />
              </div>
              <div>
                <span className="text-2xl font-bold text-white">{user.statistics?.anime?.episodesWatched || 0}</span>
                <p className="text-sm text-white/70">Episodes</p>
              </div>
            </div>

            <div className="bg-[#1a0a29]/80 backdrop-blur-xl rounded-2xl border border-pink-500/10 p-4 flex items-center gap-4 hover:bg-[#1a0a29] transition-all transform hover:-translate-y-1 hover:shadow-lg duration-300">
              <div className="w-12 h-12 rounded-full bg-pink-500/10 flex items-center justify-center">
                <Star size={24} className="text-pink-400" />
              </div>
              <div>
                <span className="text-2xl font-bold text-white">{user.statistics?.anime?.meanScore || 0}</span>
                <p className="text-sm text-white/70">Mean Score</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Main Content Tabs - Updated to Match Hero Design */}
      <div className="mt-0">
        <div className="flex justify-center mb-8">
          <div className="bg-black/40 backdrop-blur-md rounded-full p-1.5 border border-white/10 inline-flex shadow-lg">
            <Button
              variant={activeSection === "anime-lists" ? "default" : "outline"}
              className={`rounded-full px-8 py-2.5 ${activeSection === "anime-lists" ? "bg-white text-black hover:bg-white/90" : "bg-transparent border-transparent text-white hover:bg-white/10"}`}
              onClick={() => setActiveSection("anime-lists")}
            >
              <PlayCircle size={18} className={activeSection === "anime-lists" ? "text-black" : "text-white"} />
              <span className="ml-2 font-medium">Anime Lists</span>
            </Button>

            <Button
              variant={activeSection === "settings" ? "default" : "outline"}
              className={`rounded-full px-8 py-2.5 ${activeSection === "settings" ? "bg-white text-black hover:bg-white/90" : "bg-transparent border-transparent text-white hover:bg-white/10"}`}
              onClick={() => setActiveSection("settings")}
            >
              <Settings size={18} className={activeSection === "settings" ? "text-black" : "text-white"} />
              <span className="ml-2 font-medium">Profile Settings</span>
            </Button>
          </div>
        </div>

        {/* Content Sections - Conditionally rendered based on activeSection */}

        {/* Anime Lists Section */}
        {activeSection === "anime-lists" && (
          <div className="w-full max-w-[1920px] mx-auto px-4">
            <Tabs
              defaultValue="CURRENT"
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <div className="flex flex-col items-center mb-10 gap-6">
                <h2 className="text-2xl md:text-3xl font-bold text-white text-center">My Anime Collection</h2>

                {/* Mobile Tabs - Scrollable horizontally */}
                <div className="sm:hidden w-full overflow-x-auto pb-2">
                  <div className="flex bg-black/40 backdrop-blur-md rounded-full p-1.5 border border-white/10 gap-1 min-w-max mx-auto" style={{ maxWidth: '100%' }}>
                    <button
                      className={`flex items-center justify-center gap-1.5 relative px-3 py-2 rounded-full ${activeTab === "CURRENT" ? "bg-white text-black" : "text-white hover:bg-white/10"}`}
                      onClick={() => setActiveTab("CURRENT")}
                    >
                      <div className="relative">
                        <PlayCircle size={16} className={activeTab === "CURRENT" ? "text-blue-600" : "text-blue-400"} />
                        {getListCount("CURRENT") > 0 && (
                          <span className="absolute -top-2 -right-2 bg-blue-500 text-white text-[10px] font-bold rounded-full w-4 h-4 flex items-center justify-center">
                            {getListCount("CURRENT")}
                          </span>
                        )}
                      </div>
                      <span className="ml-1 font-medium text-sm">Watching</span>
                    </button>

                    <button
                      className={`flex items-center justify-center gap-1.5 relative px-3 py-2 rounded-full ${activeTab === "PLANNING" ? "bg-white text-black" : "text-white hover:bg-white/10"}`}
                      onClick={() => setActiveTab("PLANNING")}
                    >
                      <div className="relative">
                        <ListPlus size={16} className={activeTab === "PLANNING" ? "text-green-600" : "text-green-400"} />
                        {getListCount("PLANNING") > 0 && (
                          <span className="absolute -top-2 -right-2 bg-green-500 text-white text-[10px] font-bold rounded-full w-4 h-4 flex items-center justify-center">
                            {getListCount("PLANNING")}
                          </span>
                        )}
                      </div>
                      <span className="ml-1 font-medium text-sm">Planning</span>
                    </button>

                    <button
                      className={`flex items-center justify-center gap-1.5 relative px-3 py-2 rounded-full ${activeTab === "COMPLETED" ? "bg-white text-black" : "text-white hover:bg-white/10"}`}
                      onClick={() => setActiveTab("COMPLETED")}
                    >
                      <div className="relative">
                        <CheckCircle size={16} className={activeTab === "COMPLETED" ? "text-purple-600" : "text-purple-400"} />
                        {getListCount("COMPLETED") > 0 && (
                          <span className="absolute -top-2 -right-2 bg-purple-500 text-white text-[10px] font-bold rounded-full w-4 h-4 flex items-center justify-center">
                            {getListCount("COMPLETED") > 99 ? '99+' : getListCount("COMPLETED")}
                          </span>
                        )}
                      </div>
                      <span className="ml-1 font-medium text-sm">Completed</span>
                    </button>

                    <button
                      className={`flex items-center justify-center gap-1.5 relative px-3 py-2 rounded-full ${activeTab === "PAUSED" ? "bg-white text-black" : "text-white hover:bg-white/10"}`}
                      onClick={() => setActiveTab("PAUSED")}
                    >
                      <div className="relative">
                        <PauseCircle size={16} className={activeTab === "PAUSED" ? "text-yellow-600" : "text-yellow-400"} />
                        {getListCount("PAUSED") > 0 && (
                          <span className="absolute -top-2 -right-2 bg-yellow-500 text-white text-[10px] font-bold rounded-full w-4 h-4 flex items-center justify-center">
                            {getListCount("PAUSED")}
                          </span>
                        )}
                      </div>
                      <span className="ml-1 font-medium text-sm">Paused</span>
                    </button>

                    <button
                      className={`flex items-center justify-center gap-1.5 relative px-3 py-2 rounded-full ${activeTab === "DROPPED" ? "bg-white text-black" : "text-white hover:bg-white/10"}`}
                      onClick={() => setActiveTab("DROPPED")}
                    >
                      <div className="relative">
                        <ListX size={16} className={activeTab === "DROPPED" ? "text-red-600" : "text-red-400"} />
                        {getListCount("DROPPED") > 0 && (
                          <span className="absolute -top-2 -right-2 bg-red-500 text-white text-[10px] font-bold rounded-full w-4 h-4 flex items-center justify-center">
                            {getListCount("DROPPED")}
                          </span>
                        )}
                      </div>
                      <span className="ml-1 font-medium text-sm">Dropped</span>
                    </button>
                  </div>
                </div>

                {/* Desktop Tabs - Centered and Redesigned */}
                <TabsList className="hidden sm:flex flex-nowrap w-auto bg-black/40 backdrop-blur-md rounded-full p-1.5 border border-white/10 gap-1 mx-auto">
                  <TabsTrigger
                    value="CURRENT"
                    className={`flex items-center justify-center gap-1.5 relative px-5 py-2.5 rounded-full ${activeTab === "CURRENT" ? "bg-white text-black" : "text-white hover:bg-white/10"}`}
                    onClick={() => setActiveTab("CURRENT")}
                  >
                    <div className="relative">
                      <PlayCircle size={18} className={activeTab === "CURRENT" ? "text-blue-600" : "text-blue-400"} />
                      {getListCount("CURRENT") > 0 && (
                        <span className="absolute -top-2 -right-2 bg-blue-500 text-white text-[10px] font-bold rounded-full w-5 h-5 flex items-center justify-center">
                          {getListCount("CURRENT")}
                        </span>
                      )}
                    </div>
                    <span className="ml-1 font-medium">Watching</span>
                  </TabsTrigger>

                  <TabsTrigger
                    value="PLANNING"
                    className={`flex items-center justify-center gap-1.5 relative px-5 py-2.5 rounded-full ${activeTab === "PLANNING" ? "bg-white text-black" : "text-white hover:bg-white/10"}`}
                    onClick={() => setActiveTab("PLANNING")}
                  >
                    <div className="relative">
                      <ListPlus size={18} className={activeTab === "PLANNING" ? "text-green-600" : "text-green-400"} />
                      {getListCount("PLANNING") > 0 && (
                        <span className="absolute -top-2 -right-2 bg-green-500 text-white text-[10px] font-bold rounded-full w-5 h-5 flex items-center justify-center">
                          {getListCount("PLANNING")}
                        </span>
                      )}
                    </div>
                    <span className="ml-1 font-medium">Planning</span>
                  </TabsTrigger>

                  <TabsTrigger
                    value="COMPLETED"
                    className={`flex items-center justify-center gap-1.5 relative px-5 py-2.5 rounded-full ${activeTab === "COMPLETED" ? "bg-white text-black" : "text-white hover:bg-white/10"}`}
                    onClick={() => setActiveTab("COMPLETED")}
                  >
                    <div className="relative">
                      <CheckCircle size={18} className={activeTab === "COMPLETED" ? "text-purple-600" : "text-purple-400"} />
                      {getListCount("COMPLETED") > 0 && (
                        <span className="absolute -top-2 -right-2 bg-purple-500 text-white text-[10px] font-bold rounded-full w-5 h-5 flex items-center justify-center">
                          {getListCount("COMPLETED") > 99 ? '99+' : getListCount("COMPLETED")}
                        </span>
                      )}
                    </div>
                    <span className="ml-1 font-medium">Completed</span>
                  </TabsTrigger>

                  <TabsTrigger
                    value="PAUSED"
                    className={`flex items-center justify-center gap-1.5 relative px-5 py-2.5 rounded-full ${activeTab === "PAUSED" ? "bg-white text-black" : "text-white hover:bg-white/10"}`}
                    onClick={() => setActiveTab("PAUSED")}
                  >
                    <div className="relative">
                      <PauseCircle size={18} className={activeTab === "PAUSED" ? "text-yellow-600" : "text-yellow-400"} />
                      {getListCount("PAUSED") > 0 && (
                        <span className="absolute -top-2 -right-2 bg-yellow-500 text-white text-[10px] font-bold rounded-full w-5 h-5 flex items-center justify-center">
                          {getListCount("PAUSED")}
                        </span>
                      )}
                    </div>
                    <span className="ml-1 font-medium">Paused</span>
                  </TabsTrigger>

                  <TabsTrigger
                    value="DROPPED"
                    className={`flex items-center justify-center gap-1.5 relative px-5 py-2.5 rounded-full ${activeTab === "DROPPED" ? "bg-white text-black" : "text-white hover:bg-white/10"}`}
                    onClick={() => setActiveTab("DROPPED")}
                  >
                    <div className="relative">
                      <ListX size={18} className={activeTab === "DROPPED" ? "text-red-600" : "text-red-400"} />
                      {getListCount("DROPPED") > 0 && (
                        <span className="absolute -top-2 -right-2 bg-red-500 text-white text-[10px] font-bold rounded-full w-5 h-5 flex items-center justify-center">
                          {getListCount("DROPPED")}
                        </span>
                      )}
                    </div>
                    <span className="ml-1 font-medium">Dropped</span>
                  </TabsTrigger>
                </TabsList>
              </div>

              {/* Layout Controls - Redesigned */}
              <div className="flex flex-col sm:flex-row justify-center items-center gap-4 mb-6 max-w-xl mx-auto">
                <div className="flex items-center gap-3 bg-black/30 backdrop-blur-md rounded-full px-4 py-2 border border-white/10">
                  <span className="text-sm text-white/80 font-medium">View:</span>
                  <div className="flex bg-black/40 backdrop-blur-md rounded-full p-1 border border-white/10">
                    <Button
                      variant="ghost"
                      size="icon"
                      className={`size-7 rounded-full ${profileSettings.layout === "grid" ? "bg-white/20" : ""}`}
                      onClick={() => handleSettingChange("layout", "grid")}
                      title="Grid View"
                    >
                      <Layout size={14} className="text-white" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className={`size-7 rounded-full ${profileSettings.layout === "list" ? "bg-white/20" : ""}`}
                      onClick={() => handleSettingChange("layout", "list")}
                      title="List View"
                    >
                      <ListPlus size={14} className="text-white" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className={`size-7 rounded-full ${profileSettings.layout === "compact" ? "bg-white/20" : ""}`}
                      onClick={() => handleSettingChange("layout", "compact")}
                      title="Compact View"
                    >
                      <ListX size={14} className="text-white" />
                    </Button>
                  </div>
                </div>

                <div className="flex items-center gap-3 bg-black/30 backdrop-blur-md rounded-full px-4 py-2 border border-white/10">
                  <span className="text-sm text-white/80 font-medium">Compact:</span>
                  <Switch
                    checked={profileSettings.compactView}
                    onCheckedChange={(checked) => handleSettingChange("compactView", checked)}
                    className="data-[state=checked]:bg-white data-[state=checked]:text-black"
                  />
                </div>
              </div>

              {/* Tab Content */}
              <TabsContent value="CURRENT" className="mt-0">
                {listsLoading ? (
                  <div className="flex justify-center py-12">
                    <Loader2 className="w-8 h-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <AnimeListSection
                    lists={animeLists}
                    status="CURRENT"
                    layout={profileSettings.layout}
                    compactView={profileSettings.compactView}
                  />
                )}
              </TabsContent>

              <TabsContent value="PLANNING" className="mt-0">
                {listsLoading ? (
                  <div className="flex justify-center py-12">
                    <Loader2 className="w-8 h-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <AnimeListSection
                    lists={animeLists}
                    status="PLANNING"
                    layout={profileSettings.layout}
                    compactView={profileSettings.compactView}
                  />
                )}
              </TabsContent>

              <TabsContent value="COMPLETED" className="mt-0">
                {listsLoading ? (
                  <div className="flex justify-center py-12">
                    <Loader2 className="w-8 h-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <AnimeListSection
                    lists={animeLists}
                    status="COMPLETED"
                    layout={profileSettings.layout}
                    compactView={profileSettings.compactView}
                  />
                )}
              </TabsContent>

              <TabsContent value="PAUSED" className="mt-0">
                {listsLoading ? (
                  <div className="flex justify-center py-12">
                    <Loader2 className="w-8 h-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <AnimeListSection
                    lists={animeLists}
                    status="PAUSED"
                    layout={profileSettings.layout}
                    compactView={profileSettings.compactView}
                  />
                )}
              </TabsContent>

              <TabsContent value="DROPPED" className="mt-0">
                {listsLoading ? (
                  <div className="flex justify-center py-12">
                    <Loader2 className="w-8 h-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <AnimeListSection
                    lists={animeLists}
                    status="DROPPED"
                    layout={profileSettings.layout}
                    compactView={profileSettings.compactView}
                  />
                )}
              </TabsContent>
            </Tabs>
          </div>
        )}

        {/* Settings Section - Updated to Match Hero Design */}
        {activeSection === "settings" && (
          <div className="w-full max-w-[1920px] mx-auto px-4">
            <div className="text-center mb-6">
              <h2 className="text-2xl md:text-3xl font-bold text-white">Profile Settings</h2>
              <p className="text-white/60 mt-2">Customize your profile appearance and preferences</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Display Settings */}
              <div className="bg-black/30 backdrop-blur-md rounded-xl border border-white/10 p-5 hover:bg-black/40 transition-colors">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-9 h-9 rounded-full bg-white/10 flex items-center justify-center">
                    <Layout size={18} className="text-white" />
                  </div>
                  <h3 className="text-lg font-medium text-white">Display Settings</h3>
                </div>

                <div className="space-y-5">
                  <div className="flex items-center justify-between bg-black/20 p-3 rounded-xl hover:bg-black/30 transition-colors">
                    <Label htmlFor="show-stats" className="text-white flex flex-col">
                      <span className="font-medium">Show Statistics</span>
                      <span className="text-xs text-white/60 mt-1">Display anime stats on your profile</span>
                    </Label>
                    <Switch
                      id="show-stats"
                      checked={profileSettings.showStats}
                      onCheckedChange={(checked) => handleSettingChange("showStats", checked)}
                      className="data-[state=checked]:bg-white data-[state=checked]:text-black"
                    />
                  </div>

                  <div className="flex items-center justify-between bg-black/20 p-3 rounded-xl hover:bg-black/30 transition-colors">
                    <Label htmlFor="show-bio" className="text-white flex flex-col">
                      <span className="font-medium">Show Bio</span>
                      <span className="text-xs text-white/60 mt-1">Display your bio on your profile</span>
                    </Label>
                    <Switch
                      id="show-bio"
                      checked={profileSettings.showBio}
                      onCheckedChange={(checked) => handleSettingChange("showBio", checked)}
                      className="data-[state=checked]:bg-white data-[state=checked]:text-black"
                    />
                  </div>

                  <div className="flex items-center justify-between bg-black/20 p-3 rounded-xl hover:bg-black/30 transition-colors">
                    <Label htmlFor="banner-opacity" className="text-white flex flex-col">
                      <span className="font-medium">Banner Opacity</span>
                      <span className="text-xs text-white/60 mt-1">Adjust the opacity of your banner image</span>
                    </Label>
                    <Select
                      value={profileSettings.bannerOpacity.toString()}
                      onValueChange={(value) => handleSettingChange("bannerOpacity", parseFloat(value))}
                    >
                      <SelectTrigger className="w-32 bg-black/40 border-white/10 rounded-full">
                        <SelectValue placeholder="Select opacity" />
                      </SelectTrigger>
                      <SelectContent className="bg-black/90 border-white/10 rounded-xl">
                        <SelectItem value="0.5">50%</SelectItem>
                        <SelectItem value="0.6">60%</SelectItem>
                        <SelectItem value="0.7">70%</SelectItem>
                        <SelectItem value="0.8">80%</SelectItem>
                        <SelectItem value="0.9">90%</SelectItem>
                        <SelectItem value="1">100%</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Theme Settings */}
              <div className="bg-black/30 backdrop-blur-md rounded-xl border border-white/10 p-5 hover:bg-black/40 transition-colors">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-9 h-9 rounded-full bg-white/10 flex items-center justify-center">
                    <Settings size={18} className="text-white" />
                  </div>
                  <h3 className="text-lg font-medium text-white">Theme Settings</h3>
                </div>

                <div className="space-y-5">
                  <div className="flex items-center justify-between bg-black/20 p-3 rounded-xl hover:bg-black/30 transition-colors">
                    <Label htmlFor="accent-color" className="text-white flex flex-col">
                      <span className="font-medium">Accent Color</span>
                      <span className="text-xs text-white/60 mt-1">Choose your profile accent color</span>
                    </Label>
                    <Select
                      value={profileSettings.accentColor}
                      onValueChange={(value) => handleSettingChange("accentColor", value)}
                    >
                      <SelectTrigger className="w-32 bg-black/40 border-white/10 rounded-full">
                        <SelectValue placeholder="Select color" />
                      </SelectTrigger>
                      <SelectContent className="bg-black/90 border-white/10 rounded-xl">
                        <SelectItem value="blue">Blue</SelectItem>
                        <SelectItem value="purple">Purple</SelectItem>
                        <SelectItem value="green">Green</SelectItem>
                        <SelectItem value="red">Red</SelectItem>
                        <SelectItem value="orange">Orange</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between bg-black/20 p-3 rounded-xl hover:bg-black/30 transition-colors">
                    <Label htmlFor="default-layout" className="text-white flex flex-col">
                      <span className="font-medium">Default Layout</span>
                      <span className="text-xs text-white/60 mt-1">Choose how your anime lists are displayed</span>
                    </Label>
                    <Select
                      value={profileSettings.layout}
                      onValueChange={(value) => handleSettingChange("layout", value)}
                    >
                      <SelectTrigger className="w-32 bg-black/40 border-white/10 rounded-full">
                        <SelectValue placeholder="Select layout" />
                      </SelectTrigger>
                      <SelectContent className="bg-black/90 border-white/10 rounded-xl">
                        <SelectItem value="grid">Grid</SelectItem>
                        <SelectItem value="list">List</SelectItem>
                        <SelectItem value="compact">Compact</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between bg-black/20 p-3 rounded-xl hover:bg-black/30 transition-colors">
                    <Label htmlFor="compact-view" className="text-white flex flex-col">
                      <span className="font-medium">Compact View</span>
                      <span className="text-xs text-white/60 mt-1">Show more items in each row</span>
                    </Label>
                    <Switch
                      id="compact-view"
                      checked={profileSettings.compactView}
                      onCheckedChange={(checked) => handleSettingChange("compactView", checked)}
                      className="data-[state=checked]:bg-white data-[state=checked]:text-black"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Component to display anime list for a specific status
const AnimeListSection = ({ lists, status, layout = "grid", compactView = false }) => {
  // Check if lists is null or undefined
  if (!lists) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-gray-400">
        <p>Unable to load anime list</p>
      </div>
    );
  }

  // Find the list with the matching status
  const statusList = lists.find(list => list.status === status);

  if (!statusList || !statusList.entries || statusList.entries.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-20 text-white bg-black/30 backdrop-blur-md rounded-xl border border-white/10 shadow-lg">
        <div className="flex flex-col items-center gap-5 max-w-md text-center px-4">
          <div className="w-20 h-20 rounded-full flex items-center justify-center"
            style={{
              backgroundColor: status === "CURRENT" ? "rgba(59, 130, 246, 0.2)" :
                              status === "COMPLETED" ? "rgba(147, 51, 234, 0.2)" :
                              status === "PLANNING" ? "rgba(34, 197, 94, 0.2)" :
                              status === "PAUSED" ? "rgba(234, 179, 8, 0.2)" :
                              "rgba(239, 68, 68, 0.2)",
              border: status === "CURRENT" ? "2px solid rgba(59, 130, 246, 0.4)" :
                      status === "COMPLETED" ? "2px solid rgba(147, 51, 234, 0.4)" :
                      status === "PLANNING" ? "2px solid rgba(34, 197, 94, 0.4)" :
                      status === "PAUSED" ? "2px solid rgba(234, 179, 8, 0.4)" :
                      "2px solid rgba(239, 68, 68, 0.4)"
            }}
          >
            {status === "CURRENT" && <PlayCircle size={48} className="text-blue-400" />}
            {status === "PLANNING" && <ListPlus size={48} className="text-green-400" />}
            {status === "COMPLETED" && <CheckCircle size={48} className="text-purple-400" />}
            {status === "PAUSED" && <PauseCircle size={48} className="text-yellow-400" />}
            {status === "DROPPED" && <ListX size={48} className="text-red-400" />}
          </div>

          <div>
            <h3 className="text-xl font-bold mb-2">
              {status === "CURRENT" && "No Anime Currently Watching"}
              {status === "PLANNING" && "Your Planning List is Empty"}
              {status === "COMPLETED" && "No Completed Anime Yet"}
              {status === "PAUSED" && "No Paused Anime"}
              {status === "DROPPED" && "No Dropped Anime"}
            </h3>

            <p className="text-white/70">
              {status === "CURRENT" && "Start watching some anime and they'll appear here. Explore the catalog to find your next favorite series!"}
              {status === "PLANNING" && "Add anime to your planning list to keep track of what you want to watch next. Explore the catalog to discover new series!"}
              {status === "COMPLETED" && "Once you finish watching anime, they'll be listed here. Keep watching to build your collection of completed series!"}
              {status === "PAUSED" && "When you need a break from a series, you can pause it and it will appear here for easy access later."}
              {status === "DROPPED" && "If you decide not to continue watching a series, it will be listed here. Everyone has different tastes!"}
            </p>
          </div>

          <Link to="/explore" className="mt-2 px-6 py-2.5 bg-white text-black font-medium rounded-full flex items-center gap-2 hover:bg-white/90 transition-colors">
            <Compass size={18} />
            <span>Explore Anime</span>
          </Link>
        </div>
      </div>
    );
  }

  // Grid Layout (Default)
  if (layout === "grid") {
    const gridCols = compactView
      ? "grid-cols-2 sm:grid-cols-3 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-7"
      : "grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6";

    return (
      <div className={`grid ${gridCols} gap-4 md:gap-6`}>
        {statusList.entries.map(entry => (
          <Link
            key={entry.id}
            to={`/anime/${entry.media.id}`}
            className="flex flex-col group"
          >
            <div className="flex w-full aspect-[1/1.45] bg-black/40 backdrop-blur-md rounded-xl relative overflow-hidden group-hover:scale-[1.03] transition-all duration-300 shadow-lg border border-white/10 group-hover:border-white/30 group-hover:shadow-xl">
              <Image
                src={entry.media.coverImage.large || entry.media.coverImage.medium}
                quality="high"
                className="!object-cover !w-full !h-full"
              />

              {/* Overlay with gradient */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              {/* Progress Badge */}
              {entry.progress > 0 && (
                <div className="absolute bottom-3 left-3 bg-black/60 backdrop-blur-md text-white text-xs px-2.5 py-1.5 rounded-full flex items-center gap-1.5 border border-white/10 shadow-md">
                  <PlayCircle size={14} className="text-blue-400" />
                  <span className="font-medium">{entry.progress}</span>
                  <span className="text-white/70">/</span>
                  <span className="text-white/70">{entry.media.episodes || '?'}</span>
                </div>
              )}

              {/* Score Badge */}
              {entry.score > 0 && (
                <div className="absolute top-3 right-3 bg-black/60 backdrop-blur-md text-white text-xs px-2.5 py-1.5 rounded-full flex items-center gap-1.5 border border-white/10 shadow-md">
                  <Star fill="gold" color="gold" size={14} />
                  <span className="font-medium">{entry.score}</span>
                </div>
              )}

              {/* Status Badge - Only show for non-current lists */}
              {status !== "CURRENT" && (
                <div className="absolute top-3 left-3 backdrop-blur-md text-white text-xs px-2.5 py-1.5 rounded-full flex items-center gap-1.5 border border-white/10 shadow-md"
                  style={{
                    backgroundColor: status === "COMPLETED" ? "rgba(147, 51, 234, 0.6)" :
                                    status === "PLANNING" ? "rgba(34, 197, 94, 0.6)" :
                                    status === "PAUSED" ? "rgba(234, 179, 8, 0.6)" :
                                    "rgba(239, 68, 68, 0.6)"
                  }}
                >
                  {status === "COMPLETED" && <CheckCircle size={14} />}
                  {status === "PLANNING" && <ListPlus size={14} />}
                  {status === "PAUSED" && <PauseCircle size={14} />}
                  {status === "DROPPED" && <ListX size={14} />}
                  <span className="font-medium">
                    {status === "COMPLETED" && "Completed"}
                    {status === "PLANNING" && "Plan to Watch"}
                    {status === "PAUSED" && "Paused"}
                    {status === "DROPPED" && "Dropped"}
                  </span>
                </div>
              )}

              {/* Quick Info - Visible on hover */}
              <div className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-black/90 to-black/0 translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                <h3 className="text-sm font-medium text-white line-clamp-2">
                  {entry.media.title.english || entry.media.title.romaji}
                </h3>
                <div className="flex flex-wrap gap-2 mt-2">
                  {entry.media.genres && entry.media.genres.slice(0, 2).map((genre, idx) => (
                    <span key={idx} className="text-[10px] bg-white/10 px-2 py-0.5 rounded-full text-white/80">
                      {genre}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            <div className="mt-3 px-1">
              <h3 className="text-sm font-medium line-clamp-2 group-hover:text-white transition-colors text-white/90">
                {entry.media.title.english || entry.media.title.romaji}
              </h3>
              <div className="flex w-full text-[10px] text-white/60 mt-1 items-center">
                {entry.media.format && (
                  <>
                    <span className="uppercase">{entry.media.format}</span>
                    <span className="mx-1">•</span>
                  </>
                )}
                <span>{entry.media.episodes ? `${entry.media.episodes} Episodes` : 'Unknown'}</span>
                {entry.media.averageScore && (
                  <>
                    <span className="mx-1">•</span>
                    <span className="flex items-center">
                      <Star size={10} className="mr-0.5 text-yellow-400" fill="currentColor" />
                      {entry.media.averageScore}%
                    </span>
                  </>
                )}
              </div>
            </div>
          </Link>
        ))}
      </div>
    );
  }

  // List Layout
  if (layout === "list") {
    return (
      <div className="flex flex-col gap-4">
        {statusList.entries.map(entry => (
          <Link
            key={entry.id}
            to={`/anime/${entry.media.id}`}
            className="flex bg-black/40 backdrop-blur-md rounded-xl overflow-hidden border border-white/10 hover:bg-black/60 hover:border-white/20 transition-all group shadow-md hover:shadow-lg"
          >
            <div className="w-20 sm:w-24 h-28 sm:h-32 flex-shrink-0 relative">
              <Image
                src={entry.media.coverImage.large || entry.media.coverImage.medium}
                quality="high"
                className="!object-cover !w-full !h-full"
              />
              <div className="absolute inset-0 bg-gradient-to-r from-black/50 to-transparent"></div>
            </div>

            <div className="flex flex-col justify-between p-4 flex-grow">
              <div>
                <h3 className="text-sm sm:text-base font-medium group-hover:text-white text-white/90 transition-colors">
                  {entry.media.title.english || entry.media.title.romaji}
                </h3>

                <div className="flex flex-wrap gap-2 mt-2">
                  {entry.media.genres && entry.media.genres.slice(0, 3).map((genre, idx) => (
                    <span key={idx} className="text-[10px] bg-white/10 px-2 py-0.5 rounded-full text-white/80">
                      {genre}
                    </span>
                  ))}
                </div>

                <div className="flex flex-wrap gap-x-3 gap-y-1 mt-3">
                  {entry.media.format && (
                    <span className="text-xs text-white/70 uppercase flex items-center">
                      <span className="w-1.5 h-1.5 rounded-full bg-white/40 mr-1.5"></span>
                      {entry.media.format}
                    </span>
                  )}
                  <span className="text-xs text-white/70 flex items-center">
                    <span className="w-1.5 h-1.5 rounded-full bg-white/40 mr-1.5"></span>
                    {entry.media.episodes ? `${entry.media.episodes} Episodes` : 'Unknown'}
                  </span>
                  {entry.media.averageScore && (
                    <span className="text-xs text-white/70 flex items-center">
                      <span className="w-1.5 h-1.5 rounded-full bg-white/40 mr-1.5"></span>
                      <Star size={12} className="mr-1 text-yellow-400" fill="currentColor" />
                      {entry.media.averageScore}%
                    </span>
                  )}
                </div>
              </div>

              <div className="flex items-center justify-between mt-4 pt-3 border-t border-white/10">
                <div className="flex items-center gap-4">
                  {entry.progress > 0 && (
                    <span className="text-xs text-white/80 flex items-center gap-1.5 bg-blue-500/20 px-2.5 py-1 rounded-full">
                      <PlayCircle size={14} className="text-blue-400" />
                      <span className="font-medium">{entry.progress}</span>
                      <span className="text-white/60">/</span>
                      <span className="text-white/60">{entry.media.episodes || '?'}</span>
                    </span>
                  )}

                  {entry.score > 0 && (
                    <span className="text-xs text-white/80 flex items-center gap-1.5 bg-yellow-500/20 px-2.5 py-1 rounded-full">
                      <Star size={14} className="text-yellow-400" fill="currentColor" />
                      <span className="font-medium">{entry.score}</span>
                    </span>
                  )}
                </div>

                <div className="flex items-center">
                  <span className="text-xs px-3 py-1 rounded-full text-white font-medium flex items-center gap-1.5"
                    style={{
                      backgroundColor: status === "CURRENT" ? "rgba(59, 130, 246, 0.6)" :
                                      status === "COMPLETED" ? "rgba(147, 51, 234, 0.6)" :
                                      status === "PLANNING" ? "rgba(34, 197, 94, 0.6)" :
                                      status === "PAUSED" ? "rgba(234, 179, 8, 0.6)" :
                                      "rgba(239, 68, 68, 0.6)"
                    }}
                  >
                    {status === "CURRENT" && (
                      <>
                        <PlayCircle size={14} />
                        <span>Watching</span>
                      </>
                    )}
                    {status === "COMPLETED" && (
                      <>
                        <CheckCircle size={14} />
                        <span>Completed</span>
                      </>
                    )}
                    {status === "PLANNING" && (
                      <>
                        <ListPlus size={14} />
                        <span>Planning</span>
                      </>
                    )}
                    {status === "PAUSED" && (
                      <>
                        <PauseCircle size={14} />
                        <span>Paused</span>
                      </>
                    )}
                    {status === "DROPPED" && (
                      <>
                        <ListX size={14} />
                        <span>Dropped</span>
                      </>
                    )}
                  </span>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    );
  }

  // Compact Layout
  if (layout === "compact") {
    return (
      <div className="grid grid-cols-1 gap-2.5">
        {statusList.entries.map(entry => (
          <Link
            key={entry.id}
            to={`/anime/${entry.media.id}`}
            className="flex items-center bg-black/40 backdrop-blur-md rounded-xl p-3 hover:bg-black/60 transition-all group border border-white/10 hover:border-white/20 shadow-sm hover:shadow-md"
          >
            <div className="w-12 h-16 rounded-lg overflow-hidden mr-4 flex-shrink-0 border border-white/10">
              <Image
                src={entry.media.coverImage.large || entry.media.coverImage.medium}
                quality="high"
                className="!object-cover !w-full !h-full"
              />
            </div>

            <div className="flex-grow min-w-0">
              <h3 className="text-sm font-medium truncate group-hover:text-white text-white/90 transition-colors">
                {entry.media.title.english || entry.media.title.romaji}
              </h3>
              <div className="flex items-center flex-wrap gap-3 mt-1">
                {entry.progress > 0 && (
                  <span className="flex items-center gap-1 text-xs text-white/70">
                    <PlayCircle size={12} className="text-blue-400" />
                    <span>{entry.progress}</span>
                    <span className="text-white/50">/</span>
                    <span className="text-white/50">{entry.media.episodes || '?'}</span>
                  </span>
                )}

                {entry.score > 0 && (
                  <span className="flex items-center gap-1 text-xs text-white/70">
                    <Star size={12} className="text-yellow-400" fill="currentColor" />
                    <span>{entry.score}</span>
                  </span>
                )}

                {entry.media.format && (
                  <span className="uppercase text-xs text-white/50 hidden sm:inline">{entry.media.format}</span>
                )}
              </div>
            </div>

            <div className="ml-auto pl-3 flex-shrink-0">
              <span className="text-xs px-2.5 py-1 rounded-full text-white font-medium flex items-center gap-1.5 whitespace-nowrap"
                style={{
                  backgroundColor: status === "CURRENT" ? "rgba(59, 130, 246, 0.6)" :
                                  status === "COMPLETED" ? "rgba(147, 51, 234, 0.6)" :
                                  status === "PLANNING" ? "rgba(34, 197, 94, 0.6)" :
                                  status === "PAUSED" ? "rgba(234, 179, 8, 0.6)" :
                                  "rgba(239, 68, 68, 0.6)"
                }}
              >
                {status === "CURRENT" && (
                  <>
                    <PlayCircle size={12} className="hidden sm:block" />
                    <span>Watching</span>
                  </>
                )}
                {status === "COMPLETED" && (
                  <>
                    <CheckCircle size={12} className="hidden sm:block" />
                    <span>Completed</span>
                  </>
                )}
                {status === "PLANNING" && (
                  <>
                    <ListPlus size={12} className="hidden sm:block" />
                    <span>Planning</span>
                  </>
                )}
                {status === "PAUSED" && (
                  <>
                    <PauseCircle size={12} className="hidden sm:block" />
                    <span>Paused</span>
                  </>
                )}
                {status === "DROPPED" && (
                  <>
                    <ListX size={12} className="hidden sm:block" />
                    <span>Dropped</span>
                  </>
                )}
              </span>
            </div>
          </Link>
        ))}
      </div>
    );
  }

  // Fallback to grid layout if an invalid layout is provided
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6">
      {statusList.entries.map(entry => (
        <Link
          key={entry.id}
          to={`/anime/${entry.media.id}`}
          className="flex flex-col group"
        >
          <div className="flex w-full aspect-[1/1.45] bg-black/40 backdrop-blur-md rounded-xl relative overflow-hidden group-hover:scale-[1.03] transition-all duration-300 shadow-lg border border-white/10 group-hover:border-white/30 group-hover:shadow-xl">
            <Image
              src={entry.media.coverImage.large || entry.media.coverImage.medium}
              quality="high"
              className="!object-cover !w-full !h-full"
            />

            {/* Progress Badge */}
            {entry.progress > 0 && (
              <div className="absolute bottom-3 left-3 bg-black/60 backdrop-blur-md text-white text-xs px-2.5 py-1.5 rounded-full flex items-center gap-1.5 border border-white/10 shadow-md">
                <PlayCircle size={14} className="text-blue-400" />
                <span className="font-medium">{entry.progress}</span>
                <span className="text-white/70">/</span>
                <span className="text-white/70">{entry.media.episodes || '?'}</span>
              </div>
            )}
          </div>

          <div className="mt-3 px-1">
            <h3 className="text-sm font-medium line-clamp-2 group-hover:text-white transition-colors text-white/90">
              {entry.media.title.english || entry.media.title.romaji}
            </h3>
          </div>
        </Link>
      ))}
    </div>
  );
};

export default ProfileRedesign;
