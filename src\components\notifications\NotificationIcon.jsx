import { Bell } from 'lucide-react';
import { useState, useEffect, useRef } from 'react';
import { useNotifications } from '@/context/NotificationContext';
import NotificationPanel from './NotificationPanel';
import Portal from '@/components/ui/Portal';

const NotificationIcon = ({ className = '', size = 20 }) => {
  const { unreadCount } = useNotifications();
  const [isOpen, setIsOpen] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const containerRef = useRef(null);
  const hoverTimeoutRef = useRef(null);

  // Check if device is mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    // Initial check
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);

    // Cleanup
    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  // <PERSON>le click outside to close panel
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target) && isOpen) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Handle hover state with delay
  const handleMouseEnter = () => {
    if (isMobile) return;

    clearTimeout(hoverTimeoutRef.current);
    hoverTimeoutRef.current = setTimeout(() => {
      setIsHovering(true);
    }, 300); // 300ms delay before showing
  };

  const handleMouseLeave = () => {
    if (isMobile) return;

    clearTimeout(hoverTimeoutRef.current);
    hoverTimeoutRef.current = setTimeout(() => {
      setIsHovering(false);
    }, 400); // 400ms delay before hiding
  };

  const togglePanel = () => {
    setIsOpen(!isOpen);
  };

  // Determine if panel should be shown
  const showPanel = isMobile ? isOpen : (isOpen || isHovering);

  return (
    <div
      className="relative"
      ref={containerRef}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <button
        onClick={togglePanel}
        className={`flex items-center justify-center relative ${className}`}
        aria-label="Notifications"
      >
        <Bell size={size} />

        {/* Notification badge */}
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 flex items-center justify-center w-4 h-4 bg-red-500 text-white text-[10px] font-bold rounded-full">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>

      {/* Notification panel */}
      {showPanel && (
        <Portal zIndex={99999}>
          <>
            {/* Backdrop for mobile */}
            {isMobile && (
              <div
                className="fixed inset-0 bg-black/50"
                style={{zIndex: 99990}}
                onClick={() => setIsOpen(false)}
              />
            )}

            {/* Panel */}
            <div className={`
              fixed mt-2 w-80 sm:w-96 origin-top-right
              transition-all duration-200 ease-in-out
              ${isMobile ? 'right-4 top-14' : 'right-4 lg:right-auto lg:left-64 lg:top-[var(--notification-top,4rem)]'}
              ${showPanel ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-2 pointer-events-none'}
            `}
            style={{zIndex: 99999}}>
              <NotificationPanel onClose={() => isMobile ? setIsOpen(false) : setIsHovering(false)} />
            </div>
          </>
        </Portal>
      )}
    </div>
  );
};

export default NotificationIcon;
