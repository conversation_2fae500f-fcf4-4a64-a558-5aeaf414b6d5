import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { gsapScrollTo } from '@/utils/gsapSmoothScroll';

/**
 * Component that scrolls to top when the route changes
 * This is useful for ensuring users start at the top of the page when navigating
 */
const ScrollToTop = ({ smooth = true }) => {
  const { pathname } = useLocation();

  useEffect(() => {
    // Scroll to top when the route changes
    if (smooth) {
      // Use GSAP for smooth scrolling
      gsapScrollTo(0, {
        duration: 0.8,
        ease: 'power2.inOut'
      });
    } else {
      // Use instant scrolling
      window.scrollTo(0, 0);
    }
  }, [pathname, smooth]);

  // This component doesn't render anything
  return null;
};

export default ScrollToTop;
