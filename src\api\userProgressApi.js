import axios from 'axios';

// Base API URL - replace with your actual backend URL when deployed
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://api.yourbackend.com'
  : 'http://localhost:3004/api';

// Use the profile API endpoints instead of the legacy endpoints
const USE_PROFILE_API = true;

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests if available
api.interceptors.request.use(config => {
  // Get token from AniList storage
  const anilistStorage = localStorage.getItem('anilist');
  if (anilistStorage) {
    try {
      const anilistData = JSON.parse(anilistStorage);
      if (anilistData.access_token) {
        config.headers.Authorization = `Bearer ${anilistData.access_token}`;
      }
    } catch (error) {
      console.error('Error parsing AniList storage:', error);
    }
  }
  return config;
});

// User Progress API endpoints
const userProgressApi = {
  // User Activity endpoints
  getUserActivity: async (userId) => {
    try {
      // Use profile API if enabled
      if (USE_PROFILE_API) {
        const response = await api.get(`/profile/${userId}`);
        return response.data;
      } else {
        const response = await api.get(`/users/${userId}/activity`);
        return response.data;
      }
    } catch (error) {
      console.error('Error fetching user activity:', error);
      throw error;
    }
  },

  updateUserActivity: async (userId, activityData) => {
    try {
      // Use profile API if enabled
      if (USE_PROFILE_API) {
        const response = await api.put(`/profile/${userId}`, activityData);
        return response.data;
      } else {
        const response = await api.put(`/users/${userId}/activity`, activityData);
        return response.data;
      }
    } catch (error) {
      console.error('Error updating user activity:', error);
      throw error;
    }
  },

  recordVisit: async (userId) => {
    try {
      // Use profile API if enabled
      if (USE_PROFILE_API) {
        const response = await api.post(`/profile/${userId}/visit`);
        return response.data;
      } else {
        const response = await api.post(`/users/${userId}/visits`);
        return response.data;
      }
    } catch (error) {
      console.error('Error recording visit:', error);
      throw error;
    }
  },

  // XP and Level endpoints
  addXp: async (userId, amount, source) => {
    try {
      // Use profile API if enabled
      if (USE_PROFILE_API) {
        const response = await api.post(`/profile/${userId}/xp`, { amount, source });
        return response.data;
      } else {
        const response = await api.post(`/users/${userId}/xp`, { amount, source });
        return response.data;
      }
    } catch (error) {
      console.error('Error adding XP:', error);
      throw error;
    }
  },

  // Achievements endpoints
  getUserAchievements: async (userId) => {
    try {
      // Use profile API if enabled
      if (USE_PROFILE_API) {
        // For profile API, achievements are included in the user profile
        const response = await api.get(`/profile/${userId}`);
        return response.data.achievements || [];
      } else {
        const response = await api.get(`/users/${userId}/achievements`);
        return response.data;
      }
    } catch (error) {
      console.error('Error fetching achievements:', error);
      throw error;
    }
  },

  unlockAchievement: async (userId, achievementId) => {
    try {
      // Use profile API if enabled
      if (USE_PROFILE_API) {
        const response = await api.post(`/profile/${userId}/achievements`, { achievementId });
        return response.data;
      } else {
        const response = await api.post(`/users/${userId}/achievements`, { achievementId });
        return response.data;
      }
    } catch (error) {
      console.error('Error unlocking achievement:', error);
      throw error;
    }
  },

  // Tasks endpoints
  getDailyTasks: async (userId) => {
    try {
      // Use profile API if enabled
      if (USE_PROFILE_API) {
        const response = await api.get(`/profile/${userId}/tasks`);
        return response.data;
      } else {
        const response = await api.get(`/users/${userId}/tasks`);
        return response.data;
      }
    } catch (error) {
      console.error('Error fetching daily tasks:', error);
      throw error;
    }
  },

  completeTask: async (userId, taskId) => {
    try {
      // Use profile API if enabled
      if (USE_PROFILE_API) {
        const response = await api.post(`/profile/${userId}/tasks/${taskId}/complete`);
        return response.data;
      } else {
        const response = await api.post(`/users/${userId}/tasks/${taskId}/complete`);
        return response.data;
      }
    } catch (error) {
      console.error('Error completing task:', error);
      throw error;
    }
  },

  // Likes endpoints
  addLike: async (userId, targetUserId) => {
    try {
      // Use profile API if enabled
      if (USE_PROFILE_API) {
        const response = await api.post(`/profile/${targetUserId}/like`, { likerUserId: userId });
        return response.data;
      } else {
        const response = await api.post(`/users/${targetUserId}/likes`, { userId });
        return response.data;
      }
    } catch (error) {
      console.error('Error adding like:', error);
      throw error;
    }
  },

  getUserLikes: async (userId) => {
    try {
      // Use profile API if enabled
      if (USE_PROFILE_API) {
        // For profile API, likes are included in the user profile
        const response = await api.get(`/profile/${userId}`);
        return { likes: response.data.likes || 0 };
      } else {
        const response = await api.get(`/users/${userId}/likes`);
        return response.data;
      }
    } catch (error) {
      console.error('Error fetching user likes:', error);
      throw error;
    }
  },

  // Leaderboard endpoints
  getLeaderboard: async (sortBy = 'watchTime', limit = 10) => {
    try {
      // Use profile API if enabled
      if (USE_PROFILE_API) {
        // Map legacy sortBy values to profile API type values
        let type = 'xp';
        if (sortBy === 'streak') type = 'streak';
        else if (sortBy === 'likes') type = 'likes';
        else if (sortBy === 'level') type = 'xp'; // Level is based on XP

        const response = await api.get(`/leaderboard?type=${type}&limit=${limit}`);
        return response.data;
      } else {
        const response = await api.get(`/leaderboard?sortBy=${sortBy}&limit=${limit}`);
        return response.data;
      }
    } catch (error) {
      console.error('Error fetching leaderboard:', error);

      // Return sample data as fallback
      return [
        {
          anilistId: "sample1",
          level: 78,
          rank: "Anime Legend",
          xp: 60000,
          streak: 145,
          longestStreak: 145,
          likes: 872
        },
        {
          anilistId: "sample2",
          level: 65,
          rank: "Anime Master",
          xp: 42000,
          streak: 90,
          longestStreak: 90,
          likes: 654
        },
        {
          anilistId: "sample3",
          level: 52,
          rank: "Anime Master",
          xp: 27000,
          streak: 60,
          longestStreak: 60,
          likes: 521
        },
        {
          anilistId: "sample4",
          level: 45,
          rank: "Anime Expert",
          xp: 20000,
          streak: 45,
          longestStreak: 45,
          likes: 432
        },
        {
          anilistId: "sample5",
          level: 38,
          rank: "Anime Veteran",
          xp: 14000,
          streak: 30,
          longestStreak: 30,
          likes: 345
        }
      ];
    }
  },

  // Fallback methods for when API is unavailable
  // These use localStorage as a backup
  fallback: {
    getUserActivity: (userId) => {
      try {
        const streak = parseInt(localStorage.getItem(`streak-${userId}`)) || 0;
        const longestStreak = parseInt(localStorage.getItem(`longest-streak-${userId}`)) || 0;
        const lastVisit = parseInt(localStorage.getItem(`last-visit-${userId}`)) || null;
        const visitDates = JSON.parse(localStorage.getItem(`visit-dates-${userId}`)) || [];
        const level = parseInt(localStorage.getItem(`level-${userId}`)) || 1;
        const xp = parseInt(localStorage.getItem(`xp-${userId}`)) || 0;
        const rank = localStorage.getItem(`rank-${userId}`) || 'Rookie';

        return {
          streak,
          longestStreak,
          lastVisit,
          visitDates,
          level,
          xp,
          rank,
          isFromFallback: true
        };
      } catch (error) {
        console.error('Error in fallback getUserActivity:', error);
        return {
          streak: 0,
          longestStreak: 0,
          lastVisit: null,
          visitDates: [],
          level: 1,
          xp: 0,
          rank: 'Rookie',
          isFromFallback: true
        };
      }
    },

    updateUserActivity: (userId, activityData) => {
      try {
        if (activityData.streak) localStorage.setItem(`streak-${userId}`, activityData.streak.toString());
        if (activityData.longestStreak) localStorage.setItem(`longest-streak-${userId}`, activityData.longestStreak.toString());
        if (activityData.lastVisit) localStorage.setItem(`last-visit-${userId}`, activityData.lastVisit.toString());
        if (activityData.visitDates) localStorage.setItem(`visit-dates-${userId}`, JSON.stringify(activityData.visitDates));
        if (activityData.level) localStorage.setItem(`level-${userId}`, activityData.level.toString());
        if (activityData.xp) localStorage.setItem(`xp-${userId}`, activityData.xp.toString());
        if (activityData.rank) localStorage.setItem(`rank-${userId}`, activityData.rank);

        return { ...activityData, isFromFallback: true };
      } catch (error) {
        console.error('Error in fallback updateUserActivity:', error);
        return { isFromFallback: true, error: true };
      }
    }
  }
};

export default userProgressApi;
