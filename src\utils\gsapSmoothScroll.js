/**
 * GSAP-based smooth scrolling implementation
 * This file provides smooth scrolling functionality using GSAP
 */

import gsap from 'gsap';
import { ScrollToPlugin } from 'gsap/ScrollToPlugin';

// Register the ScrollToPlugin with GSAP
try {
  gsap.registerPlugin(ScrollToPlugin);
  console.log('ScrollToPlugin registered successfully');
} catch (error) {
  console.error('Failed to register ScrollToPlugin:', error);
}

/**
 * Smoothly scrolls to a specific element or position on the page using GSAP
 *
 * @param {string|HTMLElement|number} target - Element ID, DOM element, or Y position to scroll to
 * @param {Object} options - Scroll options
 * @param {number} options.duration - Duration of scroll animation in ms (default: 800)
 * @param {number} options.offset - Offset from the target in pixels (default: 0)
 * @param {string} options.ease - GSAP easing function (default: 'power2.inOut')
 * @param {Function} options.onComplete - Function to call when scrolling is complete
 */
export const gsapScrollTo = (target, options = {}) => {
  // Default options
  const {
    duration = 0.8, // GSAP uses seconds instead of milliseconds
    offset = 0,
    ease = 'power2.inOut',
    onComplete = () => {}
  } = options;

  // Get the target position
  let targetPosition;
  let targetElement = null;

  if (typeof target === 'number') {
    // If target is a number, use it directly as the Y position
    targetPosition = target;
  } else {
    // If target is a string (element ID) or an element
    targetElement = typeof target === 'string'
      ? document.getElementById(target)
      : target;

    if (!targetElement) {
      console.error('Target element not found');
      return;
    }

    // Get the element's position relative to the viewport
    const rect = targetElement.getBoundingClientRect();

    // Calculate the absolute position by adding the current scroll position
    targetPosition = rect.top + window.scrollY;
  }

  // Apply offset
  targetPosition += offset;

  // Use GSAP to animate the scroll
  try {
    console.log('GSAP scrolling to:', targetPosition);
    gsap.to(window, {
      duration,
      scrollTo: {
        y: targetPosition,
        autoKill: true
      },
      ease,
      onComplete
    });
  } catch (error) {
    console.error('GSAP scroll error:', error);
    // Fallback to native scrolling if GSAP fails
    window.scrollTo({
      top: targetPosition,
      behavior: 'smooth'
    });
  }
};

/**
 * Creates a smooth scroll behavior for all anchor links on the page using GSAP
 *
 * @param {Object} options - Options for the smooth scroll
 * @param {number} options.duration - Duration of scroll animation in seconds (default: 0.8)
 * @param {number} options.offset - Offset from the target in pixels (default: 0)
 * @param {string} options.ease - GSAP easing function (default: 'power2.inOut')
 */
export const initGsapSmoothScrollAnchors = (options = {}) => {
  // Get all anchor links
  document.addEventListener('click', (event) => {
    // Check if the clicked element is an anchor link
    let target = event.target;

    // If the target is not an anchor, check if it's a child of an anchor
    while (target && target.tagName !== 'A') {
      target = target.parentElement;
    }

    // If no anchor was found, return
    if (!target) return;

    // Get the href attribute
    const href = target.getAttribute('href');

    // Check if the href is an anchor link
    if (href && href.startsWith('#') && href.length > 1) {
      // Prevent default behavior
      event.preventDefault();

      // Get the target element
      const targetId = href.substring(1);
      const targetElement = document.getElementById(targetId);

      if (!targetElement) {
        console.error(`Element with id "${targetId}" not found`);
        return;
      }

      // Use GSAP for smooth scrolling
      gsapScrollTo(targetElement, options);

      // Update the URL hash without scrolling
      if (history.pushState) {
        history.pushState(null, null, href);
      } else {
        location.hash = href;
      }
    }
  });
};

/**
 * React hook for GSAP smooth scrolling
 *
 * @param {Object} options - Options for the smooth scroll
 * @returns {Function} - Function to call for smooth scrolling
 */
export const useGsapSmoothScroll = (options = {}) => {
  return (target) => gsapScrollTo(target, options);
};

/**
 * Initialize GSAP smooth scrolling for the entire page
 * This creates a smooth scrolling effect for all scrolling, not just anchor links
 *
 * @param {Object} options - Options for the smooth scroll
 * @param {string} options.trigger - The element to use as the trigger (default: 'body')
 * @param {string} options.start - The start position (default: 'top top')
 * @param {string} options.end - The end position (default: 'bottom bottom')
 * @param {string} options.scrub - The scrub value (default: true)
 */
export const initGsapSmoothScrolling = (options = {}) => {
  // This function would typically use ScrollTrigger plugin
  // For now, we'll just initialize the anchor links
  initGsapSmoothScrollAnchors(options);
};

export default gsapScrollTo;
