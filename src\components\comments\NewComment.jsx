import { useState } from 'react';
import { Send, Loader2, <PERSON><PERSON><PERSON>riangle } from 'lucide-react';
import { addComment } from '@/api/comments';
import { toast } from 'sonner';

const NewComment = ({ animeId, episodeNumber, animeTitle, animeBanner, user, onCommentAdded }) => {
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasSpoiler, setHasSpoiler] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!content.trim()) {
      toast.error('Comment cannot be empty');
      return;
    }

    try {
      setIsSubmitting(true);

      const commentData = {
        animeId,
        animeTitle: animeTitle || 'Unknown Anime',
        animeBanner: animeBanner || '',
        episodeNumber,
        userId: user.id,
        userName: user.name,
        userAvatar: user.avatar?.medium || '',
        content: content.trim(),
        hasSpoiler: hasSpoiler
      };

      const newComment = await addComment(commentData);
      onCommentAdded(newComment);
      setContent('');
      toast.success('Comment added successfully');
    } catch (error) {
      toast.error('Failed to add comment. Please try again.');
      console.error('Error adding comment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white/5 hover:bg-white/8 transition-colors duration-200 rounded-xl p-3 border border-white/10">
      <div className="flex gap-3">
        <div className="shrink-0">
          <img
            src={user.avatar?.medium || 'https://i.imgur.com/6VBx3io.png'}
            alt={user.name}
            className="w-8 h-8 rounded-full object-cover border border-blue-500/30 shadow-sm"
          />
        </div>

        <form onSubmit={handleSubmit} className="flex-1">
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Share your thoughts on this episode..."
            className="w-full bg-white/10 border border-white/20 focus:border-blue-500/50 outline-none rounded-lg p-2 text-sm resize-none min-h-[80px] transition-colors"
            disabled={isSubmitting}
          />

          <div className="flex items-center mt-2">
            <label className="flex items-center gap-2 text-xs cursor-pointer">
              <input
                type="checkbox"
                checked={hasSpoiler}
                onChange={() => setHasSpoiler(!hasSpoiler)}
                className="rounded text-blue-500 focus:ring-blue-500 h-4 w-4"
              />
              <div className="flex items-center gap-1.5">
                <AlertTriangle size={14} className="text-yellow-500" />
                <span>Mark as spoiler</span>
              </div>
            </label>
          </div>

          <div className="flex justify-between items-center mt-3">
            <div className="text-xs text-gray-400">
              Be respectful and avoid unmarked spoilers
            </div>
            <button
              type="submit"
              disabled={isSubmitting || !content.trim()}
              className={`flex items-center gap-2 px-5 py-2 rounded-lg text-sm font-medium transition-colors ${
                isSubmitting || !content.trim()
                  ? 'bg-white/10 text-gray-400 cursor-not-allowed'
                  : hasSpoiler
                    ? 'bg-yellow-600 hover:bg-yellow-700 text-white'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {isSubmitting ? (
                <>
                  <Loader2 size={16} className="animate-spin" />
                  Submitting...
                </>
              ) : (
                <>
                  {hasSpoiler && <AlertTriangle size={16} className="mr-1" />}
                  <Send size={16} />
                  Comment
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NewComment;
