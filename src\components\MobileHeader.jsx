import React from 'react';
import { Menu, Search, RotateCcw, Settings } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useMainContext } from '@/context/MainContext';
import NotificationIcon from './notifications/NotificationIcon';

const MobileHeader = ({ toggleSidebar, isSidebarOpen }) => {

  return (
    <header className="fixed top-0 left-0 right-0 h-12 bg-black/90 backdrop-blur-md z-40 border-b border-white/10 lg:hidden">
      <div className="flex items-center h-full px-3 relative">
        {/* Left side */}
        <div className="flex items-center z-10">
          <button
            onClick={toggleSidebar}
            className="flex items-center justify-center w-8 h-8 text-white"
            aria-label="Toggle sidebar"
          >
            <Menu size={20} />
          </button>

          <button
            onClick={() => {
              // Trigger the keyboard shortcut for search
              const keyboardEvent = new KeyboardEvent('keydown', {
                key: '/',
                code: 'Slash',
                keyCode: 191,
                which: 191,
                bubbles: true,
                cancelable: true
              });
              document.dispatchEvent(keyboardEvent);
            }}
            className="flex items-center justify-center w-8 h-8 text-white ml-2"
            aria-label="Search"
          >
            <Search size={20} />
          </button>
        </div>

        {/* Center Logo */}
        <div className={`absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transition-all duration-300 ${isSidebarOpen ? 'opacity-0 scale-95' : 'opacity-100 scale-100'}`}>
          <Link to="/" className="flex items-center">
            <div className="flex flex-col items-center">
              <span className="font-bold text-white text-lg leading-tight">AnimeHQ</span>
              <span className="text-white/70 text-[10px] -mt-1">Platform</span>
            </div>
          </Link>
        </div>

        {/* Right side */}
        <div className="flex items-center gap-2 ml-auto z-10">
          <Link
            to="/history"
            className="flex items-center justify-center w-8 h-8 text-white"
            aria-label="History"
          >
            <RotateCcw size={18} />
          </Link>

          <div className="relative z-[9999]">
            <NotificationIcon className="w-8 h-8 text-white" size={18} />
          </div>

          <Link
            to="/profile-new"
            className="flex items-center justify-center w-8 h-8 text-white"
            aria-label="Profile"
          >
            <Settings size={18} />
          </Link>
        </div>
      </div>
    </header>
  );
};

export default MobileHeader;
