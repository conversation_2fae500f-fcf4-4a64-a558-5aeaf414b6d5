import { useEffect, useMemo, useState } from "react";
import { Link, useSearchParams } from "react-router-dom";
import { debounce } from "lodash";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getExplore, tmdb } from "@/api/tmdb";
import useFetch from "@/hooks/useFetch";
import { discover_options } from "@/lib/utils";
import AddToList from "@/components/AddToList";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Loader2,
  Star,
  Trash2,
} from "lucide-react";
import Image from "@/components/ui/Image";
import { formatDetails } from "@/api/format.jsx";

const Explore = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [body, setBody] = useState({});
  const type = searchParams.get("type") || "movie";
  const year = searchParams.get("year") || "any";
  const country = searchParams.get("country") || "any";
  const genre = searchParams.get("genre") || "any";
  const studio = searchParams.get("studio") || "any";
  const sort = searchParams.get("sort") || "popularity.desc";
  const page = parseInt(searchParams.get("page") || 1);
  document.title = "Explore Movies & Shows";
  const { data, isLoading } = useFetch({
    key: [`explore${JSON.stringify(body)}`],
    fun: async () => await getExplore(body),
  });

  const filters = useMemo(
    () => [
      { name: "TYPE", options: discover_options?.types },
      {
        name: "GENRE",
        options:
          type === "movie"
            ? discover_options?.genres?.movie
            : discover_options?.genres?.tv,
      },
      { name: "STUDIO", options: discover_options?.studios },
      { name: "YEAR", options: discover_options?.years },
      { name: "COUNTRY", options: discover_options?.country_codes },
      {
        name: "SORT",
        options:
          type === "movie"
            ? discover_options?.sorts?.movie
            : discover_options?.sorts?.tv,
      },
    ],
    [type]
  );

  const updateBody = debounce(() => {
    setBody({
      type,
      genre,
      year,
      sort,
      country,
      studio,
      page,
    });
  }, 500);

  const { results, total_pages } = useMemo(
    () => ({
      results: isLoading
        ? []
        : formatDetails(
            data?.results?.filter((r) => r?.type !== "person") || [],
            type
          ),
      total_pages: parseInt(
        (data?.total_pages || 1) > 500 ? 500 : data?.total_pages || 1
      ),
    }),
    [data, isLoading, type]
  );

  useEffect(() => {
    updateBody();
    return () => updateBody.cancel();
  }, [type, year, genre, country, studio, sort, page]);

  const goToPage = (page) => {
    window.scrollTo({ top: 0, behavior: "smooth" });
    if (page >= 1 && page <= total_pages) {
      setSearchParams((params) => {
        params.set("page", page);
        return params;
      });
    }
  };

  return (
    <div className="w-full py-4 flex flex-col gap-4">
      <div className="flex w-full text-xl md:text-2xl xl:text-3xl font-semibold pl-1">
        Explore
      </div>
      <div className="flex flex-col gap-10 w-full">
        <div className="flex w-full flex-wrap lg:!flex-nowrap items-end">
          {filters.map(({ name, options }) => (
            <div
              key={name}
              className="flex w-1/2 sm:w-1/3 md:w-1/4 lg:w-[19%] p-[.4rem] sm:p-2 shrink-0 flex-col gap-1 !select-none"
            >
              <div className="flex w-full pl-1">{name}</div>
              <Select
                onValueChange={(v) => {
                  searchParams.set(name.toLowerCase(), v);
                  setSearchParams(searchParams);
                }}
                value={
                  searchParams.get(name.toLowerCase()) || options[0]?.value
                }
              >
                <SelectTrigger className="!w-full bg-white/10 rounded-xl overflow-hidden">
                  <SelectValue placeholder={name} />
                </SelectTrigger>
                <SelectContent className="bg-black/80 backdrop-blur-sm p-1 shadow-lg">
                  {options.map(({ name, value }) => (
                    <SelectItem
                      key={value}
                      value={value}
                      className="hover:bg-white/5 rounded-lg overflow-hidden cursor-pointer py-2"
                    >
                      {name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          ))}
          <Link
            to={`/search`}
            className="shrink-0 p-2 bg-white/[.12] rounded-lg overflow-hidden ml-auto mb-2"
          >
            <Trash2 size={23} />
          </Link>
        </div>
        <div className="flex w-full flex-wrap gap-y-4">
          {isLoading ? (
            <span className="text-sm text-gray-300 flex items-center size-full justify-center gap-1 p-10">
              <Loader2 className="animate-spin text-gray-300" size={18} />{" "}
              Loading...
            </span>
          ) : results?.length ? (
            results?.map((w) => (
              <div
                key={`${w?.type}-${w?.id}`}
                className="flex p-[.4rem] sm:p-2 w-1/3 sm:w-1/4 md:w-1/5 lg:w-1/6 xl:w-[14.28%] flex-wrap !select-none shrink-0"
              >
                <div className="flex flex-col group w-full gap-1">
                  <div className="flex w-full aspect-[1/1.45] pp smooth relative bg-white/5 shrink-0 rounded-xl overflow-hidden">
                    <Link
                      to={`/watch/${w?.type}/${w?.id}`}
                      className="size-full"
                    >
                      <Image
                        src={w?.images?.coverSmall || w?.images?.coverLarge}
                      />
                    </Link>
                    <span className="flex flex-col gap-1 absolute items-end text-xs right-1 top-1">
                      {Number(w?.rating) > 0 && (
                        <span className="bg-black/75 p-[.1rem] px-1 gap-1 lg:group-hover:hidden rounded-md flex items-center">
                          <Star fill="gold" color="gold" size={12} />
                          {Number(w?.rating)?.toFixed(1) || "n/a"}
                        </span>
                      )}
                      {/* Removed AddToList */}
                    </span>
                  </div>
                  <Link
                    to={`/watch/${w?.type}/${w?.id}`}
                    className="flex w-full gap-1 shrink-0 flex-col"
                  >
                    <div className="w-full flex text-xs items-center justify-between">
                      <span className="uppercase">{w?.type}</span>
                      <span>
                        {w?.release_date ? w?.release_date.slice(-4) : ""}
                      </span>
                    </div>
                    <div className="line-clamp-2 !leading-tight text-sm lg:text-base tracking-wider">
                      {w?.title}
                    </div>
                  </Link>
                </div>
              </div>
            ))
          ) : (
            <span className="text-sm text-gray-300 flex items-center size-full justify-center gap-1 p-10">
              No Results Found :(
            </span>
          )}
        </div>
        <div className="h-14 flex justify-center items-center my-5 md:my-8">
          <div className="bg-white/5 flex gap-[2px] justify-center items-center rounded-lg overflow-hidden select-none">
            <button
              onClick={() => goToPage(1)}
              disabled={page === 1}
              className={`bg-white/10 p-2 w-14 flex items-center justify-center ${
                page === 1
                  ? "opacity-60"
                  : "hover:bg-white/5 hover:text-[#4aeadc]"
              } `}
            >
              <ChevronsLeft />
            </button>
            <button
              onClick={() => goToPage(page - 1)}
              className={`bg-white/10 p-2 w-12 flex items-center justify-center ${
                page === 1
                  ? "opacity-60"
                  : "hover:bg-white/5 hover:text-[#4aeadc]"
              } `}
              disabled={page === 1}
            >
              <ChevronLeft />
            </button>
            <span className="bg-white/10 p-2 min-w-[2.5rem] text-center select-none">
              {page} - {total_pages}
            </span>
            <button
              onClick={() => goToPage(page + 1)}
              disabled={page === total_pages}
              className={`bg-white/10 p-2 w-12 flex items-center justify-center ${
                page === total_pages
                  ? "opacity-60"
                  : "hover:bg-white/5 hover:text-[#4aeadc]"
              } `}
            >
              <ChevronRight />
            </button>
            <button
              onClick={() => goToPage(total_pages)}
              disabled={page === total_pages}
              className={`bg-white/10 p-2 w-14 flex items-center justify-center ${
                page === total_pages
                  ? "opacity-60"
                  : "hover:bg-white/5 hover:text-[#4aeadc]"
              } `}
            >
              <ChevronsRight />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Explore;
