import { useMemo } from "react";
import { useMainContext } from "./useContexts";
import { toast } from "sonner";

const useHistory = () => {
  const { getStorage, setStorage } = useMainContext();
  const { history, setHistory, refetch } = useMemo(() => {
    return {
      history: getStorage("watch-history") || [],
      setHistory: (h) => {
        try {
          setStorage("watch-history", h);
        } catch (e) {
          console.log("set-history", e?.message);
        }
      },
      refetch: () =>
        setStorage("watch-history", getStorage("watch-history") || []),
    };
  });

  const updateHistory = (op, item) => {
    try {
      let list = getStorage("watch-history") || [];
      if (op === "add") {
        const id = Number(item?.id);
        const {
          type,
          title = "",
          currentTime,
          tagline = null,
          duration,
        } = item;
        const poster = item?.poster || item?.image || "";
        const x = list.findIndex(
          (a) => Number(a?.id) === id && a?.type === type
        );
        if (x !== -1) {
          list[x].hidden = false;
          list[x].dateAdded = Date.now();
          list[x].poster = poster;
          list[x].tagline = tagline || null;
          if (title) list[x].title = title;
          if (type === "movie") {
            list[x].currentTime = currentTime;
            list[x].duration = duration;
          }
          if (type === "tv") {
            list[x].currentEpisode = Number(item.currentEpisode);
            list[x].currentSeason = Number(item.currentSeason);
            const ei = list[x].episodes.findIndex(
              (ep) =>
                ep?.number === item?.currentEpisode &&
                ep?.season === item?.currentSeason
            );
            if (ei !== -1) {
              list[x].episodes[ei].currentTime = currentTime;
            } else {
              let i = {
                number: item?.currentEpisode,
                currentTime: currentTime,
                duration: duration,
              };
              if (type === "tv") i.season = item?.currentSeason;
              list[x].episodes.push(i);
            }
          }
        } else {
          let i = {
            id,
            type,
            poster,
            title,
            tagline: tagline,
            dateAdded: Date.now(),
          };
          if (["tv", "anime"]?.includes(type)) {
            i.currentEpisode = Number(item?.currentEpisode);
            i.episodes = [
              {
                number: item?.currentEpisode,
                currentTime: currentTime,
                duration: duration,
              },
            ];
            if (type === "tv") {
              i.currentSeason = Number(item?.currentSeason);
              i.episodes[0].season = item?.currentSeason;
            }
          }
          if (type === "movie") {
            i.currentTime = currentTime;
            i.duration = duration;
          }
          list.push(i);
        }
        const newH = list
          ?.sort((a, b) => b?.dateAdded - a?.dateAdded)
          ?.slice(0, 500);
        setStorage("watch-history", newH);
        return newH;
      } else if (op === "delete") {
        const newH = list?.filter(
          (a) => !(Number(a?.id) === Number(item?.id) && a?.type === item?.type)
        );
        setStorage("watch-history", newH);
        return newH;
      } else if (op === "clear") {
        localStorage.removeItem("watch-history");
        return [];
      }
    } catch (e) {
      console.log(e?.message);
    }
  };

  const addToHistory = async (d) => {
    try {
      if (!d?.id) return false;
      const h = await updateHistory("add", d);
      if (h) {
        setHistory(h);
        return true;
      } else {
        return false;
      }
    } catch (e) {
      console.log(e?.message);
      return false;
    }
  };
  const removeFromHistory = async (id, type) => {
    toast.promise(updateHistory("delete", { id, type }), {
      loading: "Removing item...",
      success: (h) => {
        setHistory(h);
        return "Watch history updated";
      },
      error: "Something went wrong, please try again",
    });
  };

  const ch = async () => {
    try {
      const h = await updateHistory("clear");
      if (h) {
        setHistory(h);
        return true;
      } else {
        return false;
      }
    } catch (e) {
      console.log(e?.message);
      return false;
    }
  };
  const clearHistory = () => {
    setLoading(true);
    toast.promise(ch(), {
      loading: "Clearing watch history",
      success: (data) => {
        return data
          ? `Watch history cleared succesfully`
          : "Something went wrong, please try again";
      },
      error: "Something went wrong, please try again",
      finally: () => {
        setLoading(false);
      },
    });
  };
  return {
    history,
    setHistory,
    refetch,
    clearHistory,
    removeFromHistory,
    addToHistory,
  };
};

export default useHistory;
