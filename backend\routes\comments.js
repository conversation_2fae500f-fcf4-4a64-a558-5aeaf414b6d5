import express from 'express';
import Comment from '../models/Comment.js';
import mongoose from 'mongoose';

const router = express.Router();

/**
 * @route   GET /api/comments/recent
 * @desc    Get recent comments across all anime episodes
 * @access  Public
 */
router.get('/recent', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;

    // Get recent comments
    const comments = await Comment.find()
      .sort({ createdAt: -1 })
      .limit(limit)
      .lean();

    // The comments should already have animeTitle field from the database
    // But let's make sure they all have a title, using a fallback if needed
    const commentsWithAnimeInfo = comments.map(comment => {
      // Use the stored animeTitle or a fallback
      if (!comment.animeTitle) {
        comment.animeTitle = "Unknown Anime";
      }

      return comment;
    });

    res.json(commentsWithAnimeInfo);
  } catch (error) {
    console.error('Error fetching recent comments:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @route   GET /api/comments/:animeId/:episodeNumber
 * @desc    Get all comments for a specific anime episode
 * @access  Public
 */
router.get('/:animeId/:episodeNumber', async (req, res) => {
  try {
    const { animeId, episodeNumber } = req.params;

    const comments = await Comment.find({
      animeId,
      episodeNumber: Number(episodeNumber)
    }).sort({ createdAt: -1 });

    res.json(comments);
  } catch (error) {
    console.error('Error fetching comments:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @route   POST /api/comments
 * @desc    Add a new comment
 * @access  Private (requires AniList auth)
 */
router.post('/', async (req, res) => {
  try {
    const { animeId, animeTitle, animeBanner, episodeNumber, userId, userName, userAvatar, content, hasSpoiler } = req.body;

    // Validate required fields
    if (!animeId || !episodeNumber || !userId || !userName || !content) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const newComment = new Comment({
      animeId,
      animeTitle: animeTitle || 'Unknown Anime', // Use provided anime title or default
      animeBanner: animeBanner || '', // Use provided anime banner or empty string
      episodeNumber: Number(episodeNumber),
      userId,
      userName,
      userAvatar,
      content,
      hasSpoiler: hasSpoiler || false
    });

    const savedComment = await newComment.save();
    res.status(201).json(savedComment);
  } catch (error) {
    console.error('Error adding comment:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @route   PUT /api/comments/:id
 * @desc    Update a comment
 * @access  Private (requires AniList auth)
 */
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { content, userId, hasSpoiler } = req.body;

    // Find comment
    const comment = await Comment.findById(id);

    // Check if comment exists
    if (!comment) {
      return res.status(404).json({ error: 'Comment not found' });
    }

    // Check if user is the author of the comment
    if (comment.userId !== userId) {
      return res.status(403).json({ error: 'Not authorized to update this comment' });
    }

    // Update comment
    comment.content = content;
    if (hasSpoiler !== undefined) {
      comment.hasSpoiler = hasSpoiler;
    }
    comment.updatedAt = Date.now();

    const updatedComment = await comment.save();
    res.json(updatedComment);
  } catch (error) {
    console.error('Error updating comment:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @route   DELETE /api/comments/:id
 * @desc    Delete a comment
 * @access  Private (requires AniList auth)
 */
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req.body;

    // Find comment
    const comment = await Comment.findById(id);

    // Check if comment exists
    if (!comment) {
      return res.status(404).json({ error: 'Comment not found' });
    }

    // Check if user is the author of the comment
    if (comment.userId !== userId) {
      return res.status(403).json({ error: 'Not authorized to delete this comment' });
    }

    await Comment.findByIdAndDelete(id);
    res.json({ message: 'Comment deleted successfully' });
  } catch (error) {
    console.error('Error deleting comment:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @route   POST /api/comments/:id/like
 * @desc    Like a comment
 * @access  Private (requires AniList auth)
 */
router.post('/:id/like', async (req, res) => {
  try {
    const { id } = req.params;

    // Find and update comment
    const comment = await Comment.findByIdAndUpdate(
      id,
      { $inc: { likes: 1 } },
      { new: true }
    );

    if (!comment) {
      return res.status(404).json({ error: 'Comment not found' });
    }

    res.json(comment);
  } catch (error) {
    console.error('Error liking comment:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @route   POST /api/comments/:id/reply
 * @desc    Add a reply to a comment
 * @access  Private (requires AniList auth)
 */
router.post('/:id/reply', async (req, res) => {
  try {
    const { id } = req.params;
    const { userId, userName, userAvatar, content, hasSpoiler } = req.body;

    // Validate required fields
    if (!userId || !userName || !content) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Create reply object
    const newReply = {
      userId,
      userName,
      userAvatar: userAvatar || '',
      content,
      hasSpoiler: hasSpoiler || false,
      likes: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      _id: new mongoose.Types.ObjectId() // Generate a new ObjectId for the reply
    };

    // Add reply to comment
    const comment = await Comment.findByIdAndUpdate(
      id,
      {
        $push: { replies: newReply },
        updatedAt: new Date()
      },
      { new: true }
    );

    if (!comment) {
      return res.status(404).json({ error: 'Comment not found' });
    }

    res.status(201).json(comment);
  } catch (error) {
    console.error('Error adding reply:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @route   PUT /api/comments/:id/reply/:replyId
 * @desc    Update a reply
 * @access  Private (requires AniList auth)
 */
router.put('/:id/reply/:replyId', async (req, res) => {
  try {
    const { id, replyId } = req.params;
    const { content, userId, hasSpoiler } = req.body;

    // Find comment
    const comment = await Comment.findById(id);

    // Check if comment exists
    if (!comment) {
      return res.status(404).json({ error: 'Comment not found' });
    }

    // Find reply
    const reply = comment.replies.id(replyId);

    // Check if reply exists
    if (!reply) {
      return res.status(404).json({ error: 'Reply not found' });
    }

    // Check if user is the author of the reply
    if (reply.userId !== userId) {
      return res.status(403).json({ error: 'Not authorized to update this reply' });
    }

    // Update reply
    reply.content = content;
    if (hasSpoiler !== undefined) {
      reply.hasSpoiler = hasSpoiler;
    }
    reply.updatedAt = new Date();
    comment.updatedAt = new Date();

    await comment.save();
    res.json(comment);
  } catch (error) {
    console.error('Error updating reply:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @route   DELETE /api/comments/:id/reply/:replyId
 * @desc    Delete a reply
 * @access  Private (requires AniList auth)
 */
router.delete('/:id/reply/:replyId', async (req, res) => {
  try {
    const { id, replyId } = req.params;
    const { userId } = req.body;

    // Find comment
    const comment = await Comment.findById(id);

    // Check if comment exists
    if (!comment) {
      return res.status(404).json({ error: 'Comment not found' });
    }

    // Find reply
    const reply = comment.replies.id(replyId);

    // Check if reply exists
    if (!reply) {
      return res.status(404).json({ error: 'Reply not found' });
    }

    // Check if user is the author of the reply
    if (reply.userId !== userId) {
      return res.status(403).json({ error: 'Not authorized to delete this reply' });
    }

    // Remove reply
    comment.replies.pull(replyId);
    comment.updatedAt = new Date();

    await comment.save();
    res.json({ message: 'Reply deleted successfully' });
  } catch (error) {
    console.error('Error deleting reply:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @route   POST /api/comments/:id/reply/:replyId/like
 * @desc    Like a reply
 * @access  Private (requires AniList auth)
 */
router.post('/:id/reply/:replyId/like', async (req, res) => {
  try {
    const { id, replyId } = req.params;

    // Find comment
    const comment = await Comment.findById(id);

    // Check if comment exists
    if (!comment) {
      return res.status(404).json({ error: 'Comment not found' });
    }

    // Find reply
    const reply = comment.replies.id(replyId);

    // Check if reply exists
    if (!reply) {
      return res.status(404).json({ error: 'Reply not found' });
    }

    // Increment likes
    reply.likes += 1;
    comment.updatedAt = new Date();

    await comment.save();
    res.json(comment);
  } catch (error) {
    console.error('Error liking reply:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

export default router;
