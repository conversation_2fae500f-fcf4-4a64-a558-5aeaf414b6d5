import mongoose from 'mongoose';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// MongoDB connection URI (default to localhost if not provided)
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/animehq-profiles';

// Set up mongoose connection options
const mongooseOptions = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
  socketTimeoutMS: 45000, // Close sockets after 45s of inactivity
  family: 4 // Use IPv4, skip trying IPv6
};

// Connect to MongoDB
const connectDB = async () => {
  try {
    // Check if we're already connected
    if (mongoose.connection.readyState === 1) {
      console.log('MongoDB already connected');
      return mongoose.connection;
    }

    console.log(`Connecting to MongoDB at ${MONGODB_URI.split('@')[1] || 'localhost'}`);
    const conn = await mongoose.connect(MONGODB_URI, mongooseOptions);

    // Set up connection error handler
    mongoose.connection.on('error', err => {
      console.error(`MongoDB connection error: ${err}`);
    });

    // Set up disconnection handler
    mongoose.connection.on('disconnected', () => {
      console.log('MongoDB disconnected, attempting to reconnect...');
      setTimeout(() => {
        connectDB();
      }, 5000);
    });

    console.log(`MongoDB Connected: ${conn.connection.host}`);
    return conn;
  } catch (error) {
    console.error(`Error connecting to MongoDB: ${error.message}`);
    console.log('Will retry connection in 5 seconds...');

    // Instead of exiting, retry the connection after a delay
    setTimeout(() => {
      connectDB();
    }, 5000);

    // Return null to indicate connection failure
    return null;
  }
};

export default connectDB;
