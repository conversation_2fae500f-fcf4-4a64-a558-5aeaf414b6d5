import { useState, useRef, useEffect } from 'react';
import { Heart, Edit, Trash, X, Check, MessageSquare, AlertTriangle, MoreVertical } from 'lucide-react';
import { useAniList } from '@/hooks/useAniList';
import { deleteComment, updateComment, likeComment } from '@/api/comments';
import { toast } from 'sonner';
import { formatTimeAgo } from '@/utils/formatDate';
import ReplyItem from './ReplyItem';
import NewReply from './NewReply';
import SpoilerContent from './SpoilerContent';

const CommentItem = ({ comment, onCommentUpdated, onCommentDeleted }) => {
  const { user, isAuthenticated } = useAniList();
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(comment.content);
  const [isLiking, setIsLiking] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  const optionsRef = useRef(null);
  const [likes, setLikes] = useState(comment.likes || 0);
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [replies, setReplies] = useState(comment.replies || []);
  const [hasSpoiler, setHasSpoiler] = useState(comment.hasSpoiler || false);
  const [showReplies, setShowReplies] = useState(false);

  // For testing purposes, always consider the user as the author
  const isAuthor = true; // user && user.id === comment.userId;
  const formattedDate = formatTimeAgo(comment.createdAt);

  const handleLike = async () => {
    if (isLiking) return;

    try {
      setIsLiking(true);
      const updatedComment = await likeComment(comment._id);
      setLikes(updatedComment.likes);
      onCommentUpdated(updatedComment);
    } catch (error) {
      toast.error('Failed to like comment');
    } finally {
      setIsLiking(false);
    }
  };

  // Handle click outside to close options menu
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (optionsRef.current && !optionsRef.current.contains(event.target)) {
        setShowOptions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [optionsRef]);

  const handleEdit = () => {
    setIsEditing(true);
    setShowOptions(false);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditContent(comment.content);
  };

  const handleSaveEdit = async () => {
    if (!editContent.trim()) {
      toast.error('Comment cannot be empty');
      return;
    }

    try {
      // For testing, use the comment's original userId instead of the current user's ID
      const updatedComment = await updateComment(comment._id, {
        content: editContent,
        userId: comment.userId, // Use the original comment's userId
        hasSpoiler: hasSpoiler
      });

      setIsEditing(false);
      onCommentUpdated(updatedComment);
      toast.success('Comment updated');
    } catch (error) {
      console.error('Error updating comment:', error);
      toast.error('Failed to update comment');
    }
  };

  const toggleSpoiler = () => {
    setHasSpoiler(!hasSpoiler);
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this comment?')) return;

    try {
      // For testing, use the comment's original userId instead of the current user's ID
      await deleteComment(comment._id, comment.userId);
      onCommentDeleted(comment._id);
      toast.success('Comment deleted');
    } catch (error) {
      console.error('Error deleting comment:', error);
      toast.error('Failed to delete comment');
    }
  };

  const handleReplyClick = () => {
    if (!isAuthenticated) {
      toast.error('Please log in to reply to comments');
      return;
    }
    setShowReplyForm(true);
    // Also show the replies when replying
    setShowReplies(true);
  };

  const toggleReplies = () => {
    setShowReplies(!showReplies);
  };

  const handleReplyAdded = (updatedComment) => {
    setReplies(updatedComment.replies || []);
    onCommentUpdated(updatedComment);
  };

  const handleReplyUpdated = (updatedComment) => {
    setReplies(updatedComment.replies || []);
    onCommentUpdated(updatedComment);
  };

  const handleReplyDeleted = (replyId) => {
    setReplies(replies.filter(reply => reply._id !== replyId));
  };

  return (
    <div className="flex flex-col">
      <div className="flex gap-3 p-3 bg-white/5 hover:bg-white/8 transition-colors duration-200 rounded-xl border border-white/10">
        <div className="shrink-0">
          <img
            src={comment.userAvatar || 'https://i.imgur.com/6VBx3io.png'}
            alt={comment.userName}
            className="w-8 h-8 rounded-full object-cover border border-blue-500/30 shadow-sm"
          />
        </div>

        <div className="flex-1">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="font-semibold text-sm text-white">{comment.userName}</span>
              <span className="text-xs text-gray-400 italic">{formattedDate}</span>
            </div>

            <div className="flex items-center gap-2">

              {user && (
                <div className="relative" ref={optionsRef}>
                  <button
                    onClick={() => setShowOptions(!showOptions)}
                    className="p-1.5 hover:bg-white/10 rounded-full transition-colors"
                  >
                    <MoreVertical size={16} className="text-gray-400" />
                  </button>

                  {showOptions && (
                    <div className="absolute right-0 top-full mt-1 bg-gray-800/90 backdrop-blur-sm rounded-lg shadow-xl z-10 py-1 w-32 border border-white/10">
                      <>
                        <button
                          onClick={handleEdit}
                          className="flex items-center gap-2 w-full px-3 py-2 hover:bg-white/10 text-left text-sm transition-colors"
                        >
                          <Edit size={14} /> Edit
                        </button>
                        <button
                          onClick={handleDelete}
                          className="flex items-center gap-2 w-full px-3 py-2 hover:bg-red-500/20 text-left text-sm text-red-400 transition-colors"
                        >
                          <Trash size={14} /> Delete
                        </button>
                      </>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {isEditing ? (
            <div className="mt-3">
              <textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                className="w-full bg-white/10 border border-white/20 focus:border-blue-500/50 outline-none rounded-lg p-3 text-sm resize-none min-h-[100px] transition-colors"
                placeholder="Edit your comment..."
              />

              <div className="flex items-center mt-2">
                <label className="flex items-center gap-2 text-xs cursor-pointer">
                  <input
                    type="checkbox"
                    checked={hasSpoiler}
                    onChange={toggleSpoiler}
                    className="rounded text-blue-500 focus:ring-blue-500 h-3.5 w-3.5"
                  />
                  <div className="flex items-center gap-1.5">
                    <AlertTriangle size={14} className="text-yellow-500" />
                    <span>Mark as spoiler</span>
                  </div>
                </label>
              </div>

              <div className="flex justify-end gap-2 mt-3">
                <button
                  onClick={handleCancelEdit}
                  className="flex items-center gap-1.5 px-4 py-1.5 bg-white/10 hover:bg-white/15 rounded-lg text-xs font-medium transition-colors"
                >
                  <X size={14} /> Cancel
                </button>
                <button
                  onClick={handleSaveEdit}
                  className="flex items-center gap-1.5 px-4 py-1.5 bg-blue-600 hover:bg-blue-700 rounded-lg text-xs font-medium transition-colors"
                >
                  <Check size={14} /> Save
                </button>
              </div>
            </div>
          ) : (
            <>
              {comment.hasSpoiler ? (
                <SpoilerContent content={comment.content} />
              ) : (
                <p className="text-sm mt-1 leading-relaxed">{comment.content}</p>
              )}

              <div className="flex items-center gap-3 mt-2">
                <button
                  onClick={handleLike}
                  disabled={isLiking}
                  className={`flex items-center gap-1.5 text-xs font-medium px-2 py-1 rounded-md transition-all ${
                    likes > 0
                      ? 'text-pink-400 bg-pink-500/10'
                      : 'text-gray-300 hover:text-pink-400 hover:bg-pink-500/10'
                  }`}
                >
                  <Heart size={14} className={likes > 0 ? 'fill-pink-400 text-pink-400' : ''} />
                  {likes > 0 ? likes : 'Like'}
                </button>

                <button
                  onClick={handleReplyClick}
                  className="flex items-center gap-1.5 text-xs font-medium px-2 py-1 text-gray-300 hover:text-blue-400 hover:bg-blue-500/10 rounded-md transition-all"
                >
                  <MessageSquare size={14} />
                  Reply
                </button>

                {replies.length > 0 && (
                  <button
                    onClick={toggleReplies}
                    className="flex items-center gap-1.5 text-xs font-medium px-2 py-1 text-gray-400 hover:text-blue-400 hover:bg-blue-500/10 rounded-md transition-all"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className={`transition-transform ${showReplies ? 'rotate-90' : ''}`}
                    >
                      <polyline points="9 18 15 12 9 6"></polyline>
                    </svg>
                    {replies.length} {replies.length === 1 ? 'reply' : 'replies'}
                  </button>
                )}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Reply Form */}
      {showReplyForm && user && (
        <NewReply
          commentId={comment._id}
          user={user}
          onReplyAdded={handleReplyAdded}
          onCancel={() => setShowReplyForm(false)}
        />
      )}

      {/* Replies Content */}
      {replies.length > 0 && showReplies && (
        <div className="flex flex-col gap-2 mt-1 transition-all">
          {replies.map(reply => (
            <ReplyItem
              key={reply._id}
              commentId={comment._id}
              reply={reply}
              onReplyUpdated={handleReplyUpdated}
              onReplyDeleted={handleReplyDeleted}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default CommentItem;
