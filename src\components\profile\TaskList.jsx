import { useState, useEffect } from "react";
import { useTaskContext } from "@/context/TaskContext";
import { useAniList } from "@/hooks/useAniList";
import { isTaskLocked } from "@/utils/taskVerification";
import {
  Calendar,
  Play,
  Plus,
  Star,
  RefreshCw,
  Layers,
  Zap,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Trophy,
  XCircle,
  Bug,
  AlertCircle,
  Lock
} from "lucide-react";

const TaskList = () => {
  const { dailyTasks, completeTask, getTaskProgress, getTotalAvailableXp, getEarnedXp, debugTaskVerification, debugSetTaskFlag } = useTaskContext();
  const { isAuthenticated, user } = useAniList();
  const [expanded, setExpanded] = useState(true);
  const [showDebug, setShowDebug] = useState(false);

  // Force check for completed tasks on every render
  useEffect(() => {
    if (isAuthenticated && user?.id) {
      // Get today's date in YYYY-MM-DD format
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const dateKey = today.toISOString().split('T')[0];

      // Check each task for a completion record
      dailyTasks.forEach(task => {
        if (!task.isCompleted) {
          const completionKey = `task-completion-${user.id}-${task.id}`;
          const completionRecord = localStorage.getItem(completionKey);

          if (completionRecord) {
            try {
              const record = JSON.parse(completionRecord);
              // If the record is from today, the task should be completed
              if (record.dateKey === dateKey) {
                console.log(`Found completion record for task ${task.id} from today, completing task`);
                // Complete the task
                completeTask(task.id);
              }
            } catch (error) {
              console.error(`Error parsing completion record for task ${task.id}:`, error);
            }
          }
        }
      });
    }
  }, [isAuthenticated, user, dailyTasks, completeTask]);

  // Get task icon based on icon name
  const getTaskIcon = (iconName, completed) => {
    const iconProps = {
      size: 16,
      className: completed ? "text-green-500" : "text-white/60"
    };

    switch (iconName) {
      case "calendar": return <Calendar {...iconProps} />;
      case "play": return <Play {...iconProps} />;
      case "plus": return <Plus {...iconProps} />;
      case "star": return <Star {...iconProps} />;
      case "refresh": return <RefreshCw {...iconProps} />;
      case "layers": return <Layers {...iconProps} />;
      case "zap": return <Zap {...iconProps} />;
      case "check-circle": return <CheckCircle {...iconProps} />;
      default: return <Calendar {...iconProps} />;
    }
  };

  // Get difficulty badge color
  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case "easy": return "bg-green-500/20 text-green-400";
      case "medium": return "bg-yellow-500/20 text-yellow-400";
      case "hard": return "bg-red-500/20 text-red-400";
      default: return "bg-blue-500/20 text-blue-400";
    }
  };

  // Filter tasks into regular and bonus categories
  const regularTasks = dailyTasks.filter(task => !task.isBonus);
  const bonusTasks = dailyTasks.filter(task => task.isBonus);

  // Calculate progress
  const progress = getTaskProgress();
  const totalXp = getTotalAvailableXp();
  const earnedXp = getEarnedXp();

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm text-white/80 flex items-center gap-1">
          <Trophy size={14} className="text-yellow-500" />
          Daily Tasks
        </h3>
        <button
          onClick={() => setExpanded(!expanded)}
          className="text-white/60 hover:text-white"
        >
          {expanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
        </button>
      </div>

      {/* Progress bar */}
      <div className="bg-black/30 rounded-lg p-3 mb-3">
        <div className="flex justify-between items-center mb-1">
          <span className="text-xs text-white/80">Daily Progress</span>
          <span className="text-xs text-white/60">{progress}%</span>
        </div>
        <div className="w-full h-1.5 bg-black/50 rounded-full overflow-hidden">
          <div
            className="h-full bg-blue-500 rounded-full"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <div className="flex justify-between items-center mt-1">
          <span className="text-[10px] text-white/60">
            {earnedXp} / {totalXp} XP earned
          </span>
        </div>
      </div>

      {expanded && (
        <div className="space-y-2">
          {/* Regular tasks */}
          {regularTasks.map(task => (
            <TaskItem
              key={task.id}
              task={task}
              onComplete={completeTask}
              getTaskIcon={getTaskIcon}
              getDifficultyColor={getDifficultyColor}
            />
          ))}

          {/* Bonus tasks */}
          {bonusTasks.map(task => (
            <TaskItem
              key={task.id}
              task={task}
              onComplete={completeTask}
              getTaskIcon={getTaskIcon}
              getDifficultyColor={getDifficultyColor}
              isBonus
            />
          ))}

          {/* Debug section - only visible in development */}
          {process.env.NODE_ENV !== 'production' && isAuthenticated && (
            <div className="mt-4">
              <button
                onClick={() => setShowDebug(!showDebug)}
                className="text-xs text-white/60 hover:text-white flex items-center gap-1 bg-black/30 hover:bg-black/50 px-3 py-1.5 rounded-full transition-colors w-full justify-center border border-white/10"
              >
                <Bug size={12} />
                {showDebug ? 'Hide Debug Tools' : 'Show Debug Tools'}
              </button>

              {showDebug && (
                <div className="mt-2 p-3 bg-black/30 border border-white/10 rounded-lg">
                  <h4 className="text-xs font-medium flex items-center gap-1 mb-2">
                    <AlertCircle size={12} className="text-yellow-400" />
                    Task Verification Debug Tools
                  </h4>

                  <div className="grid grid-cols-2 gap-2">
                    <button
                      onClick={debugTaskVerification}
                      className="text-xs bg-blue-900/30 hover:bg-blue-900/50 px-2 py-1.5 rounded border border-blue-500/30 transition-colors"
                    >
                      Check Verification Status
                    </button>

                    <button
                      onClick={() => debugSetTaskFlag('watchedEpisode')}
                      className="text-xs bg-green-900/30 hover:bg-green-900/50 px-2 py-1.5 rounded border border-green-500/30 transition-colors"
                    >
                      Set Watch Episode Flag
                    </button>

                    <button
                      onClick={() => debugSetTaskFlag('addedAnime')}
                      className="text-xs bg-purple-900/30 hover:bg-purple-900/50 px-2 py-1.5 rounded border border-purple-500/30 transition-colors"
                    >
                      Set Add Anime Flag
                    </button>

                    <button
                      onClick={() => debugSetTaskFlag('ratedAnime')}
                      className="text-xs bg-yellow-900/30 hover:bg-yellow-900/50 px-2 py-1.5 rounded border border-yellow-500/30 transition-colors"
                    >
                      Set Rate Anime Flag
                    </button>

                    <button
                      onClick={() => debugSetTaskFlag('updatedProgress')}
                      className="text-xs bg-orange-900/30 hover:bg-orange-900/50 px-2 py-1.5 rounded border border-orange-500/30 transition-colors"
                    >
                      Set Update Progress Flag
                    </button>

                    <button
                      onClick={() => debugSetTaskFlag('exploredGenres')}
                      className="text-xs bg-pink-900/30 hover:bg-pink-900/50 px-2 py-1.5 rounded border border-pink-500/30 transition-colors"
                    >
                      Set Explore Genres Flag
                    </button>

                    <button
                      onClick={() => debugSetTaskFlag('watchedSeasonal')}
                      className="text-xs bg-teal-900/30 hover:bg-teal-900/50 px-2 py-1.5 rounded border border-teal-500/30 transition-colors col-span-2"
                    >
                      Set Watch Seasonal Flag
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Individual task item component
const TaskItem = ({ task, onComplete, getTaskIcon, getDifficultyColor, isBonus = false }) => {
  const { user, isAuthenticated } = useAniList();
  // Use useState to ensure the component re-renders when task completion status changes
  const [completed, setCompleted] = useState(task.isCompleted === true);
  // Track if the task is locked (already completed today and can't be changed)
  const [locked, setLocked] = useState(false);

  // Check for completion record and locked status on mount and when task changes
  useEffect(() => {
    // First check if the task is already marked as completed in the task object
    if (task.isCompleted === true) {
      setCompleted(true);
    }

    // Check if the task is locked (already completed today)
    if (isAuthenticated && user?.id) {
      const taskLocked = isTaskLocked(user.id, task.id);
      setLocked(taskLocked);

      // If the task is locked, it should also be marked as completed
      if (taskLocked) {
        setCompleted(true);
      }

      // If not locked, check for a completion record in localStorage
      if (!taskLocked) {
        // Get today's date in YYYY-MM-DD format
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const dateKey = today.toISOString().split('T')[0];

        // Check for a completion record
        const completionKey = `task-completion-${user.id}-${task.id}`;
        const completionRecord = localStorage.getItem(completionKey);

        if (completionRecord) {
          try {
            const record = JSON.parse(completionRecord);
            // If the record is from today, mark the task as completed
            if (record.dateKey === dateKey) {
              console.log(`TaskItem: Found completion record for task ${task.id} from today`);
              setCompleted(true);
              setLocked(true);
            }
          } catch (error) {
            console.error(`TaskItem: Error parsing completion record for task ${task.id}:`, error);
          }
        }
      }
    }
  }, [task, isAuthenticated, user]);

  // Update the completed state when task.isCompleted changes
  useEffect(() => {
    if (completed !== (task.isCompleted === true)) {
      setCompleted(task.isCompleted === true);
    }

    // Re-check if the task is locked whenever completion status changes
    if (isAuthenticated && user?.id && completed) {
      const taskLocked = isTaskLocked(user.id, task.id);
      if (taskLocked !== locked) {
        setLocked(taskLocked);
      }
    }
  }, [task.isCompleted, completed, isAuthenticated, user, locked, task.id]);

  // Log the task completion status for debugging
  console.log(`Task ${task.id} isCompleted:`, completed, 'locked:', locked, 'Original:', task.isCompleted);

  return (
    <div
      className={`bg-black/30 border ${completed ? 'border-green-500/30' : 'border-white/10'}
        rounded-lg p-3 flex items-center gap-3 transition-colors
        ${isBonus ? 'bg-yellow-900/10' : ''}
        ${!completed && !isBonus ? 'hover:border-white/20' : ''}`}
      data-task-id={task.id}
      data-completed={completed ? 'true' : 'false'}
    >
      {/* Task icon */}
      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${completed ? 'bg-green-500/20' : 'bg-black/50'}`}>
        {getTaskIcon(task.icon, completed)}
      </div>

      {/* Task details */}
      <div className="flex-1">
        <div className="flex items-center gap-2">
          <h4 className="text-sm font-medium">{task.title}</h4>
          <span className={`text-[10px] px-1.5 py-0.5 rounded-full ${getDifficultyColor(task.difficulty)}`}>
            {task.difficulty}
          </span>
          {isBonus && (
            <span className="text-[10px] bg-yellow-500/20 text-yellow-400 px-1.5 py-0.5 rounded-full">
              BONUS
            </span>
          )}
        </div>
        <p className="text-xs text-white/60 mt-0.5">{task.description}</p>
      </div>

      {/* XP reward and complete button */}
      <div className="flex flex-col items-end gap-1">
        <span className="text-xs text-yellow-400">+{task.xpReward} XP</span>
        {completed ? (
          <span className="text-[10px] text-green-500 flex items-center gap-1">
            {locked ? <Lock size={12} /> : <CheckCircle size={12} />}
            {locked ? "Locked" : "Completed"}
          </span>
        ) : (
          <button
            onClick={() => {
              // Only update UI after the task is actually completed by the verification system
              // Call the complete function which will handle verification
              onComplete(task.id);
            }}
            className="text-[10px] text-white/60 hover:text-white flex items-center gap-1 bg-white/5 hover:bg-white/10 px-2 py-1 rounded-full transition-colors"
            disabled={locked}
          >
            Complete
          </button>
        )}
      </div>
    </div>
  );
};

export default TaskList;
