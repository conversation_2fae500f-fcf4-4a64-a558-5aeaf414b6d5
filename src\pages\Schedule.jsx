import { useState, useEffect } from "react";
import { getAnimeSchedule, groupScheduleByDay } from "../api/animeSchedule";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Calendar,
  Filter,
  Star,
  Clock,
  ChevronLeft,
  ChevronRight,
  CalendarDays,
  ListFilter,
  SortAsc,
  SortDesc,
  Tag,
  X,
  Search,
  Sliders
} from "lucide-react";
import Image from "../components/ui/Image";
import { Link } from "react-router-dom";
import LoadingSpinner from "../components/ui/LoadingSpinner";
import LiveClock from "../components/ui/LiveClock";

const Schedule = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [schedule, setSchedule] = useState({});
  const [loading, setLoading] = useState(true);
  const [activeDay, setActiveDay] = useState("");
  const [airType, setAirType] = useState("sub");
  // Removed calendar view, only using list view now
  const [todayReleases, setTodayReleases] = useState([]);

  // Enhanced filtering options
  const [selectedGenres, setSelectedGenres] = useState([]);
  const [sortOption, setSortOption] = useState("time"); // "time", "title", "score"
  const [sortDirection, setSortDirection] = useState("asc"); // "asc" or "desc"
  const [searchQuery, setSearchQuery] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [availableGenres, setAvailableGenres] = useState([]);

  // Responsive design
  const [isMobile, setIsMobile] = useState(false);

  // Check if device is mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);

    // Cleanup
    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  // Get the current day of the week
  const getCurrentDay = () => {
    const days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
    const dayIndex = new Date().getDay();
    return days[dayIndex];
  };

  // Order days of the week starting with today
  const getOrderedDays = () => {
    const currentDay = getCurrentDay();
    const weekDays = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];

    // Find the index of the current day
    const currentIndex = weekDays.indexOf(currentDay);

    // Reorder the days starting with the current day
    const orderedDays = [
      ...weekDays.slice(currentIndex),
      ...weekDays.slice(0, currentIndex)
    ];

    // Add "Unknown" at the end if needed
    if (schedule && schedule["Unknown"] && schedule["Unknown"].length > 0) {
      orderedDays.push("Unknown");
    }

    return orderedDays;
  };

  // Parse query parameters
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const day = params.get("day");
    const type = params.get("type");
    const genres = params.get("genres");
    const sort = params.get("sort");
    const direction = params.get("direction");
    const query = params.get("query");

    if (day) {
      setActiveDay(day);
    } else {
      setActiveDay(getCurrentDay());
    }

    if (type && ["sub", "dub", "raw", "all"].includes(type)) {
      setAirType(type);
    }

    if (genres) {
      setSelectedGenres(genres.split(","));
    }

    if (sort && ["time", "title", "score"].includes(sort)) {
      setSortOption(sort);
    }

    if (direction && ["asc", "desc"].includes(direction)) {
      setSortDirection(direction);
    }

    if (query) {
      setSearchQuery(query);
    }
  }, [location.search]);

  // Update active day if the current one has no anime
  useEffect(() => {
    if (schedule && Object.keys(schedule).length > 0) {
      // If the current active day has no anime or doesn't exist
      if (!schedule[activeDay] || schedule[activeDay].length === 0) {
        // Find the first day with anime
        const orderedDays = getOrderedDays();
        for (const day of orderedDays) {
          if (schedule[day] && schedule[day].length > 0) {
            setActiveDay(day);
            break;
          }
        }
      }
    }
  }, [schedule, activeDay]);

  // Fetch schedule data
  useEffect(() => {
    const fetchSchedule = async () => {
      setLoading(true);
      console.log("Fetching schedule data with airType:", airType);

      try {
        const data = await getAnimeSchedule(airType);
        console.log("Raw schedule data:", data);

        const groupedData = groupScheduleByDay(data);
        console.log("Grouped schedule data:", groupedData);

        // Make sure all days have at least an empty array
        const days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday", "Unknown"];
        days.forEach(day => {
          if (!groupedData[day]) {
            groupedData[day] = [];
          }
        });

        setSchedule(groupedData);

        // Extract today's releases for the timeline view
        const todayAnimeReleases = extractTodayReleases(groupedData);
        setTodayReleases(todayAnimeReleases);

        // Extract all available genres from the schedule data
        const genres = extractAvailableGenres(data);
        setAvailableGenres(genres);
      } catch (error) {
        console.error("Error in schedule component:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchSchedule();
  }, [airType]);

  // Extract today's releases and organize them by time
  const extractTodayReleases = (scheduleData) => {
    const today = getCurrentDay();
    const todayAnime = scheduleData[today] || [];

    // Sort by airing time
    return todayAnime.sort((a, b) => {
      const timeA = new Date(a.episodeDate).getTime();
      const timeB = new Date(b.episodeDate).getTime();
      return timeA - timeB;
    });
  };

  // Week dates function removed

  // Format time for timeline
  const formatTimeForTimeline = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Extract all available genres from the schedule data
  const extractAvailableGenres = (scheduleData) => {
    const genreSet = new Set();

    // Iterate through all anime in the schedule
    scheduleData.forEach(anime => {
      if (anime.genres && Array.isArray(anime.genres)) {
        anime.genres.forEach(genre => {
          genreSet.add(genre);
        });
      }
    });

    // Convert Set to Array and sort alphabetically
    return Array.from(genreSet).sort();
  };

  // Update URL when filters change
  const updateFilters = (options = {}) => {
    const {
      day = activeDay,
      type = airType,
      genres = selectedGenres,
      sort = sortOption,
      direction = sortDirection,
      query = searchQuery
    } = options;

    const params = new URLSearchParams();
    if (day) params.set("day", day);
    if (type) params.set("type", type);
    if (genres && genres.length > 0) params.set("genres", genres.join(","));
    if (sort) params.set("sort", sort);
    if (direction) params.set("direction", direction);
    if (query) params.set("query", query);

    navigate(`/schedule?${params.toString()}`);
  };

  // Handle day change
  const handleDayChange = (day) => {
    updateFilters({ day });
  };

  // Handle air type change
  const handleAirTypeChange = (type) => {
    updateFilters({ type });
  };

  // Removed view mode change handler

  // Handle genre selection
  const handleGenreToggle = (genre) => {
    const newSelectedGenres = selectedGenres.includes(genre)
      ? selectedGenres.filter(g => g !== genre)
      : [...selectedGenres, genre];

    setSelectedGenres(newSelectedGenres);
    updateFilters({ genres: newSelectedGenres });
  };

  // Handle sort option change
  const handleSortChange = (option) => {
    setSortOption(option);
    updateFilters({ sort: option });
  };

  // Handle sort direction change
  const handleSortDirectionToggle = () => {
    const newDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    setSortDirection(newDirection);
    updateFilters({ direction: newDirection });
  };

  // Handle search query change
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  // Handle search submit
  const handleSearchSubmit = (e) => {
    e.preventDefault();
    updateFilters({ query: searchQuery });
  };

  // Clear all filters
  const clearAllFilters = () => {
    setSelectedGenres([]);
    setSortOption("time");
    setSortDirection("asc");
    setSearchQuery("");
    updateFilters({
      genres: [],
      sort: "time",
      direction: "asc",
      query: ""
    });
  };

  // Week navigation removed

  // Get image URL - directly use the URL from Jikan API
  const getImageUrl = (imageUrl) => {
    if (!imageUrl) return "";
    return imageUrl;
  };

  // Filter and sort anime list based on selected filters
  const getFilteredAndSortedAnime = (animeList) => {
    if (!animeList || !Array.isArray(animeList)) return [];

    // Apply genre filter
    let filtered = animeList;
    if (selectedGenres.length > 0) {
      filtered = filtered.filter(anime => {
        if (!anime.genres || !Array.isArray(anime.genres)) return false;
        return selectedGenres.some(genre => anime.genres.includes(genre));
      });
    }

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(anime =>
        anime.title.toLowerCase().includes(query) ||
        (anime.genres && anime.genres.some(genre => genre.toLowerCase().includes(query)))
      );
    }

    // Apply sorting
    return filtered.sort((a, b) => {
      if (sortOption === "time") {
        const timeA = new Date(a.episodeDate).getTime();
        const timeB = new Date(b.episodeDate).getTime();
        return sortDirection === "asc" ? timeA - timeB : timeB - timeA;
      } else if (sortOption === "title") {
        return sortDirection === "asc"
          ? a.title.localeCompare(b.title)
          : b.title.localeCompare(a.title);
      } else if (sortOption === "score") {
        const scoreA = parseFloat(a.score) || 0;
        const scoreB = parseFloat(b.score) || 0;
        return sortDirection === "asc" ? scoreA - scoreB : scoreB - scoreA;
      }
      return 0;
    });
  };

  // Set document title
  useEffect(() => {
    document.title = "Anime Schedule - AnimeHQ";
  }, []);

  // Render anime card (used in both views)
  const renderAnimeCard = (anime) => (
    <div
      key={`${anime.route}`}
      className="relative rounded-lg overflow-hidden flex hover:scale-[1.01] transition-all duration-300 border-l-4 border-white/50 group"
    >
      {/* Blurred background */}
      <div className="absolute inset-0 z-0">
        <Image
          src={getImageUrl(anime.imageVersionRoute)}
          alt={anime.title}
          className="!object-cover !w-full !h-full blur-sm opacity-40 scale-110"
          quality="low"
          loading="lazy"
        />
        <div className="absolute inset-0 bg-black/60 group-hover:bg-black/70 transition-colors duration-300"></div>
      </div>

      {/* Rank number or episode number */}
      <div className="flex items-center justify-center w-10 bg-white/80 text-black font-bold relative z-10">
        #{anime.episodeNumber}
      </div>

      {/* Poster image */}
      <div className="w-16 h-24 shrink-0 overflow-hidden relative z-10 ml-1">
        <Image
          src={getImageUrl(anime.imageVersionRoute)}
          alt={anime.title}
          className="!object-cover !w-full !h-full"
          quality="high"
          loading="lazy"
        />
      </div>

      {/* Content */}
      <div className="p-3 flex flex-col flex-1 relative z-10">
        <div className="flex items-start justify-between gap-2">
          <h3 className="font-semibold line-clamp-2">{anime.title || ""}</h3>
          {anime.airingStatus && (
            <span
              className={`text-xs px-2 py-0.5 rounded-full shrink-0 ${
                anime.airingStatus === "aired"
                  ? "bg-green-500/30 text-green-300 border border-green-500/30"
                  : anime.airingStatus === "airing"
                  ? "bg-yellow-500/30 text-yellow-300 border border-yellow-500/30"
                  : "bg-gray-500/30 text-gray-300 border border-gray-500/30"
              }`}
            >
              {anime.airingStatus}
            </span>
          )}
        </div>

        <div className="mt-1 text-sm text-white">
          Episode {anime.episodeNumber} {anime.totalEpisodes !== "?" && `of ${anime.totalEpisodes}`}
        </div>

        <div className="flex flex-wrap items-center gap-2 mt-1">
          {anime.type && (
            <span className="text-xs bg-black/50 border border-white/30 text-white rounded-full px-2 py-0.5 capitalize">
              {typeof anime.type === 'string' ? anime.type.toLowerCase() : anime.type}
            </span>
          )}

          {anime.episodeDate && (
            <span className="text-xs bg-black/50 border border-white/30 text-white rounded-full px-2 py-0.5 flex items-center gap-1">
              <Calendar size={10} />
              {new Date(anime.episodeDate).toLocaleString(undefined, {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </span>
          )}

          {anime.score && (
            <span className="text-xs bg-white/20 px-2 py-0.5 rounded-full flex items-center gap-1">
              <Star size={10} fill="#FFFFFF" color="#FFFFFF" />
              <span className="font-bold text-white">{anime.score}</span>
            </span>
          )}
        </div>

        {anime.genres && anime.genres.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {anime.genres.slice(0, 3).map(genre => (
              <span key={genre} className="text-[10px] px-1.5 py-0.5 bg-black/50 border border-white/30 text-white rounded-full">
                {genre}
              </span>
            ))}
          </div>
        )}

        <div className="mt-auto pt-2 flex gap-2">
          <a
            href={anime.url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-xs bg-black/50 border border-white/30 text-white px-2 py-0.5 rounded-full hover:bg-white/20 transition-colors"
          >
            AniList
          </a>
          <Link
            to={`/anime/${anime.route}`}
            className="text-xs bg-white/80 text-black font-medium px-2 py-0.5 rounded-full ml-auto hover:bg-white transition-colors"
          >
            Details
          </Link>
        </div>
      </div>
    </div>
  );

  // Calendar view has been removed

  // Render the list view (original view)
  const renderListView = () => {
    const filteredAnime = getFilteredAndSortedAnime(schedule[activeDay] || []);
    const hasActiveFilters = selectedGenres.length > 0 || searchQuery || sortOption !== "time" || sortDirection !== "asc";

    return (
      <div className="flex flex-col gap-4">
        {/* Day selector */}
        <div className="flex flex-wrap gap-2">
          {getOrderedDays().map((day) => {
            // Get a random anime from this day to use as background
            const dayAnime = schedule[day] || [];
            const randomAnime = dayAnime.length > 0 ? dayAnime[Math.floor(Math.random() * dayAnime.length)] : null;

            return (
              <button
                key={day}
                onClick={() => handleDayChange(day)}
                className={`px-4 py-2 rounded-lg flex items-center gap-2 relative overflow-hidden group ${
                  activeDay === day
                    ? "ring-2 ring-white/70"
                    : ""
                }`}
              >
                {/* Blurred background */}
                {randomAnime && (
                  <div className="absolute inset-0 z-0">
                    <Image
                      src={getImageUrl(randomAnime.imageVersionRoute)}
                      alt=""
                      className="!object-cover !w-full !h-full blur-sm opacity-30 scale-110"
                      quality="low"
                      loading="lazy"
                    />
                    <div className={`absolute inset-0 ${
                      activeDay === day
                        ? "bg-white/20"
                        : "bg-black/60 group-hover:bg-black/50"
                    } transition-colors duration-300`}></div>
                  </div>
                )}

                {/* If no anime, use a solid background */}
                {!randomAnime && (
                  <div className={`absolute inset-0 ${
                    activeDay === day
                      ? "bg-white/20"
                      : "bg-black/60 group-hover:bg-black/50"
                  } transition-colors duration-300`}></div>
                )}

                <Calendar size={16} className="relative z-10" />
                <span className="relative z-10 font-medium">{day}</span>
                {schedule[day]?.length > 0 && (
                  <span className="bg-white/80 text-black text-xs px-2 py-0.5 rounded-full font-bold relative z-10">
                    {schedule[day].length}
                  </span>
                )}
              </button>
            );
          })}
        </div>

        {/* Advanced filters section */}
        <div className="bg-white/5 rounded-lg p-4 mb-4">
          <div className="flex flex-col md:flex-row gap-4 items-start">
            {/* Search bar */}
            <form onSubmit={handleSearchSubmit} className="flex-1">
              <div className="relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={handleSearchChange}
                  placeholder="Search anime or genres..."
                  className="w-full bg-white/10 rounded-lg px-4 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                {searchQuery && (
                  <button
                    type="button"
                    onClick={() => {
                      setSearchQuery("");
                      updateFilters({ query: "" });
                    }}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                  >
                    <X size={16} />
                  </button>
                )}
              </div>
            </form>

            {/* Sort options */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-400">Sort by:</span>
              <div className="flex rounded-lg overflow-hidden">
                {[
                  { id: "time", label: "Time" },
                  { id: "title", label: "Title" },
                  { id: "score", label: "Score" }
                ].map(option => (
                  <button
                    key={option.id}
                    onClick={() => handleSortChange(option.id)}
                    className={`px-3 py-1 text-sm ${
                      sortOption === option.id
                        ? "bg-blue-500 text-white"
                        : "bg-white/10 hover:bg-white/20"
                    }`}
                  >
                    {option.label}
                  </button>
                ))}
              </div>

              <button
                onClick={handleSortDirectionToggle}
                className="p-1 rounded-lg bg-white/10 hover:bg-white/20"
                title={sortDirection === "asc" ? "Ascending" : "Descending"}
              >
                {sortDirection === "asc" ? <SortAsc size={18} /> : <SortDesc size={18} />}
              </button>
            </div>

            {/* Toggle filters button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center gap-1 px-3 py-1 rounded-lg ${
                showFilters || selectedGenres.length > 0
                  ? "bg-blue-500 text-white"
                  : "bg-white/10 hover:bg-white/20"
              }`}
            >
              <Sliders size={16} />
              Filters
              {selectedGenres.length > 0 && (
                <span className="bg-white/20 text-white text-xs px-2 py-0.5 rounded-full">
                  {selectedGenres.length}
                </span>
              )}
            </button>

            {/* Clear filters button */}
            {hasActiveFilters && (
              <button
                onClick={clearAllFilters}
                className="flex items-center gap-1 px-3 py-1 rounded-lg bg-red-500/20 text-red-400 hover:bg-red-500/30"
              >
                <X size={16} />
                Clear Filters
              </button>
            )}
          </div>

          {/* Genre filters */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t border-white/10">
              <h3 className="text-sm font-medium mb-2 flex items-center gap-1">
                <Tag size={16} />
                Filter by Genre
              </h3>
              <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto p-1">
                {availableGenres.map(genre => (
                  <button
                    key={genre}
                    onClick={() => handleGenreToggle(genre)}
                    className={`px-2 py-1 text-xs rounded-full ${
                      selectedGenres.includes(genre)
                        ? "bg-blue-500 text-white"
                        : "bg-white/10 hover:bg-white/20"
                    }`}
                  >
                    {genre}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Schedule content */}
        {filteredAnime.length > 0 ? (
          <>
            <div className="flex justify-between items-center mb-2">
              <div className="text-sm text-gray-400">
                Showing {filteredAnime.length} {filteredAnime.length === 1 ? 'anime' : 'anime'}
                {hasActiveFilters && schedule[activeDay]?.length > 0 && ` (filtered from ${schedule[activeDay].length})`}
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredAnime.map((anime) => renderAnimeCard(anime))}
            </div>
          </>
        ) : (
          <div className="relative rounded-lg p-8 text-center overflow-hidden border border-white/30">
            {/* Background with subtle pattern */}
            <div className="absolute inset-0 bg-black/60"></div>

            <div className="relative z-10">
              <div className="inline-block bg-white/20 p-2 rounded-full mb-4">
                <X size={24} className="text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-2">
                {schedule[activeDay]?.length > 0
                  ? "No anime match your filters"
                  : `No anime scheduled for ${activeDay}`}
              </h3>
              <p className="text-gray-300">
                {schedule[activeDay]?.length > 0
                  ? "Try adjusting your filters or selecting a different day."
                  : `There are no ${airType !== "all" ? airType : ""} anime releases scheduled for this day.
                     Try selecting a different day or filter.`}
              </p>
              {hasActiveFilters && (
                <button
                  onClick={clearAllFilters}
                  className="mt-4 px-4 py-2 bg-white/80 text-black font-medium rounded-full hover:bg-white transition-colors"
                >
                  Clear All Filters
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Hero Section with Background */}
      <div className="relative rounded-xl overflow-hidden mb-8">
        {/* Background Image - using a random anime from today */}
        <div className="absolute inset-0 z-0">
          {todayReleases.length > 0 && (
            <Image
              src={getImageUrl(todayReleases[0]?.imageVersionRoute)}
              alt=""
              className="!object-cover !w-full !h-full blur-sm opacity-30 scale-110"
              quality="high"
              loading="lazy"
            />
          )}
          <div className="absolute inset-0 bg-gradient-to-r from-black/90 via-black/70 to-black/90"></div>
        </div>

        {/* Hero Content */}
        <div className="relative z-10 p-6 md:p-8 flex flex-col md:flex-row items-center justify-between">
          <div className="flex flex-col items-center md:items-start text-center md:text-left mb-4 md:mb-0">
            <div className="flex items-center gap-3 mb-2">
              <div className="bg-white/20 p-2 rounded-full">
                <Calendar size={24} className="text-white" />
              </div>
              <h1 className="text-3xl font-bold">Anime Schedule</h1>
            </div>
            <p className="text-gray-300 max-w-md">
              Track upcoming anime episodes and never miss your favorite shows
            </p>
          </div>

          <div className="flex flex-col items-center">
            <LiveClock />
            <div className="mt-2 text-sm text-white">
              {getCurrentDay()}, {new Date().toLocaleDateString(undefined, {month: 'long', day: 'numeric', year: 'numeric'})}
            </div>
          </div>
        </div>

        {/* Air type selector */}
        <div className="relative z-10 flex flex-wrap gap-2 p-4 md:p-6 border-t border-white/20 bg-black/40">
          <div className="flex items-center gap-1 mr-2">
            <Filter size={16} className="text-white" />
            <span className="font-medium">Filter by Type:</span>
          </div>
          {["sub", "dub", "raw", "all"].map((type) => (
            <button
              key={type}
              onClick={() => handleAirTypeChange(type)}
              className={`px-3 py-1 rounded-full capitalize relative overflow-hidden group ${
                airType === type
                  ? "bg-white/80 text-black font-medium"
                  : "bg-black/60 border border-white/30 text-white group-hover:bg-black/50"
              }`}
            >
              <span className="relative z-10">{type}</span>
            </button>
          ))}

          <div className="flex items-center gap-2 ml-auto">
            <span className="text-sm text-gray-300">
              {Object.values(schedule).flat().length} Episodes
            </span>
          </div>
        </div>
      </div>

      {/* Active filters display */}
      {(selectedGenres.length > 0 || searchQuery) && (
        <div className="mb-4 flex flex-wrap gap-2 items-center bg-black/40 p-3 rounded-lg border border-white/20">
          <span className="text-sm text-white font-medium">Active filters:</span>

          {searchQuery && (
            <div className="flex items-center gap-1 bg-black/50 border border-white/30 text-white px-2 py-1 rounded-full text-sm">
              <Search size={14} />
              <span>{searchQuery}</span>
              <button
                onClick={() => {
                  setSearchQuery("");
                  updateFilters({ query: "" });
                }}
                className="ml-1 hover:text-white/70"
              >
                <X size={14} />
              </button>
            </div>
          )}

          {selectedGenres.map(genre => (
            <div key={genre} className="flex items-center gap-1 bg-black/50 border border-white/30 text-white px-2 py-1 rounded-full text-sm">
              <Tag size={14} />
              <span>{genre}</span>
              <button
                onClick={() => handleGenreToggle(genre)}
                className="ml-1 hover:text-white/70"
              >
                <X size={14} />
              </button>
            </div>
          ))}

          <button
            onClick={clearAllFilters}
            className="ml-auto text-xs bg-white/80 text-black font-medium px-2 py-1 rounded-full hover:bg-white transition-colors"
          >
            Clear All
          </button>
        </div>
      )}

      {/* Content */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      ) : (
        renderListView()
      )}
    </div>
  );
};

export default Schedule;