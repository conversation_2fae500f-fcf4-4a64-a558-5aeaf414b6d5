import { useState, useEffect, useMemo, memo } from 'react';
import { Quote, Book, BookText, RefreshCw, Sparkles, Clock, Image as ImageIcon } from 'lucide-react';
import { getRandomAnimeQuote, getRandomBibleVerse, getRandomQuranVerse, getRandomBackground } from '@/api/quotes';

// Memoized button component to prevent unnecessary re-renders
const QuoteTypeButton = memo(({ type, currentType, styles, onClick, icon: Icon, label }) => {
  const isActive = type === currentType;
  return (
    <button
      onClick={() => onClick(type)}
      className={`px-3 py-1.5 rounded-lg flex items-center gap-1.5 text-sm ${
        isActive
          ? `${styles.buttonBgActive} text-white`
          : `bg-black/30 text-white/70 ${styles.buttonBgHover}`
      } border ${isActive ? styles.borderColor : 'border-white/10'}`}
    >
      <Icon size={14} className={isActive ? styles.accentColor : 'text-white/70'} />
      {label}
    </button>
  );
});

const GreetingsSection = memo(() => {
  const [greeting, setGreeting] = useState('');
  const [quoteType, setQuoteType] = useState('anime'); // 'anime', 'bible', or 'quran'
  const [quote, setQuote] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [background, setBackground] = useState('');
  const [loadingBackground, setLoadingBackground] = useState(true);

  // Update greeting and time
  useEffect(() => {
    const updateGreetingAndTime = () => {
      const now = new Date();
      const hour = now.getHours();
      let newGreeting = '';

      if (hour >= 5 && hour < 12) {
        newGreeting = 'Good morning';
      } else if (hour >= 12 && hour < 18) {
        newGreeting = 'Good afternoon';
      } else {
        newGreeting = 'Good evening';
      }

      setGreeting(newGreeting);
      setCurrentTime(now);
    };

    // Update immediately and then every minute
    updateGreetingAndTime();
    const interval = setInterval(updateGreetingAndTime, 60000);

    return () => clearInterval(interval);
  }, []);

  // Fetch quote based on selected type
  useEffect(() => {
    const fetchQuote = async () => {
      setLoading(true);
      try {
        let newQuote;

        switch (quoteType) {
          case 'anime':
            newQuote = await getRandomAnimeQuote();
            break;
          case 'bible':
            newQuote = await getRandomBibleVerse();
            break;
          case 'quran':
            newQuote = await getRandomQuranVerse();
            break;
          default:
            newQuote = await getRandomAnimeQuote();
        }

        setQuote(newQuote);
      } catch (error) {
        console.error('Error fetching quote:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchQuote();
  }, [quoteType]);

  // Fetch background based on selected type
  useEffect(() => {
    const fetchBackground = async () => {
      setLoadingBackground(true);
      try {
        // For anime quotes, use Jikan API to get random anime backgrounds
        // For other quote types, use the static backgrounds
        const bgUrl = await getRandomBackground(quoteType);
        setBackground(bgUrl);
      } catch (error) {
        console.error('Error fetching background:', error);
        // Set fallback backgrounds based on quote type
        const fallbackBgs = {
          anime: 'https://i.imgur.com/8KbJGpK.jpg',
          bible: 'https://i.imgur.com/JFgCUHw.jpg',
          quran: 'https://i.imgur.com/Rl5JnZn.jpg'
        };
        setBackground(fallbackBgs[quoteType] || fallbackBgs.anime);
      } finally {
        setLoadingBackground(false);
      }
    };

    fetchBackground();
  }, [quoteType]);

  // Handle quote type change
  const handleQuoteTypeChange = (type) => {
    if (type !== quoteType) {
      setQuoteType(type);
    }
  };

  // Refresh current quote and background
  const handleRefreshQuote = async () => {
    setLoading(true);
    setLoadingBackground(true);

    try {
      // Fetch new quote - pass true to force bypass cache
      let newQuote;

      switch (quoteType) {
        case 'anime':
          newQuote = await getRandomAnimeQuote(true); // Pass true to bypass cache
          break;
        case 'bible':
          newQuote = await getRandomBibleVerse(true); // Pass true to bypass cache
          break;
        case 'quran':
          newQuote = await getRandomQuranVerse(true); // Pass true to bypass cache
          break;
        default:
          newQuote = await getRandomAnimeQuote(true); // Pass true to bypass cache
      }

      setQuote(newQuote);

      // Fetch new background - pass true to force bypass cache
      const bgUrl = await getRandomBackground(quoteType, true);
      setBackground(bgUrl);
    } catch (error) {
      console.error('Error refreshing content:', error);
    } finally {
      setLoading(false);
      setLoadingBackground(false);
    }
  };

  // Format time as HH:MM
  const formatTime = (date) => {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  };

  // Get accent color based on quote type - memoized to prevent recalculation
  const styles = useMemo(() => {
    // Default styles for each quote type
    const stylesByType = {
      anime: {
        accentColor: 'text-blue-400',
        borderColor: 'border-blue-500/30',
        buttonBgActive: 'bg-blue-500/20',
        buttonBgHover: 'hover:bg-blue-500/10',
        iconColor: 'text-blue-400',
        overlayColor: 'bg-black/70'
      },
      bible: {
        accentColor: 'text-amber-400',
        borderColor: 'border-amber-500/30',
        buttonBgActive: 'bg-amber-500/20',
        buttonBgHover: 'hover:bg-amber-500/10',
        iconColor: 'text-amber-400',
        overlayColor: 'bg-black/70'
      },
      quran: {
        accentColor: 'text-emerald-400',
        borderColor: 'border-emerald-500/30',
        buttonBgActive: 'bg-emerald-500/20',
        buttonBgHover: 'hover:bg-emerald-500/10',
        iconColor: 'text-emerald-400',
        overlayColor: 'bg-black/70'
      }
    };

    return stylesByType[quoteType] || stylesByType.anime;
  }, [quoteType]);

  // Render quote content based on type - memoized to prevent recalculation
  const quoteContent = useMemo(() => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-16">
          <RefreshCw className={`w-6 h-6 ${styles.accentColor} animate-spin`} />
        </div>
      );
    }

    if (!quote) {
      return (
        <div className="text-white/70 italic text-center">
          Unable to load quote. Please try again.
        </div>
      );
    }

    switch (quoteType) {
      case 'anime':
        return (
          <div className="flex flex-col">
            <p className="text-white italic mb-2 text-lg">"{quote.quote}"</p>
            <div className="flex items-center justify-between">
              <div className="text-sm">
                <span className={`font-medium ${styles.accentColor}`}>{quote.character}</span>
                <span className="mx-1 text-white/50">•</span>
                <span className="text-white/80">{quote.anime}</span>
              </div>
            </div>
          </div>
        );
      case 'bible':
      case 'quran':
        return (
          <div className="flex flex-col">
            <p className="text-white italic mb-2 text-lg">"{quote.text}"</p>
            <div className={`text-sm ${styles.accentColor} font-medium`}>
              {quote.reference}
            </div>
          </div>
        );
      default:
        return null;
    }
  }, [loading, quote, quoteType, styles.accentColor]);



  return (
    <div className="w-full mb-8">
      <div
        className="border border-white/10 rounded-xl p-5 relative overflow-hidden"
        style={{
          backgroundImage: background ? `url("${background}")` : 'none',
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
      >
        {/* Loading indicator for background */}
        {loadingBackground && (
          <div className="absolute top-2 right-2 z-20 bg-black/50 rounded-full p-1">
            <ImageIcon className={`w-4 h-4 ${styles.accentColor} animate-pulse`} />
          </div>
        )}

        {/* Overlay */}
        <div className={`absolute inset-0 ${styles.overlayColor}`}></div>

        {/* Glass effect elements */}
        <div className="absolute -top-8 -left-8 w-24 h-24 bg-white/5 rounded-full blur-xl"></div>
        <div className="absolute -bottom-10 -right-10 w-32 h-32 bg-white/5 rounded-full blur-xl"></div>

        {/* Content */}
        <div className="relative z-10">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-2xl md:text-3xl font-bold text-white flex items-center gap-2">
              <Sparkles className={`w-6 h-6 ${styles.accentColor}`} />
              {greeting}, Anime Fan!
            </h2>

            {/* Simple Time Display */}
            <div className={`flex items-center gap-2 bg-black/30 px-3 py-1.5 rounded-lg border ${styles.borderColor}`}>
              <Clock className={`w-4 h-4 ${styles.accentColor}`} />
              <span className="text-lg font-medium text-white">{formatTime(currentTime)}</span>
            </div>
          </div>

          {/* Quote Section */}
          <div className="mt-4 mb-3 bg-black/30 p-4 rounded-lg border border-white/10">
            {quoteContent}
          </div>

          {/* Quote Type Selector */}
          <div className="flex items-center justify-between mt-4">
            <div className="flex items-center gap-2">
              <QuoteTypeButton
                type="anime"
                currentType={quoteType}
                styles={styles}
                onClick={handleQuoteTypeChange}
                icon={Quote}
                label="Anime"
              />
              <QuoteTypeButton
                type="bible"
                currentType={quoteType}
                styles={styles}
                onClick={handleQuoteTypeChange}
                icon={Book}
                label="Bible"
              />
              <QuoteTypeButton
                type="quran"
                currentType={quoteType}
                styles={styles}
                onClick={handleQuoteTypeChange}
                icon={BookText}
                label="Quran"
              />
            </div>

            <button
              onClick={handleRefreshQuote}
              className={`p-1.5 rounded-lg bg-black/30 hover:bg-black/50 ${styles.accentColor} border ${styles.borderColor}`}
              title="Get new quote"
            >
              <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
});

export default GreetingsSection;
