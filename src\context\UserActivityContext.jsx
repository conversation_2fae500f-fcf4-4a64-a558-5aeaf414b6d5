import { createContext, useContext, useState, useEffect } from "react";
import { useAniList } from "@/hooks/useAniList";
import { toast } from "sonner";
import userProgressApi from "@/api/userProgressApi";
import profileApi from "@/api/profileApi";

const UserActivityContext = createContext();

export const useUserActivity = () => useContext(UserActivityContext);

export const UserActivityProvider = ({ children }) => {
  const { user, isAuthenticated } = useAniList();
  const [streak, setStreak] = useState(0);
  const [longestStreak, setLongestStreak] = useState(0);
  const [lastVisit, setLastVisit] = useState(null);
  const [visitDates, setVisitDates] = useState([]);
  const [level, setLevel] = useState(1);
  const [xp, setXp] = useState(0);
  const [rank, setRank] = useState("Rookie");
  const [achievements, setAchievements] = useState([]);
  const [rewards, setRewards] = useState([]);
  const [likes, setLikes] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Function to fetch user activity data
  const fetchUserActivity = async (userId) => {
    setIsLoading(true);
    let retryCount = 0;
    const maxRetries = 3;

    const loadUserData = async () => {
      try {
        console.log(`Attempt ${retryCount + 1} to fetch user profile data`);

        // Try to get data from profile API
        const activityData = await profileApi.getUserProfile(userId);

        if (!activityData || Object.keys(activityData).length === 0) {
          throw new Error('Empty response from profile API');
        }

        console.log("User activity loaded from profile API:", activityData);

        // Update state with API data
        setStreak(activityData.streak || 1);
        setLongestStreak(activityData.longestStreak || 1);
        setLastVisit(activityData.lastVisit || null);
        setVisitDates(activityData.visitDates || []);
        setLevel(activityData.level || 1);
        setXp(activityData.xp || 0);
        setRank(activityData.rank || 'Rookie');
        setAchievements(activityData.achievements || []);
        setRewards(activityData.rewards || []);
        setLikes(activityData.likes || 0);

        // Save to localStorage as a backup
        localStorage.setItem(`streak-${userId}`, (activityData.streak || 1).toString());
        localStorage.setItem(`longest-streak-${userId}`, (activityData.longestStreak || 1).toString());
        if (activityData.lastVisit) {
          localStorage.setItem(`last-visit-${userId}`, new Date(activityData.lastVisit).getTime().toString());
        }
        localStorage.setItem(`level-${userId}`, (activityData.level || 1).toString());
        localStorage.setItem(`xp-${userId}`, (activityData.xp || 0).toString());
        localStorage.setItem(`rank-${userId}`, activityData.rank || 'Rookie');
        localStorage.setItem(`likes-${userId}`, (activityData.likes || 0).toString());

        return true;
      } catch (err) {
        console.error(`Attempt ${retryCount + 1} failed:`, err);

        if (retryCount < maxRetries - 1) {
          retryCount++;
          // Wait a bit before retrying
          await new Promise(resolve => setTimeout(resolve, 1000));
          return await loadUserData();
        }

        console.error("All attempts to fetch from profile API failed, trying legacy API");
        setError("Failed to load user data from server. Using local data instead.");

        try {
          // Try to get data from legacy API as a second option
          const activityData = await userProgressApi.getUserActivity(userId);

          if (!activityData || Object.keys(activityData).length === 0) {
            throw new Error('Empty response from legacy API');
          }

          console.log("User activity loaded from legacy API:", activityData);

          // Update state with legacy API data
          setStreak(activityData.streak || 1);
          setLongestStreak(activityData.longestStreak || 1);
          setLastVisit(activityData.lastVisit || null);
          setVisitDates(activityData.visitDates || []);
          setLevel(activityData.level || 1);
          setXp(activityData.xp || 0);
          setRank(activityData.rank || 'Rookie');
          setAchievements(activityData.achievements || []);
          setRewards(activityData.rewards || []);
          setLikes(activityData.likes || 0);

          return true;
        } catch (legacyErr) {
          console.error("Error fetching user activity from legacy API:", legacyErr);

          // Fallback to localStorage if both APIs fail
          const fallbackData = profileApi.fallback.getUserProfile(userId);

          // Update state with fallback data
          setStreak(fallbackData.streak);
          setLongestStreak(fallbackData.longestStreak);
          setLastVisit(fallbackData.lastVisit);
          setVisitDates(fallbackData.visitDates);
          setLevel(fallbackData.level);
          setXp(fallbackData.xp);
          setRank(fallbackData.rank);
          setAchievements(fallbackData.achievements || []);
          setRewards(fallbackData.rewards || []);
          setLikes(fallbackData.likes || 0);

          console.log("User activity loaded from localStorage (fallback)");
          return false;
        }
      }
    };

    await loadUserData();
    setIsLoading(false);

    return true;
  };

  // Initialize user activity data from API or fallback to localStorage
  useEffect(() => {
    if (isAuthenticated && user?.id) {
      const userId = user.id;

      const initializeUserActivity = async () => {
        // Fetch user activity data
        await fetchUserActivity(userId);

        // Record today's visit
        recordVisit(userId);
      };

      initializeUserActivity();

      // Set up a periodic refresh of user data (every 30 seconds)
      const refreshInterval = setInterval(() => {
        if (isAuthenticated && user?.id) {
          console.log("Periodic refresh of user profile data");
          fetchUserActivity(userId);
        }
      }, 30000); // 30 seconds

      // Clean up interval on unmount
      return () => clearInterval(refreshInterval);
    }
  }, [isAuthenticated, user]);

  // Update rank whenever level changes
  useEffect(() => {
    if (isAuthenticated && user?.id) {
      console.log("Level changed to", level, "- updating rank");
      updateRank(level);
    }
  }, [level, isAuthenticated, user]);

  // Record a visit and update streak
  const recordVisit = async (userId) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayTimestamp = today.getTime();

    // Check if we've already recorded a visit today in localStorage
    const visitRecordedKey = `visit-recorded-${userId}-${todayTimestamp}`;
    const visitAlreadyRecorded = localStorage.getItem(visitRecordedKey);

    if (visitAlreadyRecorded) {
      console.log("Visit already recorded today (from localStorage flag)");
      return; // Already recorded a visit today
    }

    // Check if we already recorded a visit today based on lastVisit
    if (lastVisit) {
      const lastVisitDate = new Date(lastVisit);
      lastVisitDate.setHours(0, 0, 0, 0);

      if (lastVisitDate.getTime() === todayTimestamp) {
        console.log("Already recorded a visit today (from lastVisit)");
        // Set the flag to prevent future attempts
        localStorage.setItem(visitRecordedKey, 'true');
        return; // Already recorded a visit today
      }
    }

    // Check if we already recorded a visit today based on visitDates
    const hasVisitedToday = Array.isArray(visitDates) && visitDates.some(date => {
      const visitDate = new Date(date);
      visitDate.setHours(0, 0, 0, 0);
      return visitDate.getTime() === todayTimestamp;
    });

    if (hasVisitedToday) {
      console.log("Already recorded a visit today (from visitDates)");
      // Set the flag to prevent future attempts
      localStorage.setItem(visitRecordedKey, 'true');
      return; // Already recorded a visit today
    }

    // Double-check with the server if we've already recorded a visit today
    try {
      const profileData = await profileApi.getUserProfile(userId);

      if (profileData && profileData.lastVisit) {
        const serverLastVisit = new Date(profileData.lastVisit);
        serverLastVisit.setHours(0, 0, 0, 0);

        if (serverLastVisit.getTime() === todayTimestamp) {
          console.log("Already recorded a visit today (from server)");
          // Set the flag to prevent future attempts
          localStorage.setItem(visitRecordedKey, 'true');

          // Update local state to match server
          setLastVisit(profileData.lastVisit);
          setVisitDates(profileData.visitDates || []);
          setStreak(profileData.streak || 1);
          setLongestStreak(profileData.longestStreak || 1);

          return; // Already recorded a visit today
        }
      }
    } catch (error) {
      console.error("Error checking visit status from server:", error);
      // Continue with recording the visit if we can't check the server
    }

    // Get yesterday's date
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayTimestamp = yesterday.getTime();

    let newStreak = streak || 1;
    let newVisitDates = Array.isArray(visitDates) ? [...visitDates] : [];

    // Add today to visit dates if not already there
    newVisitDates.push(today);

    // Keep only the last 30 days of visits
    if (newVisitDates.length > 30) {
      newVisitDates = newVisitDates.slice(newVisitDates.length - 30);
    }

    // Update streak logic
    let visitedYesterday = false;
    if (lastVisit) {
      const lastVisitDate = new Date(lastVisit);
      lastVisitDate.setHours(0, 0, 0, 0);
      visitedYesterday = lastVisitDate.getTime() === yesterdayTimestamp;
    }

    if (visitedYesterday) {
      // Consecutive day, increment streak
      newStreak += 1;
      console.log(`Streak increased to ${newStreak} days`);
    } else {
      // Not consecutive, reset streak
      newStreak = 1;
      console.log(`Streak reset to 1 day`);
    }

    // For demonstration purposes, let's set a minimum streak of 1
    // This ensures the user always sees at least one flame lit
    newStreak = Math.max(1, newStreak);

    // Update longest streak if current streak is longer
    const newLongestStreak = Math.max(longestStreak || 0, newStreak);

    // Save all updates
    setStreak(newStreak);
    setLongestStreak(newLongestStreak);
    setLastVisit(today);
    setVisitDates(newVisitDates);

    // Set the flag to prevent recording multiple visits in one day
    localStorage.setItem(visitRecordedKey, 'true');

    // Add XP for daily visit
    addXp(10);

    // Check for streak achievements
    checkStreakAchievements(newStreak, userId);

    // Update data on server with retry logic
    let retryCount = 0;
    const maxRetries = 3;

    const updateServer = async () => {
      try {
        console.log(`Attempt ${retryCount + 1} to record visit on profile server`);

        // Try to record visit with profile API
        const response = await profileApi.recordVisit(userId);
        console.log("Visit recorded on profile server:", response);

        // Refresh user profile data after a short delay
        setTimeout(() => {
          console.log("Refreshing user profile data after recording visit");
          fetchUserActivity(userId);
        }, 1000);

        return true;
      } catch (profileErr) {
        console.error(`Attempt ${retryCount + 1} failed:`, profileErr);

        if (retryCount < maxRetries - 1) {
          retryCount++;
          // Wait a bit before retrying
          await new Promise(resolve => setTimeout(resolve, 1000));
          return await updateServer();
        }

        console.error("All attempts to record visit on profile server failed, trying legacy API");

        try {
          // Try legacy API as fallback
          await userProgressApi.recordVisit(userId);

          // Update user activity data with legacy API
          const activityData = {
            streak: newStreak,
            longestStreak: newLongestStreak,
            lastVisit: today,
            visitDates: newVisitDates
          };

          await userProgressApi.updateUserActivity(userId, activityData);
          console.log("Visit recorded on legacy server");

          // Refresh user profile data after a short delay
          setTimeout(() => {
            console.log("Refreshing user profile data after recording visit (legacy)");
            fetchUserActivity(userId);
          }, 1000);

          return true;
        } catch (legacyErr) {
          console.error("Error recording visit on legacy server:", legacyErr);

          // Fallback to localStorage if both APIs fail
          localStorage.setItem(`streak-${userId}`, newStreak.toString());
          localStorage.setItem(`longest-streak-${userId}`, newLongestStreak.toString());
          localStorage.setItem(`last-visit-${userId}`, todayTimestamp.toString());
          localStorage.setItem(`visit-dates-${userId}`, JSON.stringify(newVisitDates.map(d => new Date(d).getTime())));
          console.log("Visit recorded in localStorage (fallback)");
          return false;
        }
      }
    };

    await updateServer();
  };

  // Add XP and update level/rank
  const addXp = async (amount, source = 'general') => {
    if (!amount || isNaN(amount) || amount <= 0) {
      console.warn(`Invalid XP amount: ${amount}`);
      return;
    }

    const newXp = (xp || 0) + amount;

    // Calculate level based on XP
    // The formula should be the inverse of the XP calculation: level = sqrt(xp/100)
    // This ensures that level 1 is 0-100 XP, level 2 is 100-400 XP, level 3 is 400-900 XP, etc.
    let newLevel = 1;

    // Find the highest level where the base XP is less than or equal to the current XP
    while (Math.pow(newLevel + 1, 2) * 100 <= newXp) {
      newLevel++;
    }

    console.log(`Level calculation: XP=${newXp}, calculated level=${newLevel}`);

    // Check if user leveled up
    if (newLevel > (level || 1)) {
      // Show level up notification
      toast.success(`Level Up! You're now level ${newLevel}`, {
        description: `You've earned +${newLevel * 10} XP as a level-up bonus!`,
        duration: 5000
      });

      // Add level-up bonus XP
      const bonusXp = newLevel * 10;
      const totalNewXp = newXp + bonusXp;

      // Update state with bonus included
      setXp(totalNewXp);

      // Create level-up achievement
      if (user?.id) {
        const levelAchievement = {
          id: `level-${newLevel}`,
          title: `Reached Level ${newLevel}!`,
          description: `You've reached level ${newLevel} on your anime journey!`,
          date: Date.now(),
          type: 'level'
        };

        const levelReward = {
          id: `level-reward-${newLevel}`,
          title: `Level ${newLevel} Reward`,
          description: `+${bonusXp} XP`,
          date: Date.now(),
          value: bonusXp,
          type: 'xp'
        };

        // Update achievements and rewards
        const updatedAchievements = [...achievements, levelAchievement];
        const updatedRewards = [...rewards, levelReward];

        setAchievements(updatedAchievements);
        setRewards(updatedRewards);

        // Try to save achievement to API with retry logic
        let retryCount = 0;
        const maxRetries = 3;

        const saveAchievement = async () => {
          try {
            console.log(`Attempt ${retryCount + 1} to save achievement to profile API`);

            // Try to unlock achievement with profile API
            await profileApi.unlockAchievement(user.id, levelAchievement.id);
            console.log("Achievement saved to profile API");
            return true;
          } catch (profileErr) {
            console.error(`Attempt ${retryCount + 1} failed:`, profileErr);

            if (retryCount < maxRetries - 1) {
              retryCount++;
              // Wait a bit before retrying
              await new Promise(resolve => setTimeout(resolve, 1000));
              return await saveAchievement();
            }

            console.error("All attempts to save achievement to profile API failed, trying legacy API");

            try {
              // Try legacy API as fallback
              await userProgressApi.unlockAchievement(user.id, levelAchievement.id);
              console.log("Achievement saved to legacy API");
              return true;
            } catch (legacyErr) {
              console.error("Error saving achievement to legacy API:", legacyErr);

              // Fallback to localStorage if both APIs fail
              localStorage.setItem(`achievements-${user.id}`, JSON.stringify(updatedAchievements));
              localStorage.setItem(`rewards-${user.id}`, JSON.stringify(updatedRewards));
              return false;
            }
          }
        };

        // Don't await this to avoid blocking the UI
        saveAchievement().catch(err => {
          console.error("Unexpected error in saveAchievement:", err);
        });
      }
    } else {
      // Just update XP without bonus
      setXp(newXp);
    }

    // Update level
    setLevel(newLevel);

    // Determine rank based on level and watch time
    updateRank(newLevel);

    // Try to update XP on server with retry logic
    if (user?.id) {
      let retryCount = 0;
      const maxRetries = 3;

      const updateXp = async () => {
        try {
          console.log(`Attempt ${retryCount + 1} to add XP on profile server`);

          // Try to add XP with profile API
          const response = await profileApi.addXp(user.id, amount, source);
          console.log(`${amount} XP added on profile server:`, response);

          // Refresh user profile data after a short delay to ensure the server has processed the update
          setTimeout(() => {
            console.log("Refreshing user profile data after XP update");
            fetchUserActivity(user.id);
          }, 1000);

          return true;
        } catch (profileErr) {
          console.error(`Attempt ${retryCount + 1} failed:`, profileErr);

          if (retryCount < maxRetries - 1) {
            retryCount++;
            // Wait a bit before retrying
            await new Promise(resolve => setTimeout(resolve, 1000));
            return await updateXp();
          }

          console.error("All attempts to add XP on profile server failed, trying legacy API");

          try {
            // Try legacy API as fallback
            await userProgressApi.addXp(user.id, amount, source);

            // Update user activity data with legacy API
            const activityData = {
              xp: newLevel > (level || 1) ? newXp + (newLevel * 10) : newXp,
              level: newLevel
            };

            await userProgressApi.updateUserActivity(user.id, activityData);
            console.log(`${amount} XP added on legacy server`);

            // Refresh user profile data after a short delay
            setTimeout(() => {
              console.log("Refreshing user profile data after XP update (legacy)");
              fetchUserActivity(user.id);
            }, 1000);

            return true;
          } catch (legacyErr) {
            console.error("Error adding XP on legacy server:", legacyErr);

            // Fallback to localStorage if both APIs fail
            localStorage.setItem(`xp-${user.id}`, (newLevel > (level || 1) ? newXp + (newLevel * 10) : newXp).toString());
            localStorage.setItem(`level-${user.id}`, newLevel.toString());
            console.log(`${amount} XP added to localStorage (fallback)`);
            return false;
          }
        }
      };

      // Don't await this to avoid blocking the UI
      updateXp().catch(err => {
        console.error("Unexpected error in updateXp:", err);
      });
    } else {
      // If user is not logged in, just save to localStorage
      localStorage.setItem('guest-xp', newXp.toString());
      localStorage.setItem('guest-level', newLevel.toString());
    }
  };

  // Update rank based on level and watch time
  const updateRank = async (currentLevel) => {
    // Get watch time in hours from AniList data
    const watchTimeHours = user?.statistics?.anime?.minutesWatched
      ? Math.floor(user.statistics.anime.minutesWatched / 60)
      : 0;

    // Define ranks with their requirements
    const ranks = [
      { name: "Rookie", level: 1, watchHours: 0 },
      { name: "Potato", level: 5, watchHours: 24 },      // 1 day of watch time
      { name: "Anime Watcher", level: 10, watchHours: 72 },  // 3 days
      { name: "Anime Fan", level: 15, watchHours: 120 },     // 5 days
      { name: "Anime Enthusiast", level: 20, watchHours: 240 }, // 10 days
      { name: "Anime Veteran", level: 30, watchHours: 480 },    // 20 days
      { name: "Anime Expert", level: 40, watchHours: 720 },     // 30 days
      { name: "Anime Master", level: 50, watchHours: 1200 },    // 50 days
      { name: "Anime Legend", level: 75, watchHours: 2400 },    // 100 days
      { name: "Anime God", level: 100, watchHours: 3600 }       // 150 days
    ];

    // Find the highest rank the user qualifies for
    let newRank = "Rookie";
    for (let i = ranks.length - 1; i >= 0; i--) {
      if (currentLevel >= ranks[i].level && watchTimeHours >= ranks[i].watchHours) {
        newRank = ranks[i].name;
        break;
      }
    }

    // Check if rank changed
    if (newRank !== rank) {
      // Show rank up notification if it's an upgrade
      if (ranks.findIndex(r => r.name === newRank) > ranks.findIndex(r => r.name === rank)) {
        toast.success(`Rank Up! You're now ${newRank}`, {
          description: `You've achieved a new rank based on your level and watch time!`,
          duration: 5000
        });

        // Create rank-up achievement
        if (user?.id) {
          const rankAchievement = {
            id: `rank-${newRank.replace(/\s+/g, '-').toLowerCase()}`,
            title: `Achieved ${newRank} Rank!`,
            description: `You've reached the ${newRank} rank!`,
            date: Date.now(),
            type: 'rank'
          };

          // Add XP reward for rank up
          const rankXpReward = 100;

          const rankReward = {
            id: `rank-reward-${newRank.replace(/\s+/g, '-').toLowerCase()}`,
            title: `${newRank} Rank Reward`,
            description: `+${rankXpReward} XP`,
            date: Date.now(),
            value: rankXpReward,
            type: 'xp'
          };

          // Update achievements and rewards
          const updatedAchievements = [...achievements, rankAchievement];
          const updatedRewards = [...rewards, rankReward];

          setAchievements(updatedAchievements);
          setRewards(updatedRewards);

          // Try to save achievement to API
          try {
            // Try to unlock achievement with profile API
            await profileApi.unlockAchievement(user.id, rankAchievement.id);
            console.log("Rank achievement saved to profile API");

            // Add XP reward through profile API
            await profileApi.addXp(user.id, rankXpReward, 'rank_up');
          } catch (profileErr) {
            console.error("Error saving rank achievement to profile API:", profileErr);

            try {
              // Try legacy API as fallback
              await userProgressApi.unlockAchievement(user.id, rankAchievement.id);
              console.log("Rank achievement saved to legacy API");

              // Add XP reward through legacy API
              await userProgressApi.addXp(user.id, rankXpReward, 'rank_up');
            } catch (legacyErr) {
              console.error("Error saving rank achievement to legacy API:", legacyErr);

              // Fallback to localStorage if both APIs fail
              localStorage.setItem(`achievements-${user.id}`, JSON.stringify(updatedAchievements));
              localStorage.setItem(`rewards-${user.id}`, JSON.stringify(updatedRewards));

              // Add XP reward (without triggering another rank check)
              setXp(prevXp => {
                const newXpValue = prevXp + rankXpReward;
                localStorage.setItem(`xp-${user.id}`, newXpValue.toString());
                return newXpValue;
              });
            }
          }
        }
      }
    }

    // Update rank state
    setRank(newRank);

    // Update rank on server
    if (user?.id) {
      try {
        // Try to update rank with profile API
        await profileApi.updateUserProfile(user.id, { rank: newRank });
        console.log("Rank updated on profile server");
      } catch (profileErr) {
        console.error("Error updating rank on profile server:", profileErr);

        try {
          // Try legacy API as fallback
          const activityData = {
            rank: newRank
          };

          await userProgressApi.updateUserActivity(user.id, activityData);
          console.log("Rank updated on legacy server");
        } catch (legacyErr) {
          console.error("Error updating rank on legacy server:", legacyErr);

          // Fallback to localStorage if both APIs fail
          localStorage.setItem(`rank-${user.id}`, newRank);
          console.log("Rank saved to localStorage (fallback)");
        }
      }
    }
  };

  // Get visit status for the last 10 days (for streak display)
  const getRecentVisits = () => {
    const result = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get the last 10 days
    for (let i = 0; i < 10; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const timestamp = date.getTime();

      result.push({
        day: i,
        date: timestamp,
        visited: visitDates.includes(timestamp)
      });
    }

    return result.reverse(); // Return in chronological order
  };

  // Calculate XP needed for next level
  const getXpForNextLevel = () => {
    const nextLevel = level + 1;
    return Math.pow(nextLevel, 2) * 100;
  };

  // Calculate XP progress percentage
  const getXpProgress = () => {
    const nextLevelXp = getXpForNextLevel();
    const currentLevelXp = Math.pow(level, 2) * 100;

    // Calculate total XP needed for this level and how much has been earned
    const totalXpForCurrentLevel = nextLevelXp - currentLevelXp;
    const earnedXpInCurrentLevel = Math.max(0, xp - currentLevelXp);

    // Calculate progress percentage
    const progress = Math.floor((earnedXpInCurrentLevel / totalXpForCurrentLevel) * 100);

    // Log the calculation for debugging
    console.log(`XP Progress Calculation:`, {
      xp,
      level,
      nextLevelXp,
      currentLevelXp,
      totalXpForCurrentLevel,
      earnedXpInCurrentLevel,
      progress
    });

    // Ensure the result is between 0 and 100
    return Math.max(0, Math.min(100, progress));
  };

  // Check for streak achievements and rewards
  const checkStreakAchievements = async (currentStreak, userId) => {
    // Define streak milestones
    const streakMilestones = [3, 7, 14, 30, 60, 90, 180, 365];

    // Check if current streak hits any milestone
    if (streakMilestones.includes(currentStreak)) {
      // Calculate XP reward (higher for longer streaks)
      const xpReward = currentStreak * 5;

      // Create achievement
      const newAchievement = {
        id: `streak-${currentStreak}`,
        title: `${currentStreak} Day Streak!`,
        description: `You've visited the site for ${currentStreak} consecutive days!`,
        date: Date.now(),
        type: 'streak'
      };

      // Create reward
      const newReward = {
        id: `streak-reward-${currentStreak}`,
        title: `${currentStreak} Day Streak Reward`,
        description: `+${xpReward} XP`,
        date: Date.now(),
        value: xpReward,
        type: 'xp'
      };

      // Update achievements and rewards
      const updatedAchievements = [...achievements, newAchievement];
      const updatedRewards = [...rewards, newReward];

      setAchievements(updatedAchievements);
      setRewards(updatedRewards);

      // Try to save achievement to API
      try {
        // Try to unlock achievement with profile API
        await profileApi.unlockAchievement(userId, newAchievement.id);
        console.log("Streak achievement saved to profile API");

        // Add XP reward through profile API
        await profileApi.addXp(userId, xpReward, 'streak_milestone');
      } catch (profileErr) {
        console.error("Error saving streak achievement to profile API:", profileErr);

        try {
          // Try legacy API as fallback
          await userProgressApi.unlockAchievement(userId, newAchievement.id);
          console.log("Streak achievement saved to legacy API");

          // Add XP reward through legacy API
          await userProgressApi.addXp(userId, xpReward, 'streak_milestone');
        } catch (legacyErr) {
          console.error("Error saving streak achievement to legacy API:", legacyErr);

          // Fallback to localStorage if both APIs fail
          localStorage.setItem(`achievements-${userId}`, JSON.stringify(updatedAchievements));
          localStorage.setItem(`rewards-${userId}`, JSON.stringify(updatedRewards));

          // Add XP reward
          addXp(xpReward);
        }
      }

      // Show toast notification
      toast.success(`Achievement Unlocked: ${newAchievement.title}`, {
        description: `${newAchievement.description} You earned ${xpReward} XP!`,
        duration: 5000
      });
    }
  };

  // Add a like to the user's profile
  const addLike = async (likerUserId = null) => {
    if (!user?.id) return;

    const newLikes = likes + 1;
    setLikes(newLikes);

    // Add XP for receiving a like
    addXp(5, 'like_received');

    // Check for like milestones
    checkLikeMilestones(newLikes);

    // Update likes on server
    try {
      // Try to add like with profile API
      await profileApi.addLike(likerUserId || 'anonymous', user.id);
      console.log("Like added on profile server");
    } catch (profileErr) {
      console.error("Error adding like on profile server:", profileErr);

      try {
        // Try legacy API as fallback
        await userProgressApi.addLike(likerUserId || 'anonymous', user.id);

        // Update user activity data with legacy API
        const activityData = {
          likes: newLikes
        };

        await userProgressApi.updateUserActivity(user.id, activityData);
        console.log("Like added on legacy server");
      } catch (legacyErr) {
        console.error("Error adding like on legacy server:", legacyErr);

        // Fallback to localStorage if both APIs fail
        localStorage.setItem(`likes-${user.id}`, newLikes.toString());
        console.log("Like saved to localStorage (fallback)");
      }
    }

    return newLikes;
  };

  // Check for like milestone achievements
  const checkLikeMilestones = async (currentLikes) => {
    // Define like milestones
    const likeMilestones = [10, 50, 100, 500, 1000];

    // Check if current likes hits any milestone
    const milestone = likeMilestones.find(m => m === currentLikes);

    if (milestone && user?.id) {
      // Create achievement
      const likeAchievement = {
        id: `likes-${milestone}`,
        title: `${milestone} Likes!`,
        description: `Your profile has received ${milestone} likes!`,
        date: Date.now(),
        type: 'likes'
      };

      // Calculate XP reward
      const xpReward = milestone;

      const likeReward = {
        id: `likes-reward-${milestone}`,
        title: `${milestone} Likes Reward`,
        description: `+${xpReward} XP`,
        date: Date.now(),
        value: xpReward,
        type: 'xp'
      };

      // Update achievements and rewards
      const updatedAchievements = [...achievements, likeAchievement];
      const updatedRewards = [...rewards, likeReward];

      setAchievements(updatedAchievements);
      setRewards(updatedRewards);

      // Try to save achievement to API
      try {
        await userProgressApi.unlockAchievement(user.id, likeAchievement.id);
        console.log("Like achievement saved to API");

        // Add XP reward through API
        await userProgressApi.addXp(user.id, xpReward, 'like_milestone');
      } catch (err) {
        console.error("Error saving like achievement to API:", err);

        // Fallback to localStorage
        localStorage.setItem(`achievements-${user.id}`, JSON.stringify(updatedAchievements));
        localStorage.setItem(`rewards-${user.id}`, JSON.stringify(updatedRewards));

        // Add XP reward
        addXp(xpReward);
      }

      // Show toast notification
      toast.success(`Achievement Unlocked: ${likeAchievement.title}`, {
        description: `${likeAchievement.description} You earned ${xpReward} XP!`,
        duration: 5000
      });
    }
  };

  return (
    <UserActivityContext.Provider
      value={{
        streak,
        longestStreak,
        level,
        xp,
        rank,
        achievements,
        rewards,
        likes,
        getRecentVisits,
        getXpProgress,
        getXpForNextLevel,
        addXp,
        addLike,
        updateRank,
        fetchUserActivity
      }}
    >
      {children}
    </UserActivityContext.Provider>
  );
};
