import { useUserActivity } from "@/context/UserActivityContext";
import { TrendingUp, Clock, RefreshCw } from "lucide-react";

const RankSystem = () => {
  const { level, rank, xp, getXpProgress, getXpForNextLevel } = useUserActivity();

  // Let's calculate the XP progress directly here to ensure it's correct
  // For level 3: base XP is 900, next level XP is 1600

  // Calculate the base XP for the current level
  // Formula: level^2 * 100
  const currentLevelXp = Math.pow(level, 2) * 100;

  // Calculate the XP needed for the next level
  const nextLevelXp = getXpForNextLevel();

  // Calculate XP needed to reach next level from current position
  const xpNeeded = Math.max(0, nextLevelXp - xp);

  // Calculate total XP needed for this level - use let instead of const since we might modify it
  let totalXpForLevel = nextLevelXp - currentLevelXp;

  // Calculate how much XP has been earned in the current level
  // If xp < currentLevelXp, this means the user is at this level but hasn't earned any XP towards the next level yet
  // This can happen due to level calculation inconsistencies or if XP was reduced for some reason
  let earnedXpInLevel = Math.max(0, xp - currentLevelXp);

  // Special case: If the user's XP is less than the current level's base XP,
  // we'll show progress based on their XP relative to the previous level's base XP
  if (xp < currentLevelXp && level > 1) {
    const previousLevelXp = Math.pow(level - 1, 2) * 100;
    earnedXpInLevel = xp - previousLevelXp;

    // Update the total XP needed for this special case
    totalXpForLevel = currentLevelXp - previousLevelXp;

    console.log(`Special case: XP (${xp}) < currentLevelXp (${currentLevelXp}), using previous level base: ${previousLevelXp}`);
  }

  // Calculate progress percentage (make sure to handle division by zero)
  const calculatedProgress = totalXpForLevel > 0
    ? Math.floor((earnedXpInLevel / totalXpForLevel) * 100)
    : 0;

  // Ensure progress is between 0 and 100
  const progressPercent = Math.max(0, Math.min(100, calculatedProgress));

  // Force a minimum visible progress if XP is greater than 0
  // This ensures the bar is always at least slightly visible
  // For level 1, we need a special case since currentLevelXp is 0
  let displayProgress;
  if (level === 1) {
    // For level 1, calculate progress as a percentage of the XP needed for level 2
    displayProgress = Math.max(1, Math.min(100, Math.floor((xp / nextLevelXp) * 100)));
  } else {
    // For other levels, use the standard calculation but ensure it's at least 1% if there's any XP progress
    displayProgress = xp > currentLevelXp ? Math.max(1, progressPercent) : 0;
  }

  // If the user has any XP, ensure the bar shows at least 1% progress
  if (xp > 0 && displayProgress === 0) {
    displayProgress = 1;
  }

  // Log the values for debugging
  console.log('RankSystem XP Values:', {
    level,
    xp,
    currentLevelXp,
    nextLevelXp,
    xpNeeded,
    totalXpForLevel,
    earnedXpInLevel,
    calculatedProgress,
    progressPercent,
    displayProgress
  });

  // Get watch time from AniList data
  const { user } = useUserActivity();
  const watchTimeHours = user?.statistics?.anime?.minutesWatched
    ? Math.floor(user.statistics.anime.minutesWatched / 60)
    : 0;

  // Calculate next rank requirements
  const getNextRank = () => {
    const ranks = [
      { name: "Rookie", level: 1, watchHours: 0 },
      { name: "Potato", level: 5, watchHours: 24 },
      { name: "Anime Watcher", level: 10, watchHours: 72 },
      { name: "Anime Fan", level: 15, watchHours: 120 },
      { name: "Anime Enthusiast", level: 20, watchHours: 240 },
      { name: "Anime Veteran", level: 30, watchHours: 480 },
      { name: "Anime Expert", level: 40, watchHours: 720 },
      { name: "Anime Master", level: 50, watchHours: 1200 },
      { name: "Anime Legend", level: 75, watchHours: 2400 },
      { name: "Anime God", level: 100, watchHours: 3600 }
    ];

    // Log current rank and level for debugging
    console.log("Current rank:", rank, "Current level:", level);

    // Find the current rank index
    const currentRankIndex = ranks.findIndex(r => r.name === rank);

    // If rank not found or is the last rank, handle appropriately
    if (currentRankIndex === -1) {
      console.warn("Current rank not found in ranks array:", rank);
      // Find the appropriate rank based on level and return the next one
      for (let i = 0; i < ranks.length - 1; i++) {
        if (level >= ranks[i].level && level < ranks[i + 1].level) {
          return ranks[i + 1];
        }
      }
      // Default to the first rank that's higher than the current level
      for (let i = 0; i < ranks.length; i++) {
        if (ranks[i].level > level) {
          return ranks[i];
        }
      }
      return ranks[0]; // Fallback to first rank
    }

    if (currentRankIndex === ranks.length - 1) {
      return { name: "Max Rank Achieved", level: 0, watchHours: 0 };
    }

    return ranks[currentRankIndex + 1];
  };

  const nextRank = getNextRank();

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-2">
        <div>
          <h3 className="text-sm font-medium flex items-center gap-1">
            <TrendingUp size={14} className="text-yellow-500" />
            Lvl {level}
          </h3>
          <p className="text-xs text-white/60">{rank}</p>
        </div>
        <div className="text-xs text-white/60">
          {xp} / {nextLevelXp} XP
        </div>
      </div>

      {/* XP Progress Bar */}
      <div className="w-full h-1.5 bg-black/30 rounded-full overflow-hidden mb-1">
        <div
          className="h-full bg-yellow-500 rounded-full"
          style={{ width: `${displayProgress}%` }}
        ></div>
      </div>

      {/* Debug info - remove in production */}
      <div className="text-[10px] text-white/40 mb-1">
        Progress: {displayProgress}% | XP: {xp}/{nextLevelXp} | Level Base: {currentLevelXp}
      </div>

      {/* Fallback progress bar - only shown if the main one is invisible */}
      {displayProgress === 0 && xp > 0 && (
        <div className="w-full h-1.5 bg-black/30 rounded-full overflow-hidden mb-1">
          <div
            className="h-full bg-yellow-500 rounded-full"
            style={{ width: "1%" }}
          ></div>
        </div>
      )}

      <div className="text-[10px] text-white/60 text-right mb-3">
        {xpNeeded} XP needed for next level
      </div>

      {/* Next Rank Info */}
      {nextRank.name !== "Max Rank Achieved" && (
        <div className="bg-black/30 rounded-lg p-3">
          <div className="flex justify-between items-center mb-1">
            <h4 className="text-xs font-medium">Next Rank: {nextRank.name}</h4>
          </div>

          <div className="space-y-2 mt-2">
            {/* Level requirement */}
            <div>
              <div className="flex justify-between items-center text-xs mb-1">
                <span className="text-white/60">Level {nextRank.level}</span>
                <span className="text-white/60">{level}/{nextRank.level}</span>
              </div>
              <div className="w-full h-1.5 bg-black/50 rounded-full overflow-hidden">
                <div
                  className="h-full bg-blue-500 rounded-full"
                  style={{ width: `${Math.min(100, (level / nextRank.level) * 100)}%` }}
                ></div>
              </div>
            </div>

            {/* Watch time requirement */}
            <div>
              <div className="flex justify-between items-center text-xs mb-1">
                <span className="text-white/60 flex items-center gap-1">
                  <Clock size={10} />
                  {nextRank.watchHours} hours
                </span>
                <span className="text-white/60">{watchTimeHours}/{nextRank.watchHours}</span>
              </div>
              <div className="w-full h-1.5 bg-black/50 rounded-full overflow-hidden">
                <div
                  className="h-full bg-green-500 rounded-full"
                  style={{ width: `${Math.min(100, (watchTimeHours / nextRank.watchHours) * 100)}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RankSystem;
