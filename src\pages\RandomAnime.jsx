import { useState, useEffect, useCallback } from 'react';
import { Loader2, Refresh<PERSON><PERSON>, <PERSON> } from 'lucide-react';
import axios from 'axios';
import AnimeCard from '@/components/anime/AnimeCard';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

const RandomAnime = () => {
  const [randomAnime, setRandomAnime] = useState([]);
  const [loading, setLoading] = useState(true);
  const [likedAnime, setLikedAnime] = useState([]);
  const [error, setError] = useState(null);

  // Fetch random anime
  const fetchRandomAnime = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Get random page numbers for variety
      const randomPage1 = Math.floor(Math.random() * 20) + 1;
      const randomPage2 = Math.floor(Math.random() * 20) + 1;
      const randomPage3 = Math.floor(Math.random() * 20) + 1;

      // Define endpoints to fetch from
      const endpoints = [
        `https://api.jikan.moe/v4/top/anime?page=${randomPage1}&limit=10`,
        `https://api.jikan.moe/v4/seasons/now?page=${randomPage2}&limit=10`,
        `https://api.jikan.moe/v4/anime?page=${randomPage3}&limit=10&order_by=popularity&sort=desc`
      ];

      // Fetch data from all endpoints
      const responses = await Promise.all(
        endpoints.map(endpoint => axios.get(endpoint))
      );

      // Combine and filter results
      let combinedAnime = [];
      responses.forEach(response => {
        const animeWithImages = response.data.data.filter(anime =>
          anime.images?.jpg?.large_image_url ||
          anime.images?.webp?.large_image_url
        );
        combinedAnime = [...combinedAnime, ...animeWithImages];
      });

      // Shuffle the array for more randomness
      const shuffled = combinedAnime.sort(() => 0.5 - Math.random());

      // Take only 3 anime for the random selection
      const selected = shuffled.slice(0, 3);

      setRandomAnime(selected);
    } catch (error) {
      console.error("Error fetching random anime:", error);
      setError("Failed to load anime. Please try again.");
      toast.error("Failed to load anime. Please try again.");
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchRandomAnime();

    // Set page title
    document.title = "Random Anime - AnimeHQ";

    return () => {
      // Reset title when leaving the page
      document.title = "AnimeHQ";
    };
  }, [fetchRandomAnime]);

  // Handle like action
  const handleLike = (anime) => {
    // Add to liked anime
    setLikedAnime(prev => {
      // Check if already liked to avoid duplicates
      if (prev.some(item => item.mal_id === anime.mal_id)) {
        return prev;
      }
      return [...prev, anime];
    });
    toast.success(`Added ${anime.title} to liked anime!`);
  };

  // Reset and fetch new anime
  const handleRefresh = () => {
    fetchRandomAnime();
  };

  return (
    <div className="flex flex-col items-center justify-start w-full min-h-[80vh] py-8 px-4 relative">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-b from-primary/5 via-black/0 to-primary/5 pointer-events-none"></div>

      {/* Content container */}
      <div className="w-full max-w-6xl flex flex-col items-center relative z-10">
        <div className="flex flex-col md:flex-row md:items-center justify-between w-full mb-8 gap-4 bg-white/5 backdrop-blur-md p-4 md:p-6 rounded-xl border border-white/10 shadow-lg">
          <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-white to-white/70 bg-clip-text text-transparent">
            Random Anime Finder
          </h1>
          <Button
            onClick={handleRefresh}
            variant="outline"
            className="flex items-center gap-2 bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
          >
            <RefreshCw size={16} />
            <span>New Random Selection</span>
          </Button>
        </div>

        {loading ? (
          <div className="w-full flex items-center justify-center py-20 bg-white/5 backdrop-blur-md rounded-xl border border-white/10">
            <div className="flex flex-col items-center gap-4">
              <Loader2 className="w-12 h-12 text-primary animate-spin" />
              <p className="text-white/70">Finding random anime for you...</p>
            </div>
          </div>
        ) : error ? (
          <div className="w-full flex flex-col items-center justify-center py-20 text-center bg-white/5 backdrop-blur-md rounded-xl border border-white/10">
            <p className="text-red-400 mb-4">{error}</p>
            <Button onClick={handleRefresh} variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
              <RefreshCw className="mr-2 h-4 w-4" /> Try Again
            </Button>
          </div>
        ) : (
          <>
            {/* Random anime grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full mb-8">
              {randomAnime.map(anime => (
                <AnimeCard
                  key={anime.mal_id}
                  anime={anime}
                  onLike={handleLike}
                />
              ))}
            </div>

            {/* Instructions */}
            <div className="text-center text-white/70 text-sm mb-8 max-w-2xl p-4 bg-white/5 backdrop-blur-md rounded-xl border border-white/10">
              <p>These are three randomly selected anime from various categories. Click "Like" to add an anime to your liked list, or "View Details" to learn more about it.</p>
              <p className="mt-2">Click "New Random Selection" to get a fresh set of recommendations.</p>
            </div>
          </>
        )}

        {/* Liked anime section */}
        {likedAnime.length > 0 && (
          <div className="w-full mt-8 pt-8 bg-white/5 backdrop-blur-md rounded-xl border border-white/10 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl md:text-2xl font-bold bg-gradient-to-r from-white to-white/70 bg-clip-text text-transparent">
                Anime You Liked
              </h2>
              {likedAnime.length > 3 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setLikedAnime([])}
                  className="bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
                >
                  Clear All
                </Button>
              )}
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {likedAnime.map(anime => (
                <a
                  href={anime.url || `/anime/${anime.mal_id}`}
                  key={anime.mal_id}
                  className="bg-white/10 backdrop-blur-md rounded-lg overflow-hidden hover:ring-2 ring-primary transition-all border border-white/10 group"
                >
                  <div className="relative aspect-[3/4]">
                    <img
                      src={anime.images?.jpg?.large_image_url || anime.images?.webp?.large_image_url}
                      alt={anime.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
                    {anime?.score && (
                      <div className="absolute top-2 right-2 flex items-center gap-1 bg-black/30 backdrop-blur-md px-2 py-0.5 rounded-full border border-white/20">
                        <Star fill="gold" color="gold" size={12} />
                        <span className="text-white text-xs font-medium">{anime.score}</span>
                      </div>
                    )}
                  </div>
                  <div className="p-3 bg-gradient-to-b from-black/50 to-black/20 backdrop-blur-sm">
                    <h3 className="font-medium text-sm line-clamp-1 text-white">{anime.title}</h3>
                    <div className="flex items-center text-xs text-white/70 mt-1">
                      <span>{anime.type || 'TV'}</span>
                      {anime.episodes && <span className="ml-1">• {anime.episodes} eps</span>}
                    </div>
                  </div>
                </a>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RandomAnime;
