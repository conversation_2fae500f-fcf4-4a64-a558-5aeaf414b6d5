import { useState, useEffect } from "react";
import { useAniList } from "@/hooks/useAniList";
import { useUserActivity } from "@/context/UserActivityContext";
import userProgressApi from "@/api/userProgressApi";
import profileApi from "@/api/profileApi";
import {
  Trophy,
  Clock,
  Film,
  PlayCircle,
  Flame,
  TrendingUp,
  Heart,
  Search,
  ChevronDown,
  ChevronUp
} from "lucide-react";
import Image from "@/components/ui/Image";
import { Link } from "react-router-dom";

const Leaderboard = () => {
  const { user, isAuthenticated } = useAniList();
  const { level, rank, streak, likes } = useUserActivity();
  const [leaderboardData, setLeaderboardData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState("watchTime");
  const [sortDirection, setSortDirection] = useState("desc");
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch leaderboard data from API
  useEffect(() => {
    const fetchLeaderboardData = async () => {
      setLoading(true);

      try {
        // Try to fetch leaderboard data from profile API first
        try {
          const leaderboardData = await profileApi.getLeaderboard(sortBy, 10);
          console.log("Leaderboard data fetched from profile API");

          // Process the leaderboard data
          const processedData = await Promise.all(leaderboardData.map(async user => {
            // Try to get user details from AniList
            let userName = `User ${user.anilistId.substring(0, 5)}...`;
            let userAvatar = "https://s4.anilist.co/file/anilistcdn/user/avatar/large/default.png";

            try {
              // This would be a function to fetch user details from AniList API
              // For now, we'll just use placeholder data
              // In a real implementation, you would fetch user details from AniList
            } catch (error) {
              console.error("Error fetching user details from AniList:", error);
            }

            return {
              id: user.anilistId,
              name: userName,
              avatar: userAvatar,
              level: user.level || 1,
              rank: user.rank || "Rookie",
              watchTime: 0, // Placeholder
              animeCount: 0, // Placeholder
              episodesWatched: 0, // Placeholder
              streak: user.streak || 0,
              likes: user.likes || 0
            };
          }));

          setLeaderboardData(processedData);
          setLoading(false);
          return;
        } catch (profileError) {
          console.error("Error fetching leaderboard data from profile API:", profileError);
          console.log("Falling back to legacy API...");
        }

        // Fallback to legacy API
        const leaderboardData = await userProgressApi.getLeaderboard(sortBy, 10);
        console.log("Leaderboard data fetched from legacy API");

        // Process the leaderboard data
        const processedData = leaderboardData.map(user => {
          // If the user data is from the backend (has anilistId)
          if (user.anilistId) {
            return {
              id: user.anilistId,
              name: `User ${user.anilistId.substring(0, 5)}...`, // Placeholder name
              avatar: "https://s4.anilist.co/file/anilistcdn/user/avatar/large/default.png",
              level: user.level || 1,
              rank: user.rank || "Rookie",
              watchTime: 0, // Placeholder
              animeCount: 0, // Placeholder
              episodesWatched: 0, // Placeholder
              streak: user.streak || 0,
              likes: user.likes || 0
            };
          }
          // If it's already in the right format (sample data)
          return user;
        });

        // Add current user to the leaderboard if authenticated and not already in the list
        let allUsers = [...processedData];

        if (isAuthenticated && user) {
          // Check if current user is already in the leaderboard
          const userExists = allUsers.some(u => u.id === user.id);

          if (!userExists) {
            const currentUser = {
              id: user.id,
              name: user.name,
              avatar: user.avatar?.medium || "https://s4.anilist.co/file/anilistcdn/user/avatar/large/default.png",
              level: level,
              rank: rank,
              watchTime: user.statistics?.anime?.minutesWatched
                ? Math.floor(user.statistics.anime.minutesWatched / 60)
                : 0,
              animeCount: user.statistics?.anime?.count || 0,
              episodesWatched: user.statistics?.anime?.episodesWatched || 0,
              streak: streak,
              likes: likes
            };

            allUsers.push(currentUser);
          }
        }

        setLeaderboardData(allUsers);
      } catch (err) {
        console.error("Error fetching leaderboard data from API:", err);

        // Fallback to sample data
        const sampleUsers = [
          {
            id: "sample1",
            name: "AnimeKing",
            avatar: "https://s4.anilist.co/file/anilistcdn/user/avatar/large/default.png",
            level: 78,
            rank: "Anime Legend",
            watchTime: 4320, // hours
            animeCount: 420,
            episodesWatched: 6500,
            streak: 145,
            likes: 872
          },
          {
            id: "sample2",
            name: "OtakuMaster",
            avatar: "https://s4.anilist.co/file/anilistcdn/user/avatar/large/b5398835-P3ItyqUqGxvO.png",
            level: 65,
            rank: "Anime Master",
            watchTime: 3840, // hours
            animeCount: 380,
            episodesWatched: 5800,
            streak: 90,
            likes: 654
          },
          {
            id: "sample3",
            name: "SakuraChan",
            avatar: "https://s4.anilist.co/file/anilistcdn/user/avatar/large/b5398835-7qlAi80JNGcC.jpg",
            level: 52,
            rank: "Anime Master",
            watchTime: 2760, // hours
            animeCount: 310,
            episodesWatched: 4200,
            streak: 60,
            likes: 521
          },
          {
            id: "sample4",
            name: "NarutoFan",
            avatar: "https://s4.anilist.co/file/anilistcdn/user/avatar/large/b5398835-Uf9c1ZVSFW8i.png",
            level: 45,
            rank: "Anime Expert",
            watchTime: 2160, // hours
            animeCount: 250,
            episodesWatched: 3500,
            streak: 45,
            likes: 432
          },
          {
            id: "sample5",
            name: "OnePieceKing",
            avatar: "https://s4.anilist.co/file/anilistcdn/user/avatar/large/b5398835-7OGxkgIVQnib.png",
            level: 38,
            rank: "Anime Veteran",
            watchTime: 1800, // hours
            animeCount: 180,
            episodesWatched: 2800,
            streak: 30,
            likes: 345
          }
        ];

        // Add current user to the leaderboard if authenticated
        let allUsers = [...sampleUsers];

        if (isAuthenticated && user) {
          const currentUser = {
            id: user.id,
            name: user.name,
            avatar: user.avatar?.medium || "https://s4.anilist.co/file/anilistcdn/user/avatar/large/default.png",
            level: level,
            rank: rank,
            watchTime: user.statistics?.anime?.minutesWatched
              ? Math.floor(user.statistics.anime.minutesWatched / 60)
              : 0,
            animeCount: user.statistics?.anime?.count || 0,
            episodesWatched: user.statistics?.anime?.episodesWatched || 0,
            streak: streak,
            likes: likes
          };

          allUsers.push(currentUser);
        }

        setLeaderboardData(allUsers);
        console.log("Using sample leaderboard data (fallback)");
      } finally {
        setLoading(false);
      }
    };

    fetchLeaderboardData();
  }, [isAuthenticated, user, level, rank, streak, likes, sortBy]);

  // Sort and filter leaderboard data
  const sortedAndFilteredData = () => {
    // Filter by search query
    let filtered = leaderboardData;
    if (searchQuery) {
      filtered = leaderboardData.filter(user =>
        user.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Sort by selected column
    return filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case "watchTime":
          comparison = a.watchTime - b.watchTime;
          break;
        case "animeCount":
          comparison = a.animeCount - b.animeCount;
          break;
        case "episodesWatched":
          comparison = a.episodesWatched - b.episodesWatched;
          break;
        case "level":
          comparison = a.level - b.level;
          break;
        case "streak":
          comparison = a.streak - b.streak;
          break;
        case "likes":
          comparison = a.likes - b.likes;
          break;
        default:
          comparison = a.watchTime - b.watchTime;
      }

      return sortDirection === "desc" ? -comparison : comparison;
    });
  };

  // Handle sort column change
  const handleSortChange = async (column) => {
    let newDirection = sortDirection;

    if (sortBy === column) {
      // Toggle sort direction if clicking the same column
      newDirection = sortDirection === "desc" ? "asc" : "desc";
      setSortDirection(newDirection);
    } else {
      // Set new sort column and default to descending
      setSortBy(column);
      newDirection = "desc";
      setSortDirection(newDirection);
    }

    // Reload leaderboard data with new sort parameters
    setLoading(true);

    try {
      // Try to fetch from profile API first
      try {
        const leaderboardData = await profileApi.getLeaderboard(column, 10);
        console.log("Sorted leaderboard data fetched from profile API");

        // Process the leaderboard data
        const processedData = await Promise.all(leaderboardData.map(async user => {
          // Use the same processing logic as in fetchLeaderboardData
          let userName = `User ${user.anilistId.substring(0, 5)}...`;
          let userAvatar = "https://s4.anilist.co/file/anilistcdn/user/avatar/large/default.png";

          return {
            id: user.anilistId,
            name: userName,
            avatar: userAvatar,
            level: user.level || 1,
            rank: user.rank || "Rookie",
            watchTime: 0, // Placeholder
            animeCount: 0, // Placeholder
            episodesWatched: 0, // Placeholder
            streak: user.streak || 0,
            likes: user.likes || 0
          };
        }));

        // Sort according to direction (API might only support one direction)
        const sortedData = newDirection === "asc"
          ? [...processedData].reverse()
          : processedData;

        setLeaderboardData(sortedData);
        setLoading(false);
        return;
      } catch (profileError) {
        console.error("Error fetching sorted leaderboard data from profile API:", profileError);
        console.log("Falling back to legacy API...");
      }

      // Fallback to legacy API
      const leaderboardData = await userProgressApi.getLeaderboard(column, 10);
      console.log("Sorted leaderboard data fetched from legacy API");

      // Sort according to direction (API might only support one direction)
      const sortedData = newDirection === "asc"
        ? [...leaderboardData].reverse()
        : leaderboardData;

      setLeaderboardData(sortedData);
    } catch (err) {
      console.error("Error fetching sorted leaderboard data:", err);
      // We don't need fallback here as the data is already loaded
      // and will be sorted client-side in sortedAndFilteredData()
    } finally {
      setLoading(false);
    }
  };

  // Get sort icon
  const getSortIcon = (column) => {
    if (sortBy !== column) return null;

    return sortDirection === "desc"
      ? <ChevronDown size={14} className="inline" />
      : <ChevronUp size={14} className="inline" />;
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col gap-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Trophy className="text-yellow-500" />
              Anime Leaderboard
            </h1>
            <p className="text-white/60 text-sm mt-1">
              See how you rank against other anime enthusiasts
            </p>
          </div>

          {/* Search */}
          <div className="relative w-full md:w-64">
            <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40" />
            <input
              type="text"
              placeholder="Search users..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-black/30 border border-white/10 rounded-lg py-2 pl-10 pr-4 text-sm focus:outline-none focus:ring-1 focus:ring-white/20"
            />
          </div>
        </div>

        {/* Leaderboard Table */}
        <div className="bg-black/40 backdrop-blur-sm rounded-lg border border-white/10 overflow-hidden">
          {loading ? (
            <div className="flex justify-center items-center py-20">
              <div className="animate-spin w-8 h-8 border-2 border-white/20 border-t-white rounded-full"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-black/50 text-white/80 text-xs">
                    <th className="py-3 px-4 text-left">RANK</th>
                    <th className="py-3 px-4 text-left">USER</th>
                    <th
                      className="py-3 px-4 text-right cursor-pointer hover:text-white"
                      onClick={() => handleSortChange("level")}
                    >
                      LEVEL {getSortIcon("level")}
                    </th>
                    <th
                      className="py-3 px-4 text-right cursor-pointer hover:text-white"
                      onClick={() => handleSortChange("watchTime")}
                    >
                      WATCH TIME {getSortIcon("watchTime")}
                    </th>
                    <th
                      className="py-3 px-4 text-right cursor-pointer hover:text-white"
                      onClick={() => handleSortChange("animeCount")}
                    >
                      ANIME {getSortIcon("animeCount")}
                    </th>
                    <th
                      className="py-3 px-4 text-right cursor-pointer hover:text-white"
                      onClick={() => handleSortChange("episodesWatched")}
                    >
                      EPISODES {getSortIcon("episodesWatched")}
                    </th>
                    <th
                      className="py-3 px-4 text-right cursor-pointer hover:text-white"
                      onClick={() => handleSortChange("streak")}
                    >
                      STREAK {getSortIcon("streak")}
                    </th>
                    <th
                      className="py-3 px-4 text-right cursor-pointer hover:text-white"
                      onClick={() => handleSortChange("likes")}
                    >
                      LIKES {getSortIcon("likes")}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {sortedAndFilteredData().map((userData, index) => {
                    const isCurrentUser = isAuthenticated && user && userData.id === user.id;

                    return (
                      <tr
                        key={userData.id}
                        className={`border-t border-white/5 text-sm ${isCurrentUser ? 'bg-blue-900/20' : 'hover:bg-white/5'}`}
                      >
                        <td className="py-4 px-4">
                          <div className="flex items-center justify-center w-6 h-6 rounded-full bg-black/50 text-xs">
                            {index + 1}
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <Link to={`/profile-new`} className="flex items-center gap-3">
                            <div className="w-8 h-8 rounded-full overflow-hidden">
                              <Image src={userData.avatar} className="w-full h-full object-cover" />
                            </div>
                            <div>
                              <div className="font-medium">{userData.name}</div>
                              <div className="text-xs text-white/60">{userData.rank}</div>
                            </div>
                          </Link>
                        </td>
                        <td className="py-4 px-4 text-right">
                          <div className="flex items-center justify-end gap-1">
                            <TrendingUp size={14} className="text-yellow-500" />
                            <span>{userData.level}</span>
                          </div>
                        </td>
                        <td className="py-4 px-4 text-right">
                          <div className="flex items-center justify-end gap-1">
                            <Clock size={14} className="text-blue-500" />
                            <span>{userData.watchTime} hrs</span>
                          </div>
                        </td>
                        <td className="py-4 px-4 text-right">
                          <div className="flex items-center justify-end gap-1">
                            <Film size={14} className="text-purple-500" />
                            <span>{userData.animeCount}</span>
                          </div>
                        </td>
                        <td className="py-4 px-4 text-right">
                          <div className="flex items-center justify-end gap-1">
                            <PlayCircle size={14} className="text-green-500" />
                            <span>{userData.episodesWatched}</span>
                          </div>
                        </td>
                        <td className="py-4 px-4 text-right">
                          <div className="flex items-center justify-end gap-1">
                            <Flame size={14} className="text-orange-500" />
                            <span>{userData.streak} days</span>
                          </div>
                        </td>
                        <td className="py-4 px-4 text-right">
                          <div className="flex items-center justify-end gap-1">
                            <Heart size={14} className="text-red-500" />
                            <span>{userData.likes}</span>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Leaderboard;
