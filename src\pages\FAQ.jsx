import { useEffect, useState } from "react";
import { ChevronDown, ChevronUp, Search, HelpCircle } from "lucide-react";
import { Link } from "react-router-dom";

const FAQ = () => {
  // Set page title
  useEffect(() => {
    document.title = "FAQ | AnimeHQ";
    return () => {
      document.title = "AnimeHQ"; // Reset title when component unmounts
    };
  }, []);

  // State for search and expanded questions
  const [searchQuery, setSearchQuery] = useState("");
  const [expandedQuestions, setExpandedQuestions] = useState({});

  // Toggle question expansion
  const toggleQuestion = (id) => {
    setExpandedQuestions(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  // FAQ data organized by categories
  const faqData = [
    {
      category: "General",
      questions: [
        {
          id: "what-is-animehq",
          question: "What is AnimeHQ?",
          answer: "AnimeHQ is a platform dedicated to anime enthusiasts, offering a comprehensive library of anime content. Our goal is to provide a seamless experience for discovering, tracking, and enjoying anime."
        },
        {
          id: "is-animehq-free",
          question: "Is AnimeHQ free to use?",
          answer: "Yes, AnimeHQ is completely free to use. We rely on donations to cover server costs and maintain the platform."
        },
        {
          id: "supported-devices",
          question: "What devices can I use to access AnimeHQ?",
          answer: "AnimeHQ is accessible on any device with a web browser, including desktops, laptops, tablets, and smartphones. We've optimized the experience to work well across all screen sizes."
        }
      ]
    },
    {
      category: "Content",
      questions: [
        {
          id: "content-source",
          question: "Where does AnimeHQ get its content?",
          answer: "AnimeHQ aggregates content from various sources and APIs. We don't host any content directly on our servers but provide links to legitimate sources where available."
        },
        {
          id: "request-anime",
          question: "Can I request a specific anime to be added?",
          answer: "We're constantly updating our library based on availability from our sources. While we don't take direct requests, you can report missing content through our contact form, and we'll look into adding it if possible."
        },
        {
          id: "content-quality",
          question: "What video quality is available?",
          answer: "Video quality depends on the source and can range from 480p to 1080p. We always try to provide the highest quality available from our sources."
        }
      ]
    },
    {
      category: "Account & Features",
      questions: [
        {
          id: "create-account",
          question: "Do I need an account to use AnimeHQ?",
          answer: "No, you can browse and watch anime without an account. However, creating an account allows you to track your watch history, create a watchlist, and sync with AniList."
        },
        {
          id: "anilist-sync",
          question: "How does AniList synchronization work?",
          answer: "You can connect your AniList account to automatically update your watch progress. When you watch an episode on AnimeHQ, your AniList account will be updated accordingly."
        },
        {
          id: "watchlist-feature",
          question: "How do I add anime to my watchlist?",
          answer: "When logged in, you can add anime to your watchlist by clicking the bookmark icon on any anime page. Your watchlist is accessible from your profile or the sidebar."
        }
      ]
    },
    {
      category: "Technical",
      questions: [
        {
          id: "buffering-issues",
          question: "Why am I experiencing buffering issues?",
          answer: "Buffering can occur due to various factors including your internet connection speed, server load, or the source of the content. Try lowering the video quality, refreshing the page, or trying at a different time."
        },
        {
          id: "browser-compatibility",
          question: "Which browsers are supported?",
          answer: "AnimeHQ works best on modern browsers like Chrome, Firefox, Safari, and Edge. Make sure your browser is updated to the latest version for optimal performance."
        },
        {
          id: "report-bugs",
          question: "How do I report bugs or issues?",
          answer: "You can report bugs through our contact form or by sending an <NAME_EMAIL>. Please include details about the issue, your device, browser, and steps to reproduce the problem."
        }
      ]
    },
    {
      category: "Support",
      questions: [
        {
          id: "contact-support",
          question: "How can I contact support?",
          answer: "You can reach our support team by sending an <NAME_EMAIL>. We aim to respond to all inquiries within 48 hours."
        },
        {
          id: "support-animehq",
          question: "How can I support AnimeHQ?",
          answer: "You can support AnimeHQ by donating through cryptocurrency. Your donations help us cover server costs and maintain the platform. Donors get their names added to our supporters list and receive an exclusive Discord role."
        },
        {
          id: "join-community",
          question: "Is there a community I can join?",
          answer: "Yes, we have a Discord community where you can discuss anime, get updates about the platform, and connect with other anime enthusiasts. You can find the link to join in the footer of our website."
        }
      ]
    }
  ];

  // Filter questions based on search query
  const filteredFAQ = searchQuery.trim() === "" 
    ? faqData 
    : faqData.map(category => ({
        ...category,
        questions: category.questions.filter(q => 
          q.question.toLowerCase().includes(searchQuery.toLowerCase()) || 
          q.answer.toLowerCase().includes(searchQuery.toLowerCase())
        )
      })).filter(category => category.questions.length > 0);

  return (
    <div className="py-8 px-4 sm:px-6 lg:px-8 max-w-5xl mx-auto">
      <h1 className="text-3xl font-bold mb-8 text-center">Frequently Asked Questions</h1>
      
      {/* Search bar */}
      <div className="mb-8 max-w-xl mx-auto">
        <div className="relative">
          <input
            type="text"
            placeholder="Search questions..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-3 pl-10 text-white focus:outline-none focus:ring-1 focus:ring-white/30"
          />
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-white/50" size={18} />
          
          {searchQuery && (
            <button
              onClick={() => setSearchQuery("")}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-white/50 hover:text-white"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          )}
        </div>
      </div>
      
      {/* FAQ content */}
      <div className="space-y-8">
        {filteredFAQ.length > 0 ? (
          filteredFAQ.map((category, index) => (
            category.questions.length > 0 && (
              <div key={index} className="bg-white/5 rounded-xl p-6 backdrop-blur-sm border border-white/10">
                <h2 className="text-xl font-bold mb-4 flex items-center gap-2">
                  <HelpCircle className="text-white" size={20} />
                  {category.category}
                </h2>
                
                <div className="space-y-4">
                  {category.questions.map((item) => (
                    <div key={item.id} className="border-b border-white/10 last:border-b-0 pb-4 last:pb-0">
                      <button
                        onClick={() => toggleQuestion(item.id)}
                        className="flex justify-between items-center w-full text-left py-2 focus:outline-none group"
                      >
                        <h3 className="text-lg font-medium group-hover:text-white/90">{item.question}</h3>
                        {expandedQuestions[item.id] ? (
                          <ChevronUp className="text-white/70" size={20} />
                        ) : (
                          <ChevronDown className="text-white/70" size={20} />
                        )}
                      </button>
                      
                      {expandedQuestions[item.id] && (
                        <div className="mt-2 text-gray-300 pl-1">
                          <p>{item.answer}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )
          ))
        ) : (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-4">
              <HelpCircle className="text-white/50" size={32} />
            </div>
            <h3 className="text-xl font-medium mb-2">No questions found</h3>
            <p className="text-gray-400">Try a different search term or browse all questions</p>
            <button
              onClick={() => setSearchQuery("")}
              className="mt-4 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors"
            >
              Clear search
            </button>
          </div>
        )}
      </div>
      
      {/* Contact section */}
      <div className="mt-12 text-center">
        <h2 className="text-xl font-bold mb-4">Still have questions?</h2>
        <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
          If you couldn't find the answer to your question, feel free to contact us directly.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <a 
            href="mailto:<EMAIL>" 
            className="px-6 py-3 bg-white/10 hover:bg-white/20 rounded-lg transition-colors inline-flex items-center justify-center gap-2"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
            Email Support
          </a>
          <Link 
            to="/about" 
            className="px-6 py-3 bg-white/10 hover:bg-white/20 rounded-lg transition-colors inline-flex items-center justify-center gap-2"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="16" x2="12" y2="12"></line>
              <line x1="12" y1="8" x2="12.01" y2="8"></line>
            </svg>
            About Us
          </Link>
        </div>
      </div>
    </div>
  );
};

export default FAQ;
