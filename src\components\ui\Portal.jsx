import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

/**
 * Portal component for rendering children in a different part of the DOM
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Elements to render in the portal
 * @param {string} props.className - Optional class name for the portal container
 * @param {number} props.zIndex - Optional z-index for the portal container
 */
const Portal = ({ children, className = 'portal-container', zIndex = 99999 }) => {
  const [container] = useState(() => {
    // This is only executed once on component mount
    return document.createElement('div');
  });

  useEffect(() => {
    // Add portal container to the DOM
    document.body.appendChild(container);
    container.className = className;
    container.style.position = 'fixed';
    container.style.zIndex = zIndex;
    container.style.top = '0';
    container.style.left = '0';
    container.style.width = '100%';
    container.style.height = '0';
    container.style.overflow = 'visible';
    container.style.pointerEvents = 'none';

    // Clean up on unmount
    return () => {
      document.body.removeChild(container);
    };
  }, [container, className, zIndex]);

  return createPortal(
    <div style={{ pointerEvents: 'auto' }}>
      {children}
    </div>,
    container);
};

export default Portal;
