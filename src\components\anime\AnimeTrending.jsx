import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import Image from "@/components/ui/Image";
import { Link } from "react-router-dom";
import { Dot, Star, AlertCircle } from "lucide-react";
import { getTrendingAnime } from "@/api/anilist";
import useFetch from "@/hooks/useFetch";
import Autoplay from "embla-carousel-autoplay";
import AgeRating from "@/components/ui/AgeRating";

const AnimeTrending = () => {
  const { data } = useFetch({
    key: ["trending-anime"],
    fun: async () => {
      return (await getTrendingAnime()) || null;
    },
    placeholderData: [],
  });

  return (
    <div className="w-full flex flex-col gap-2">
      <div className="flex text-xl lg:text-2xl font-medium pl-1">
        Trending Anime
      </div>
      <div className="w-full">
        <Carousel
          plugins={[
            Autoplay({
              delay: 4000,
            }),
          ]}
          opts={{ dragFree: true, dragThreshold: 100 }}
          className="w-full"
        >
          <CarouselContent className="w-full">
            {data?.length
              ? data?.map((x) => {
                  return (
                    <CarouselItem
                      key={x?.id}
                      className="basis-[80%] sm:basis-[40%] lg:basis-1/4 p-[.4rem] aspect-[1.88/1] sm:p-2"
                    >
                      <div
                        className="flex size-full flex-col gap-2 group pp cursor-pointer hover:ring-2 smooth ring-white rounded-2xl lg:rounded-3xl overflow-hidden relative"
                      >
                        <div className="flex size-full pointer-events-none bg-white/10 lg:bg-white/5 smooth absolute top-0 left-0 z-[-1] group-hover:scale-105">
                          <Image src={x?.images?.bannerLarge || x?.images?.bannerSmall} quality="high" />
                          <AgeRating isAdult={x?.isAdult} genres={x?.genres} position="top-2 right-2" compact={true} />
                        </div>
                        <div className="flex flex-col mt-auto p-2 sm:p-3 bg-gradient-to-t from-black/90 via-black/60 to-transparent w-full">
                          <div className="flex gap-1 items-center text-xs">
                            <span className="flex items-center gap-1">
                              <Star fill="gold" color="gold" size={12} />
                              {x?.rating}
                            </span>
                            <Dot size={16} />
                            <span>{x?.episodes} Eps</span>
                            <Dot size={16} />
                            <span>{x?.release_date?.slice(-4)}</span>
                          </div>
                          <div className="line-clamp-1 text-sm sm:text-base font-medium">
                            {x?.title}
                          </div>
                        </div>
                        <Link
                          to={x?.id ? `/anime/${x?.id}` : ""}
                          className="absolute inset-0 z-10"
                        ></Link>
                      </div>
                    </CarouselItem>
                  );
                })
              : Array.from({ length: 8 }).map((_, i) => (
                  <CarouselItem
                    key={i}
                    className="basis-[70%] sm:basis-[45%] md:basis-[35%] lg:basis-[30%] p-[.3rem] sm:p-[.4rem] md:p-2 aspect-[1.88/1]"
                  >
                    <div className="flex size-full bg-white/5 animate-pulse rounded-2xl lg:rounded-3xl"></div>
                  </CarouselItem>
                ))}
          </CarouselContent>
        </Carousel>
      </div>
    </div>
  );
};

export default AnimeTrending;
