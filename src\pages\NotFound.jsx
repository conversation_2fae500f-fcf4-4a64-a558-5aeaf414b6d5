import { useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Home, ArrowLeft, Search } from "lucide-react";

const NotFound = () => {
  const navigate = useNavigate();

  // Set page title and add glitch styles
  useEffect(() => {
    document.title = "404 - Page Not Found | AnimeHQ";

    // Add glitch animation styles
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      @keyframes glitch {
        0% {
          clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
          transform: translate(0);
        }
        10% {
          clip-path: polygon(0 10%, 100% 0, 100% 90%, 0 100%);
          transform: translate(-3px, 1px);
        }
        20% {
          clip-path: polygon(0 0, 100% 10%, 100% 100%, 0 90%);
          transform: translate(3px, 1px);
        }
        30% {
          clip-path: polygon(0 30%, 100% 20%, 100% 70%, 0 80%);
          transform: translate(-3px, -1px);
        }
        40% {
          clip-path: polygon(0 20%, 100% 30%, 100% 80%, 0 70%);
          transform: translate(3px, -1px);
        }
        50% {
          clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
          transform: translate(0);
        }
        60% {
          clip-path: polygon(0 60%, 100% 50%, 100% 100%, 0 90%);
          transform: translate(-3px, 1px);
        }
        70% {
          clip-path: polygon(0 50%, 100% 60%, 100% 90%, 0 100%);
          transform: translate(3px, 1px);
        }
        80% {
          clip-path: polygon(0 70%, 100% 80%, 100% 100%, 0 90%);
          transform: translate(-3px, -1px);
        }
        90% {
          clip-path: polygon(0 80%, 100% 70%, 100% 90%, 0 100%);
          transform: translate(3px, -1px);
        }
        100% {
          clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
          transform: translate(0);
        }
      }

      @keyframes glitch-line {
        0% {
          transform: translateX(-100%);
        }
        50% {
          transform: translateX(100%);
        }
        100% {
          transform: translateX(-100%);
        }
      }

      @keyframes pulse-slow {
        0%, 100% {
          opacity: 0.3;
        }
        50% {
          opacity: 0.6;
        }
      }

      .animate-glitch {
        animation: glitch 2s infinite;
      }

      .animate-glitch-line {
        animation: glitch-line 3s infinite linear;
      }

      .animate-pulse-slow {
        animation: pulse-slow 4s infinite;
      }
    `;
    document.head.appendChild(styleElement);

    return () => {
      document.title = "AnimeHQ"; // Reset title when component unmounts
      document.head.removeChild(styleElement); // Clean up styles
    };
  }, []);

  return (
    <div className="min-h-[80vh] flex flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-3xl mx-auto text-center">
        {/* 404 Text with Glitch Effect */}
        <div className="relative mb-6">
          <h1 className="text-[120px] sm:text-[150px] md:text-[180px] font-bold leading-none tracking-tighter text-white opacity-10">
            404
          </h1>
          <div className="absolute inset-0 flex items-center justify-center">
            <h1 className="text-[120px] sm:text-[150px] md:text-[180px] font-bold leading-none tracking-tighter text-white animate-pulse">
              404
            </h1>
          </div>
          <div className="absolute inset-0 flex items-center justify-center">
            <h1 className="text-[120px] sm:text-[150px] md:text-[180px] font-bold leading-none tracking-tighter bg-clip-text text-transparent bg-gradient-to-b from-white to-white/20 animate-glitch">
              404
            </h1>
          </div>
        </div>

        {/* Anime-style character silhouette */}
        <div className="relative w-64 h-64 mx-auto mb-8">
          <div className="absolute inset-0 bg-white/5 rounded-full animate-pulse-slow"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <svg
              viewBox="0 0 100 100"
              className="w-full h-full opacity-80"
              fill="none"
              stroke="currentColor"
              strokeWidth="1"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              {/* Simplified anime character silhouette */}
              <path d="M50,20 C60,20 70,25 70,40 C70,50 65,55 60,60 C55,65 55,70 55,75 L45,75 C45,70 45,65 40,60 C35,55 30,50 30,40 C30,25 40,20 50,20 Z" />
              <circle cx="40" cy="35" r="3" />
              <circle cx="60" cy="35" r="3" />
              <path d="M43,45 C45,48 55,48 57,45" strokeLinecap="round" />
              <path d="M35,30 L30,25" />
              <path d="M65,30 L70,25" />
              <path d="M50,75 L50,85" />
              <path d="M45,85 L55,85" />
            </svg>
          </div>
        </div>

        <h2 className="text-3xl font-bold mb-4">Page Not Found</h2>
        <p className="text-gray-300 text-lg mb-8 max-w-xl mx-auto">
          Looks like this page has disappeared into another dimension. Perhaps it's on a journey in an isekai world?
        </p>

        {/* Glitchy line */}
        <div className="w-full max-w-md mx-auto h-px bg-white/20 relative mb-8 overflow-hidden">
          <div className="absolute inset-0 w-full h-full">
            <div className="h-full w-[20%] bg-white/40 animate-glitch-line"></div>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={() => navigate(-1)}
            className="px-6 py-3 bg-white/10 hover:bg-white/20 rounded-lg transition-colors inline-flex items-center justify-center gap-2"
          >
            <ArrowLeft size={18} />
            Go Back
          </button>
          <Link
            to="/"
            className="px-6 py-3 bg-white/10 hover:bg-white/20 rounded-lg transition-colors inline-flex items-center justify-center gap-2"
          >
            <Home size={18} />
            Home Page
          </Link>
          <Link
            to="/explore"
            className="px-6 py-3 bg-white/10 hover:bg-white/20 rounded-lg transition-colors inline-flex items-center justify-center gap-2"
          >
            <Search size={18} />
            Explore Anime
          </Link>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
