import { useState, useRef, useEffect } from 'react';
import Image from '@/components/ui/Image';
import { Star, X, Heart, ExternalLink } from 'lucide-react';
import AgeRating from '@/components/ui/AgeRating';

const AnimeSwipeCard = ({
  anime,
  onSwipe,
  isActive = false,
  index
}) => {
  const [startX, setStartX] = useState(0);
  const [currentX, setCurrentX] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const cardRef = useRef(null);

  // Reset position when card becomes active
  useEffect(() => {
    if (isActive) {
      setCurrentX(0);
    }
  }, [isActive]);

  const handleTouchStart = (e) => {
    setStartX(e.touches[0].clientX);
    setIsDragging(true);
  };

  const handleMouseDown = (e) => {
    setStartX(e.clientX);
    setIsDragging(true);
  };

  const handleTouchMove = (e) => {
    if (!isDragging) return;
    const currentX = e.touches[0].clientX - startX;
    setCurrentX(currentX);
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    const currentX = e.clientX - startX;
    setCurrentX(currentX);
  };

  const handleTouchEnd = () => {
    handleSwipeEnd();
  };

  const handleMouseUp = () => {
    handleSwipeEnd();
  };

  const handleSwipeEnd = () => {
    setIsDragging(false);

    // Determine if the swipe was significant enough
    const threshold = window.innerWidth * 0.3; // 30% of screen width

    if (currentX > threshold) {
      // Swiped right (like)
      onSwipe('right', anime);
    } else if (currentX < -threshold) {
      // Swiped left (dislike)
      onSwipe('left', anime);
    } else {
      // Not enough movement, reset position
      setCurrentX(0);
    }
  };

  const handleLikeClick = (e) => {
    e.stopPropagation();
    onSwipe('right', anime);
  };

  const handleDislikeClick = (e) => {
    e.stopPropagation();
    onSwipe('left', anime);
  };

  // Calculate rotation based on swipe distance
  const rotation = currentX / 20; // Gentle rotation

  // Determine which action indicator to show
  const showLike = currentX > 50;
  const showDislike = currentX < -50;

  // Calculate z-index based on card position
  const zIndex = 1000 - index;

  return (
    <div
      ref={cardRef}
      className={`absolute top-0 left-0 w-full h-full transition-all duration-200 ${!isActive ? 'pointer-events-none' : ''}`}
      style={{
        transform: `translateX(${currentX}px) rotate(${rotation}deg)`,
        zIndex
      }}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
    >
      <div className="relative w-full h-full bg-gray-900 rounded-xl overflow-hidden shadow-xl">
        {/* Image */}
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-black/50 to-black">
          <Image
            src={anime?.images?.jpg?.large_image_url || anime?.images?.webp?.large_image_url}
            className="w-full h-full object-cover"
            quality="high"
          />
        </div>

        {/* Content overlay */}
        <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-2xl font-bold mb-1 line-clamp-2">{anime?.title}</h2>
              <div className="flex items-center gap-2 mb-2">
                {anime?.score && (
                  <div className="flex items-center gap-1 bg-black/50 px-2 py-1 rounded-md">
                    <Star fill="gold" color="gold" size={16} />
                    <span>{anime.score}</span>
                  </div>
                )}
                <AgeRating isAdult={anime?.isAdult || anime?.rating === 'Rx'} genres={anime?.genres} />
                <span className="text-sm">{anime?.type || 'TV'}</span>
                {anime?.episodes && <span className="text-sm">{anime.episodes} eps</span>}
              </div>
              <p className="text-sm line-clamp-3 text-gray-200">{anime?.synopsis}</p>
            </div>
          </div>
        </div>

        {/* Like/Dislike indicators */}
        {showLike && (
          <div className="absolute top-10 right-10 bg-green-500 text-white px-4 py-2 rounded-lg transform rotate-12 font-bold text-xl border-2 border-white">
            LIKE
          </div>
        )}
        {showDislike && (
          <div className="absolute top-10 left-10 bg-red-500 text-white px-4 py-2 rounded-lg transform -rotate-12 font-bold text-xl border-2 border-white">
            PASS
          </div>
        )}

        {/* Action buttons */}
        <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-8 items-center">
          <button
            onClick={handleDislikeClick}
            className="bg-red-500 text-white p-3 rounded-full shadow-lg hover:bg-red-600 transition-colors"
          >
            <X size={24} />
          </button>

          <a
            href={anime?.url || `https://myanimelist.net/anime/${anime?.mal_id}`}
            target="_blank"
            rel="noopener noreferrer"
            className="bg-blue-500 text-white p-3 rounded-full shadow-lg hover:bg-blue-600 transition-colors"
            onClick={(e) => e.stopPropagation()}
          >
            <ExternalLink size={24} />
          </a>

          <button
            onClick={handleLikeClick}
            className="bg-green-500 text-white p-3 rounded-full shadow-lg hover:bg-green-600 transition-colors"
          >
            <Heart size={24} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default AnimeSwipeCard;
