import useWatchlist from "@/hooks/useWatchlist";
import { Bookmark } from "lucide-react";
import { useMemo } from "react";
import { trackAddAnime } from "@/utils/activityTracking";

const AddToList = ({ size, info }) => {
  const { inList, addToWatchList, removeFromWatchList } = useWatchlist();
  const i = useMemo(() => {
    return {
      id: Number(info?.id),
      type: info?.type,
      image: info?.images?.coverSmall || info?.image,
      rating: info?.rating,
      release_date: info?.release_date,
      title: info?.title,
    };
  }, [info]);
  const ia = inList(i?.id, i?.type);
  return (
    <button
      onClick={() => {
        if (ia) {
          removeFromWatchList(i?.id, i?.type);
        } else {
          addToWatchList(i);

          // Track that the user added an anime to their list for task verification
          const anilistStorage = localStorage.getItem('anilist');
          if (anilistStorage) {
            try {
              const anilistData = JSON.parse(anilistStorage);
              if (anilistData.id) {
                trackAddAnime(anilistData.id, i);
              }
            } catch (error) {
              console.error('Error tracking add anime:', error);
            }
          }
        }
      }}
      className="flex items-center justify-center smooth pp"
    >
      <Bookmark
        strokeWidth={2.5}
        size={size || 20}
        fill={ia ? "white" : "transparent"}
        className="drop-shadow-2xl"
      />
    </button>
  );
};

export default AddToList;
