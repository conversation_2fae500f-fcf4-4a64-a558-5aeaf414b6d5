import { useState, useEffect, memo } from "react";
import { <PERSON> } from "react-router-dom";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from "@/components/ui/carousel";
import Image from "@/components/ui/Image";
import { exploreAnime } from "@/api/anilist";
import { Loader2, Eye, ArrowRight, Layers } from "lucide-react";

// List of popular anime genres with their associated image keywords for better visuals
const GENRES = [
  { id: "action", name: "Action", imageKeyword: "action" },
  { id: "romance", name: "Romance", imageKeyword: "romance" },
  { id: "comedy", name: "Comedy", imageKeyword: "comedy" },
  { id: "fantasy", name: "Fantasy", imageKeyword: "fantasy" },
  { id: "drama", name: "Drama", imageKeyword: "drama" },
  { id: "sci-fi", name: "Sci-Fi", imageKeyword: "sci-fi" },
  { id: "slice-of-life", name: "Slice of Life", imageKeyword: "slice of life" },
  { id: "mystery", name: "Mystery", imageKeyword: "mystery" },
  { id: "supernatural", name: "Supernatural", imageKeyword: "supernatural" },
  { id: "horror", name: "Horror", imageKeyword: "horror" },
  { id: "adventure", name: "Adventure", imageKeyword: "adventure" },
  { id: "psychological", name: "Psychological", imageKeyword: "psychological" },
];

const GenreCarousel = () => {
  const [genreImages, setGenreImages] = useState({});
  const [loading, setLoading] = useState(true);

  // Fetch representative images for genres in batches to reduce API load
  useEffect(() => {
    const fetchGenreImages = async () => {
      setLoading(true);

      // Check if we have cached genre images and if they're still valid (less than 24 hours old)
      const cachedData = localStorage.getItem('genreCarouselImages');
      if (cachedData) {
        try {
          const { images, timestamp } = JSON.parse(cachedData);
          const cacheAge = Date.now() - timestamp;
          const cacheValidDuration = 24 * 60 * 60 * 1000; // 24 hours

          if (cacheAge < cacheValidDuration) {
            console.log('Using cached genre images');
            setGenreImages(images);
            setLoading(false);
            return;
          }
        } catch (error) {
          console.error('Error parsing cached genre images:', error);
          // Continue with fetching new data if cache parsing fails
        }
      }

      // Process genres in batches of 3 to avoid overwhelming the API
      const batchSize = 3;
      const imageMap = {};

      for (let i = 0; i < GENRES.length; i += batchSize) {
        const batch = GENRES.slice(i, i + batchSize);

        // Process each batch in parallel
        const batchPromises = batch.map(async (genre) => {
          try {
            // Fetch one anime from each genre to use as the genre card image
            const result = await exploreAnime({
              genre: genre.name,
              perPage: 3, // Get a few results to have options
              sort: "POPULARITY_DESC" // Get the most popular for better recognition
            });

            // Find an anime with a banner image if possible
            const animeWithBanner = result.results.find(anime => anime.images?.bannerLarge);
            const animeWithCover = result.results.find(anime => anime.images?.coverLarge);

            return {
              genre: genre.id,
              image: animeWithBanner?.images?.bannerLarge ||
                     animeWithCover?.images?.coverLarge ||
                     null
            };
          } catch (error) {
            console.error(`Error fetching image for genre ${genre.name}:`, error);
            return { genre: genre.id, image: null };
          }
        });

        const batchResults = await Promise.all(batchPromises);

        // Add batch results to the image map
        batchResults.forEach(({ genre, image }) => {
          imageMap[genre] = image;
        });

        // Small delay between batches to be kind to the API
        if (i + batchSize < GENRES.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // Cache the results with a timestamp
      try {
        localStorage.setItem('genreCarouselImages', JSON.stringify({
          images: imageMap,
          timestamp: Date.now()
        }));
      } catch (error) {
        console.error('Error caching genre images:', error);
        // Continue even if caching fails
      }

      setGenreImages(imageMap);
      setLoading(false);
    };

    fetchGenreImages();
  }, []);

  return (
    <div className="w-full flex flex-col gap-5">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <div className="bg-black/40 backdrop-blur-sm p-2 rounded-md border border-white/10">
            <Layers size={16} className="text-white" />
          </div>
          <h2 className="text-xl lg:text-2xl font-semibold">Browse by Genre</h2>
        </div>
        <Link
          to="/explore"
          className="bg-black/40 backdrop-blur-sm hover:bg-black/60 text-white px-3 py-1.5 rounded-md text-sm transition-all duration-200 flex items-center gap-2 border border-white/10 hover:border-white/20"
        >
          <span>View All Genres</span>
          <Eye size={14} />
        </Link>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-16 bg-white/5 rounded-xl">
          <div className="flex flex-col items-center gap-3">
            <Loader2 className="w-8 h-8 text-white/70 animate-spin" />
            <div className="text-sm text-white/70">Loading genres...</div>
          </div>
        </div>
      ) : (
        <div className="relative">
          <Carousel
            opts={{
              align: "start",
              loop: true,
            }}
            className="w-full"
          >
            <CarouselContent className="-ml-4">
              {GENRES.map((genre, index) => (
                <CarouselItem
                  key={genre.id}
                  className="pl-4 basis-1/2 sm:basis-1/3 md:basis-1/4 lg:basis-1/5"
                >
                  <Link
                    to={`/explore?genre=${genre.name}`}
                    className="group block relative overflow-hidden rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300 shadow-lg"
                  >
                    {/* Background image with glassy overlay */}
                    <div className="absolute inset-0">
                      {genreImages[genre.id] ? (
                        <Image
                          src={genreImages[genre.id]}
                          className="object-cover w-full h-full group-hover:scale-110 transition-all duration-500"
                        />
                      ) : (
                        <div className="w-full h-full bg-black/40 backdrop-blur-sm"></div>
                      )}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-black/20 backdrop-blur-[2px] opacity-70 group-hover:opacity-80 transition-opacity duration-300"></div>
                    </div>

                    {/* Content - Smaller on mobile */}
                    <div className="relative aspect-[1.8/1] sm:aspect-[1.8/1] flex flex-col justify-between p-3 sm:p-5">
                      {/* Big faded number - Hidden on mobile */}
                      <div className="absolute bottom-0 right-2 text-white/10 font-bold text-[80px] sm:text-[120px] leading-none pointer-events-none select-none hidden sm:block">
                        {index + 1}
                      </div>

                      {/* Genre name - Smaller on mobile */}
                      <div className="relative z-10 mt-auto">
                        <h3 className="text-lg sm:text-2xl font-bold text-white">{genre.name}</h3>
                        <div className="mt-1 sm:mt-2 bg-white/10 backdrop-blur-sm w-8 sm:w-12 h-1 rounded-full"></div>
                      </div>

                      {/* Explore button - Hidden on mobile, visible on hover for larger screens */}
                      <div className="relative z-10 mt-2 sm:mt-4 self-start hidden sm:block">
                        <div className="bg-black/40 backdrop-blur-sm hover:bg-black/60 text-white px-3 py-1.5 rounded-md text-sm transition-all duration-200 flex items-center gap-2 border border-white/10 group-hover:border-white/20 w-fit">
                          <span>Explore</span>
                          <ArrowRight size={14} className="group-hover:translate-x-1 transition-transform" />
                        </div>
                      </div>
                    </div>

                    {/* Hover effect */}
                    <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </Link>
                </CarouselItem>
              ))}
            </CarouselContent>

            <div className="absolute -bottom-12 left-0 right-0 flex justify-center gap-2 mt-4">
              <CarouselPrevious className="static translate-y-0 bg-black/40 backdrop-blur-sm hover:bg-black/60 border border-white/10 hover:border-white/20 text-white h-8 w-8" />
              <CarouselNext className="static translate-y-0 bg-black/40 backdrop-blur-sm hover:bg-black/60 border border-white/10 hover:border-white/20 text-white h-8 w-8" />
            </div>
          </Carousel>
        </div>
      )}
    </div>
  );
};

export default memo(GenreCarousel);
