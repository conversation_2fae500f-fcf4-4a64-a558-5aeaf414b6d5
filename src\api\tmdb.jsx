import axios from "axios";
import { formatDetails } from "./format.jsx";
const now_playing =
  "movie/now_playing?append_to_response=images,videos&language=en-US&page=1&include_image_language=null";
export const tmdb = axios.create({ baseURL: "https://api.cinehq.lol/proxy/" });
const trending_all = "trending/all/day?language=en-US";

export const getTrending = async (type = "all") => {
  try {
    const { data } = await tmdb.get(
      type === "all" ? trending_all : `/trending/${type}/week?language=en-US`
    );
    return formatDetails(data?.results || null);
  } catch (e) {
    console.log("trending", type, e?.message);
    return null;
  }
};
export const getNowPlaying = async () => {
  try {
    const { data } = await tmdb.get(now_playing);
    return formatDetails(data?.results, "movie");
  } catch (e) {
    console.log("trending", e?.message);
    return null;
  }
};
export const getPopular = async (type = "movie") => {
  try {
    const { data } = await tmdb.get(`/${type}/popular?language=en-US&page=1`);
    return formatDetails(data?.results, type);
  } catch (e) {
    console.log("getPopular", e?.message);
    return null;
  }
};
export const getTopRated = async (type) => {
  try {
    const { data } = await tmdb.get(`/${type}/top_rated?language=en-US&page=1`);
    return formatDetails(data?.results, type);
  } catch (e) {
    console.log("getTopRated", e?.message);
    return null;
  }
};

export const getHomeData = async () => {
  try {
    const [tm, ts, pm, ps, trm, trs] = await Promise.all([
      getTrending("movie"),
      getTrending("tv"),
      getPopular("movie"),
      getPopular("tv"),
      getTopRated("movie"),
      getTopRated("tv"),
    ]);

    return [
      { title: "Trending Movies", data: tm },
      { title: "Trending Shows", data: ts },
      {
        title: "Most Popular",
        data: pm?.concat(ps),
      },
      {
        title: "Top Rated",
        data: trm
          ?.concat(trs)
          ?.sort((a, b) => Number(b?.rating) - Number(a?.rating)),
      },
    ];
  } catch (e) {
    console.log("home", e?.message);
  }
};
export const fetchEpisodes = async ({ id, season_number }) => {
  try {
    const { data } = await tmdb.get(
      `tv/${id}/season/${season_number}?language=en-US`
    );
    // Return raw data since formatEpisodes is no longer available
    return data?.episodes ?? [];
  } catch (e) {
    // console.log(e?.message);
    return null;
  }
};
export const fetchInfo = async ({ type, id }) => {
  try {
    const { data } = await tmdb.get(
      `${type}/${id}?append_to_response=season,videos,recommendations,images,credits`
    );

    return data ? formatDetails(data, type) : null;
  } catch (e) {
    // console.log("info", e?.message);
    return null;
  }
};

export const getSearch = async (q) => {
  try {
    const { data } = await tmdb.get(
      `search/multi?include_adult=false&language=en-US&page=1&query=${q}`
    );
    return {
      ...data,
      results: formatDetails(data?.results),
    };
  } catch (e) {
    return {
      results: [],
      page,
      total_pages: page,
    };
  }
};
export const getExplore = async (u) => {
  try {
    const {
      type = "movie",
      genre,
      year,
      page = 1,
      country,
      studio,
      sort = "popularity.desc",
    } = u || {};
    const today = new Date().toLocaleDateString("en-CA");
    let url = `discover/${type}?page=${page}&sort_by=${sort}&language=en-US&append_to_response=images`;

    if (sort.includes("_date")) {
      url += `${
        type === "movie" ? "&release_date.lte=" : "&first_air_date.lte="
      }${today}&vote_count.gte=1`;
    }
    if (genre && genre !== "any") url += `&with_genres=${genre}`;
    if (year && year !== "any")
      url += type === "tv" ? `&first_air_date_year=${year}` : `&year=${year}`;
    if (country && country !== "any") url += `&with_origin_country=${country}`;
    if (studio && studio !== "any") url += `&with_companies=${studio}`;

    const { data } = await tmdb.get(url);
    return data;
  } catch (error) {
    console.log(`explore`, error?.message);
    return null;
  }
};


