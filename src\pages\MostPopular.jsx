import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { getTrendingAnime, getPopularAnime, getTopRatedAnime } from "@/api/anilist";
import Image from "@/components/ui/Image";
import { Loader2, Star } from "lucide-react";
import AgeRating from "@/components/ui/AgeRating";

const MostPopular = () => {
  const [activeTab, setActiveTab] = useState("24h");
  const [animeList, setAnimeList] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    document.title = "Most Popular Anime | AnimeHQ";
    fetchAnimeData();
  }, [activeTab]);

  const fetchAnimeData = async () => {
    setLoading(true);
    try {
      let data;

      // Fetch different data based on the active tab
      switch (activeTab) {
        case "24h":
          data = await getTrendingAnime();
          break;
        case "7days":
          data = await getPopularAnime();
          break;
        case "30days":
          data = await getTopRatedAnime();
          break;
        default:
          data = await getTrendingAnime();
      }

      setAnimeList(data || []);
    } catch (error) {
      console.error("Error fetching anime data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  return (
    <div className="w-full">
      {/* Header section */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-white mb-4">Most Popular</h1>

        {/* Time period tabs */}
        <div className="flex space-x-4 mb-6">
          <button
            onClick={() => handleTabChange("24h")}
            className={`px-4 py-2 text-sm transition-colors ${
              activeTab === "24h"
                ? "text-white border-b-2 border-white font-medium"
                : "text-white/50 hover:text-white/80"
            }`}
          >
            Last 24h
          </button>
          <button
            onClick={() => handleTabChange("7days")}
            className={`px-4 py-2 text-sm transition-colors ${
              activeTab === "7days"
                ? "text-white border-b-2 border-white font-medium"
                : "text-white/50 hover:text-white/80"
            }`}
          >
            Last 7 days
          </button>
          <button
            onClick={() => handleTabChange("30days")}
            className={`px-4 py-2 text-sm transition-colors ${
              activeTab === "30days"
                ? "text-white border-b-2 border-white font-medium"
                : "text-white/50 hover:text-white/80"
            }`}
          >
            Last 30 days
          </button>
        </div>
      </div>

      {/* Loading state */}
      {loading && (
        <div className="flex items-center justify-center py-20">
          <Loader2 className="w-10 h-10 text-primary animate-spin" />
        </div>
      )}

      {/* Anime grid */}
      {!loading && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {animeList.slice(0, 12).map((anime, index) => (
            <div
              key={anime?.id}
              className="rounded-xl overflow-hidden relative group hover:bg-black/60 transition-all duration-500 border border-white/10 hover:border-white/20 shadow-lg hover:shadow-xl"
            >
              {/* Background banner image */}
              <div className="absolute inset-0 z-0 overflow-hidden">
                <Image
                  src={anime?.images?.bannerLarge || anime?.images?.bannerSmall || anime?.images?.coverLarge}
                  className="w-full h-full object-cover opacity-20 group-hover:opacity-30 transition-opacity duration-500 scale-110 group-hover:scale-125 transition-transform"
                  quality="medium"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/95 via-black/80 to-black/60"></div>
              </div>

              {/* Large faded rank number */}
              <div className="absolute -right-5 top-1/2 -translate-y-1/2 text-[150px] font-black text-white/5 group-hover:text-white/15 transition-all duration-500 z-0 select-none opacity-70 group-hover:opacity-100 group-hover:-translate-x-2">
                {index + 1}
              </div>

              <Link to={`/anime/${anime?.id}`} className="block relative z-10">
                <div className="flex p-4">
                  {/* Anime poster */}
                  <div className="w-24 h-32 sm:w-28 sm:h-36 flex-shrink-0 relative overflow-hidden rounded-md shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                    <Image
                      src={anime?.images?.coverLarge || anime?.images?.coverMedium}
                      className="w-full h-full object-cover"
                      quality="high"
                    />
                    <AgeRating isAdult={anime?.isAdult} genres={anime?.genres} position="top-1 left-1" compact={true} />
                  </div>

                  {/* Anime details */}
                  <div className="flex-1 pl-4 flex flex-col">
                    {/* Rank number (small, visible one) */}
                    <div className="absolute top-3 right-4 z-10">
                      <div className="bg-black/40 backdrop-blur-sm px-2 py-1 rounded-lg text-2xl font-bold text-white shadow-md border border-white/10 group-hover:bg-black/60 group-hover:border-white/20 transition-all duration-300">
                        #{index + 1}
                      </div>
                    </div>

                    {/* Title */}
                    <h3 className="text-base sm:text-lg font-semibold line-clamp-2 mb-2 pr-10 text-white drop-shadow-md group-hover:text-primary-100 transition-colors duration-300">
                      {anime?.title}
                    </h3>

                    {/* Stats */}
                    <div className="flex flex-col gap-1.5 text-xs text-white/80 mt-auto">
                      <div className="flex items-center">
                        <span className="bg-white/10 backdrop-blur-sm px-2 py-0.5 rounded-sm mr-2 text-white/90 group-hover:bg-white/15 transition-colors duration-300">TV</span>
                      </div>

                      <div className="flex items-center gap-3 mt-1">
                        {anime?.episodes && (
                          <span className="flex items-center bg-black/30 px-2 py-0.5 rounded-sm">
                            <span className="mr-1">{anime.episodes}</span>
                            <span>Eps</span>
                          </span>
                        )}

                        {anime?.rating && (
                          <span className="flex items-center bg-black/30 px-2 py-0.5 rounded-sm">
                            <Star size={12} className="mr-1 text-yellow-400" fill="currentColor" />
                            <span>{anime.rating}</span>
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default MostPopular;
