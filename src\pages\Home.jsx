import { getAnimeHomeData } from "@/api/anilist";
import RecentComments from "@/components/home/<USER>";
import GenreCarousel from "@/components/home/<USER>";
import useFetch from "@/hooks/useFetch";
import { useMemo, lazy, Suspense } from "react";
import AnimeHero from "@/components/anime/AnimeHero";
const RecentEpisodes = lazy(() => import("@/components/anime/RecentEpisodes"));
import MustWatchAnime from "@/components/anime/MustWatchAnime";

import { Loader2 } from "lucide-react";
import { Link } from "react-router-dom";

// Lazy load heavy components
const TopRatedAnime = lazy(() => import("@/components/home/<USER>"));
const ComingSoonAnime = lazy(() => import("@/components/home/<USER>"));
const Strip = lazy(() => import("@/components/home/<USER>"));
const FeaturedCollection = lazy(() => import("@/components/home/<USER>"));
const GreetingsSection = lazy(() => import("@/components/home/<USER>"));
const ContinueWatching = lazy(() => import("@/components/home/<USER>"));

const Home = () => {
  const { data, isLoading } = useFetch({
    key: ["anime-strips"],
    fun: async () => {
      return (await getAnimeHomeData()) || null;
    },
  });

  const strips = useMemo(() => {
    return data || Array.from({ length: 6 });
  }, [data]);

  document.title = "AnimeHQ";

  return (
    <div className="flex flex-col w-full gap-10 pb-10">
      {/* Hero Section */}
      <AnimeHero />

      {/* Continue Watching Section */}
      <Suspense fallback={
        <div className="w-full h-[200px] bg-white/5 animate-pulse rounded-xl flex items-center justify-center">
          <Loader2 className="w-8 h-8 text-white/50 animate-spin" />
        </div>
      }>
        <ContinueWatching />
      </Suspense>

      {/* Recent Episodes Section */}
      <Suspense fallback={
        <div className="w-full h-[200px] bg-white/5 animate-pulse rounded-xl flex items-center justify-center">
          <Loader2 className="w-8 h-8 text-white/50 animate-spin" />
        </div>
      }>
        <RecentEpisodes />
      </Suspense>

      {/* Greetings Section */}
      <Suspense fallback={
        <div className="w-full h-[200px] bg-white/5 animate-pulse rounded-xl flex items-center justify-center">
          <Loader2 className="w-8 h-8 text-white/50 animate-spin" />
        </div>
      }>
        <GreetingsSection />
      </Suspense>

      {/* Must Watch Anime Section */}
      <MustWatchAnime />

      {/* Genre Carousel Section */}
      <GenreCarousel />

      {/* Recent Comments Section */}
      <RecentComments />

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-20">
          <Loader2 className="w-10 h-10 text-primary animate-spin" />
        </div>
      )}

      {/* Collections */}
      {!isLoading && (
        <div className="flex w-full flex-col gap-8 lg:gap-12">
          {/* First Row - Main Collections */}
          <div className="grid grid-cols-1 gap-10 lg:gap-16">
            {/* Only show Trending Now, skip Popular Anime */}
            {strips?.[0] && (
              <Suspense fallback={
                <div className="w-full h-[200px] bg-white/5 animate-pulse rounded-xl flex items-center justify-center">
                  <Loader2 className="w-8 h-8 text-white/50 animate-spin" />
                </div>
              }>
                <Strip title={strips[0]?.title} data={strips[0]?.data} />
              </Suspense>
            )}
          </div>

          {/* Top Rated Anime - New Design with Suspense */}
          {strips?.[2] && (
            <div className="w-full">
              <Suspense fallback={
                <div className="w-full h-[300px] bg-white/5 animate-pulse rounded-xl flex items-center justify-center">
                  <Loader2 className="w-8 h-8 text-white/50 animate-spin" />
                </div>
              }>
                <TopRatedAnime data={strips[2].data} />
              </Suspense>
            </div>
          )}

          {/* Coming Soon Section - New Design with Suspense */}
          {strips?.[3] && (
            <div className="w-full mb-2">
              <Suspense fallback={
                <div className="w-full h-[300px] bg-white/5 animate-pulse rounded-xl flex items-center justify-center">
                  <Loader2 className="w-8 h-8 text-white/50 animate-spin" />
                </div>
              }>
                <ComingSoonAnime data={strips[3].data} />
              </Suspense>
            </div>
          )}

          {/* Divider - reduced spacing */}
          <div className="w-full h-px bg-white/10 mt-2 mb-2"></div>

          {/* Genre Collections with Alternating Layouts */}
          <div className="grid grid-cols-1 gap-6 lg:gap-10">
            {/* Action Anime */}
            {strips?.[4] && (
              <Suspense fallback={
                <div className="w-full h-[200px] bg-white/5 animate-pulse rounded-xl flex items-center justify-center">
                  <Loader2 className="w-8 h-8 text-white/50 animate-spin" />
                </div>
              }>
                <Strip key="action" title={strips[4].title} data={strips[4].data} />
              </Suspense>
            )}

            {/* Romance Anime - Using Strip Component for Consistency */}
            {strips?.[5] && (
              <Suspense fallback={
                <div className="w-full h-[200px] bg-white/5 animate-pulse rounded-xl flex items-center justify-center">
                  <Loader2 className="w-8 h-8 text-white/50 animate-spin" />
                </div>
              }>
                <Strip key="romance" title={strips[5].title} data={strips[5].data} />
              </Suspense>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Home;
