import { useState, useEffect } from 'react';
import { AlertCircle, Info } from 'lucide-react';

/**
 * Component to display rate limit information to the user
 * Shows when rate limiting is active and provides helpful information
 */
const RateLimitInfo = ({ isVisible = false }) => {
  const [showInfo, setShowInfo] = useState(false);
  
  // Toggle info panel
  const toggleInfo = () => setShowInfo(!showInfo);
  
  // Hide component after rate limit is resolved
  useEffect(() => {
    if (!isVisible) {
      setShowInfo(false);
    }
  }, [isVisible]);
  
  if (!isVisible) return null;
  
  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-md">
      <div className="bg-yellow-900/80 backdrop-blur-sm border border-yellow-700 rounded-lg p-4 shadow-lg">
        <div className="flex items-start gap-3">
          <AlertCircle className="text-yellow-400 shrink-0 mt-0.5" />
          <div>
            <h3 className="font-medium text-yellow-300 flex items-center gap-2">
              AniList Rate Limit
              <button 
                onClick={toggleInfo}
                className="text-xs bg-yellow-800 hover:bg-yellow-700 px-2 py-0.5 rounded-full transition-colors"
              >
                {showInfo ? 'Hide Info' : 'More Info'}
              </button>
            </h3>
            <p className="text-sm text-yellow-200/80 mt-1">
              We're experiencing AniList API rate limits. The app will automatically retry your requests.
            </p>
            
            {showInfo && (
              <div className="mt-3 text-xs text-yellow-200/70 border-t border-yellow-700/50 pt-2">
                <div className="flex gap-2 items-start mb-2">
                  <Info size={14} className="shrink-0 mt-0.5" />
                  <p>
                    AniList limits API requests to protect their servers. We've implemented automatic retries with 
                    exponential backoff and caching to minimize the impact.
                  </p>
                </div>
                <div className="flex gap-2 items-start">
                  <Info size={14} className="shrink-0 mt-0.5" />
                  <p>
                    To avoid rate limits, try to limit rapid actions like quickly switching between tabs or 
                    making multiple updates in a short time.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RateLimitInfo;
