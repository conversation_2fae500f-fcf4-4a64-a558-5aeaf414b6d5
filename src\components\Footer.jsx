import { <PERSON> } from "react-router-dom";
import { Gith<PERSON>, Twitter, Mail } from "lucide-react";
import { useState, useEffect } from "react";
import { LazyLoadImage } from "react-lazy-load-image-component";
import { useMainContext } from "@/context/MainContext";
import axios from "axios";

const Footer = () => {
  const [banner, setBanner] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const { hideFooter } = useMainContext();

  // Fetch a random anime banner from Jikan API
  useEffect(() => {
    const fetchRandomBanner = async () => {
      try {
        setIsLoading(true);

        // Get a random page number between 1 and 10
        const randomPage = Math.floor(Math.random() * 10) + 1;

        // Randomly choose between top anime, seasonal anime, or popular anime
        const endpoints = [
          `https://api.jikan.moe/v4/top/anime?page=${randomPage}&limit=10`,
          `https://api.jikan.moe/v4/seasons/now?page=${randomPage}&limit=10`,
          `https://api.jikan.moe/v4/anime?page=${randomPage}&limit=10&order_by=popularity&sort=desc`
        ];

        const randomEndpoint = endpoints[Math.floor(Math.random() * endpoints.length)];

        // Fetch anime data from Jikan API
        const response = await axios.get(randomEndpoint);

        // Filter anime with images and get a random one
        const animeWithImages = response.data.data.filter(anime =>
          anime.images?.jpg?.large_image_url ||
          anime.images?.webp?.large_image_url
        );

        if (animeWithImages.length > 0) {
          const randomAnime = animeWithImages[Math.floor(Math.random() * animeWithImages.length)];

          // Use the anime's image URL
          const imageUrl = randomAnime.images?.jpg?.large_image_url ||
                          randomAnime.images?.webp?.large_image_url;

          setBanner(imageUrl);
        }
      } catch (error) {
        console.error("Error fetching random anime banner:", error);
        // Fallback to a default image if the API fails
        setBanner("https://s4.anilist.co/file/anilistcdn/media/anime/banner/21-wf37VakJmZqs.jpg");
      } finally {
        setIsLoading(false);
      }
    };

    fetchRandomBanner();
  }, []);

  if (hideFooter) return null;

  return (
    <footer className="w-full mt-auto !select-none relative mx-auto max-w-7xl mb-4">
      {/* Desktop layout - glassy white with anime banner background */}
      <div className="hidden md:block relative h-48 rounded-xl overflow-hidden">
        {/* Background banner */}
        <div className="absolute inset-0">
          {isLoading ? (
            <div className="w-full h-full bg-gradient-to-r from-gray-900 to-gray-800 animate-pulse"></div>
          ) : (
            <LazyLoadImage
              src={banner}
              effect="blur"
              width="100%"
              height="100%"
              className="size-full object-cover object-center !select-none scale-105"
            />
          )}
          <div className="absolute inset-0 bg-white/15 backdrop-blur-sm"></div>
        </div>

        {/* Glass effect elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
          <div className="absolute -top-4 -left-4 w-24 h-24 bg-white/20 rounded-full blur-xl"></div>
          <div className="absolute -bottom-8 -right-8 w-40 h-40 bg-white/15 rounded-full blur-xl"></div>
        </div>

        {/* Border effect */}
        <div className="absolute inset-0 border border-white/30 rounded-xl pointer-events-none"></div>

        {/* Content overlay */}
        <div className="absolute inset-0 flex flex-col items-center justify-center text-center z-10 px-4">
          <h2 className="text-4xl font-bold mb-2 text-white drop-shadow-lg">Enjoy Watching</h2>
          <p className="text-white text-base mb-6 drop-shadow-md">Explore. Watch. Discuss</p>

          <div className="flex flex-col gap-4 items-center">
            {/* Social links */}
            <div className="flex gap-8 bg-white/10 backdrop-blur-sm px-6 py-3 rounded-full">
              <a href="https://github.com" target="_blank" rel="noopener noreferrer" className="text-white hover:text-white/80 transition-colors" aria-label="GitHub">
                <Github size={22} />
              </a>
              <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="text-white hover:text-white/80 transition-colors" aria-label="Twitter">
                <Twitter size={22} />
              </a>
              <a href="mailto:<EMAIL>" className="text-white hover:text-white/80 transition-colors" aria-label="Mail">
                <Mail size={22} />
              </a>
            </div>

            {/* Page links */}
            <div className="flex flex-wrap justify-center gap-x-6 gap-y-2 text-sm text-white/70">
              <Link to="/about" className="hover:text-white transition-colors">About</Link>
              <Link to="/donate" className="hover:text-white transition-colors">Support Us</Link>
              <Link to="/live" className="hover:text-white transition-colors">Live Stream</Link>
              <Link to="/faq" className="hover:text-white transition-colors">FAQ</Link>
              <Link to="/terms" className="hover:text-white transition-colors">Terms</Link>
              <Link to="/privacy" className="hover:text-white transition-colors">Privacy</Link>
              <Link to="/dmca" className="hover:text-white transition-colors">DMCA</Link>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile layout - glassy white with anime banner background */}
      <div className="md:hidden rounded-xl overflow-hidden">
        <div className="relative">
          {/* Background image */}
          <div className="h-40 relative overflow-hidden">
            {isLoading ? (
              <div className="w-full h-full bg-gradient-to-r from-gray-900 to-gray-800 animate-pulse"></div>
            ) : (
              <LazyLoadImage
                src={banner}
                effect="blur"
                width="100%"
                height="100%"
                className="size-full object-cover object-center !select-none scale-105"
              />
            )}
            <div className="absolute inset-0 bg-white/15 backdrop-blur-sm"></div>

            {/* Glass effect elements */}
            <div className="absolute -top-4 -left-4 w-16 h-16 bg-white/20 rounded-full blur-lg"></div>
            <div className="absolute -bottom-6 -right-6 w-24 h-24 bg-white/15 rounded-full blur-lg"></div>

            {/* Border effect */}
            <div className="absolute inset-0 border border-white/30 rounded-xl pointer-events-none"></div>
          </div>

          {/* Content overlay */}
          <div className="absolute inset-0 flex flex-col items-center justify-center text-center">
            <h2 className="text-2xl font-bold mb-1 text-white drop-shadow-lg">Enjoy Watching</h2>
            <p className="text-white text-xs mb-4 drop-shadow-md">Explore. Watch. Discuss</p>

            <div className="flex flex-col gap-3 items-center">
              {/* Social links */}
              <div className="flex gap-6 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full">
                <a href="https://github.com" target="_blank" rel="noopener noreferrer" className="text-white hover:text-white/80 transition-colors" aria-label="GitHub">
                  <Github size={18} />
                </a>
                <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="text-white hover:text-white/80 transition-colors" aria-label="Twitter">
                  <Twitter size={18} />
                </a>
                <a href="mailto:<EMAIL>" className="text-white hover:text-white/80 transition-colors" aria-label="Mail">
                  <Mail size={18} />
                </a>
              </div>

              {/* Page links - smaller for mobile */}
              <div className="flex flex-wrap justify-center gap-x-4 gap-y-1 text-xs text-white/70 px-2">
                <Link to="/about" className="hover:text-white transition-colors">About</Link>
                <Link to="/donate" className="hover:text-white transition-colors">Support</Link>
                <Link to="/live" className="hover:text-white transition-colors">Live</Link>
                <Link to="/faq" className="hover:text-white transition-colors">FAQ</Link>
                <Link to="/terms" className="hover:text-white transition-colors">Terms</Link>
                <Link to="/privacy" className="hover:text-white transition-colors">Privacy</Link>
                <Link to="/dmca" className="hover:text-white transition-colors">DMCA</Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
