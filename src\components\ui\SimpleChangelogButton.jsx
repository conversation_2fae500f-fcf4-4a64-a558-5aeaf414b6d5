import { FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

const SimpleChangelogButton = ({ className, iconOnly = false }) => {
  return (
    <Button
      variant="ghost"
      size="sm"
      className={className}
      asChild
    >
      <Link to="/changelog">
        <FileText size={18} className="text-purple-400" />
        {!iconOnly && <span className="font-medium">What's New</span>}
      </Link>
    </Button>
  );
};

export default SimpleChangelogButton;
