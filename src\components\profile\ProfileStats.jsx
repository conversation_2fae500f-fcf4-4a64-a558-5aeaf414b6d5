import { useAniList } from "@/hooks/useAniList";
import { Clock, Film, PlayCircle } from "lucide-react";
import { useEffect, useState } from "react";

const ProfileStats = () => {
  const { user } = useAniList();
  const [stats, setStats] = useState({
    totalEpisodes: 0,
    totalAnime: 0,
    totalDays: 0
  });

  useEffect(() => {
    if (user?.statistics?.anime) {
      // Calculate stats from AniList data
      const totalEpisodes = user.statistics.anime.episodesWatched || 0;
      const totalAnime = user.statistics.anime.count || 0;
      const totalMinutes = user.statistics.anime.minutesWatched || 0;

      // Convert minutes to days for display
      const totalDays = Math.floor(totalMinutes / 1440); // 1440 minutes in a day

      setStats({
        totalEpisodes,
        totalAnime,
        totalDays
      });

      console.log("Updated profile stats:", { totalEpisodes, totalAnime, totalDays });
    }
  }, [user]);

  return (
    <div className="grid grid-cols-3 gap-4 w-full">
      {/* Total Episodes */}
      <div className="bg-black/40 backdrop-blur-sm rounded-lg p-4 border border-white/10">
        <div className="flex flex-col">
          <div className="flex items-center justify-between mb-1">
            <h3 className="text-xs text-white/60">Total Episodes</h3>
            <PlayCircle size={16} className="text-yellow-500" />
          </div>
          <p className="text-2xl font-bold">{stats.totalEpisodes}</p>
          <span className="text-xs text-white/60">Episodes you have watched</span>
        </div>
      </div>

      {/* Total Anime */}
      <div className="bg-black/40 backdrop-blur-sm rounded-lg p-4 border border-white/10">
        <div className="flex flex-col">
          <div className="flex items-center justify-between mb-1">
            <h3 className="text-xs text-white/60">Total Animes</h3>
            <Film size={16} className="text-blue-500" />
          </div>
          <p className="text-2xl font-bold">{stats.totalAnime}</p>
          <span className="text-xs text-white/60">Animes you have in your list</span>
        </div>
      </div>

      {/* Total Watchtime */}
      <div className="bg-black/40 backdrop-blur-sm rounded-lg p-4 border border-white/10">
        <div className="flex flex-col">
          <div className="flex items-center justify-between mb-1">
            <h3 className="text-xs text-white/60">Total Watchtime</h3>
            <Clock size={16} className="text-green-500" />
          </div>
          <p className="text-2xl font-bold">{stats.totalDays} Days</p>
          <span className="text-xs text-white/60">Your total watchtime</span>
        </div>
      </div>
    </div>
  );
};

export default ProfileStats;
