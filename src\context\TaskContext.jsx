import { createContext, useContext, useState, useEffect } from "react";
import { useAniList } from "@/hooks/useAniList";
import { useUserActivity } from "@/context/UserActivityContext";
import userProgressApi from "@/api/userProgressApi";
import profileApi from "@/api/profileApi";
import { toast } from "sonner";
import { getVerificationFunction, isTaskLocked } from "@/utils/taskVerification";
import { checkTaskVerificationStatus, setTaskVerificationFlag } from "@/utils/debugUtils";

const TaskContext = createContext();

export const useTaskContext = () => useContext(TaskContext);

export const TaskProvider = ({ children }) => {
  const { user, isAuthenticated } = useAniList();
  const { addXp, fetchUserActivity } = useUserActivity();
  const [dailyTasks, setDailyTasks] = useState([]);
  const [completedTasks, setCompletedTasks] = useState([]);
  const [lastTaskReset, setLastTaskReset] = useState(null);

  // Initialize tasks and load completed tasks from API or localStorage
  useEffect(() => {
    if (isAuthenticated && user?.id) {
      const userId = user.id;

      const fetchTasks = async () => {
        console.log("Fetching tasks for user:", userId);

        // First, check localStorage for any cached data to show immediately
        const cachedCompletedTasks = localStorage.getItem(`completed-tasks-${userId}`);
        if (cachedCompletedTasks) {
          try {
            const parsedTasks = JSON.parse(cachedCompletedTasks);
            console.log("Using cached completed tasks from localStorage:", parsedTasks);
            setCompletedTasks(parsedTasks);

            // Initialize tasks with cached data
            initializeDailyTasks(parsedTasks);
          } catch (error) {
            console.error("Error parsing cached completed tasks:", error);
          }
        }

        let retryCount = 0;
        const maxRetries = 3;

        const loadTaskData = async () => {
          try {
            console.log(`Attempt ${retryCount + 1} to fetch tasks from profile API`);

            // Try to get tasks from profile API
            const tasksData = await profileApi.getDailyTasks(userId);

            if (!tasksData) {
              throw new Error('Empty response from profile API');
            }

            console.log("Tasks data from profile API:", tasksData);

            if (tasksData.completedTasks && Array.isArray(tasksData.completedTasks)) {
              console.log("Setting completed tasks:", tasksData.completedTasks);
              setCompletedTasks(tasksData.completedTasks);

              // Also save to localStorage as a backup
              localStorage.setItem(`completed-tasks-${userId}`, JSON.stringify(tasksData.completedTasks));
            } else {
              console.warn("No completed tasks found in API response");
            }

            if (tasksData.lastReset) {
              console.log("Setting last reset:", tasksData.lastReset);
              setLastTaskReset(tasksData.lastReset);

              // Also save to localStorage as a backup
              localStorage.setItem(`tasks-last-reset-${userId}`, new Date(tasksData.lastReset).getTime().toString());
            }

            console.log("Tasks loaded from profile API");
            return { success: true, completedTasks: tasksData.completedTasks || [] };
          } catch (profileErr) {
            console.error(`Attempt ${retryCount + 1} failed:`, profileErr);

            if (retryCount < maxRetries - 1) {
              retryCount++;
              // Wait a bit before retrying
              await new Promise(resolve => setTimeout(resolve, 1000));
              return await loadTaskData();
            }

            console.error("All attempts to fetch tasks from profile API failed, trying legacy API");

            try {
              // Try legacy API as fallback
              const tasksData = await userProgressApi.getDailyTasks(userId);

              if (!tasksData) {
                throw new Error('Empty response from legacy API');
              }

              console.log("Tasks data from legacy API:", tasksData);

              if (tasksData.completedTasks && Array.isArray(tasksData.completedTasks)) {
                console.log("Setting completed tasks from legacy API:", tasksData.completedTasks);
                setCompletedTasks(tasksData.completedTasks);

                // Also save to localStorage as a backup
                localStorage.setItem(`completed-tasks-${userId}`, JSON.stringify(tasksData.completedTasks));
              }

              if (tasksData.lastReset) {
                console.log("Setting last reset from legacy API:", tasksData.lastReset);
                setLastTaskReset(tasksData.lastReset);

                // Also save to localStorage as a backup
                localStorage.setItem(`tasks-last-reset-${userId}`, new Date(tasksData.lastReset).getTime().toString());
              }

              console.log("Tasks loaded from legacy API");
              return { success: true, completedTasks: tasksData.completedTasks || [] };
            } catch (legacyErr) {
              console.error("Error fetching tasks from legacy API:", legacyErr);

              // Fallback to localStorage
              const storedCompletedTasks = localStorage.getItem(`completed-tasks-${userId}`);
              const storedLastReset = localStorage.getItem(`tasks-last-reset-${userId}`);

              let parsedTasks = [];
              if (storedCompletedTasks) {
                try {
                  parsedTasks = JSON.parse(storedCompletedTasks);
                  console.log("Setting completed tasks from localStorage:", parsedTasks);
                  setCompletedTasks(parsedTasks);
                } catch (parseErr) {
                  console.error("Error parsing stored completed tasks:", parseErr);
                  setCompletedTasks([]);
                }
              }

              if (storedLastReset) {
                try {
                  console.log("Setting last reset from localStorage:", storedLastReset);
                  setLastTaskReset(parseInt(storedLastReset));
                } catch (parseErr) {
                  console.error("Error parsing stored last reset:", parseErr);
                  setLastTaskReset(null);
                }
              }

              console.log("Tasks loaded from localStorage (fallback)");
              return { success: false, completedTasks: parsedTasks };
            }
          }
        };

        const result = await loadTaskData();

        // Check if tasks need to be reset (new day)
        await checkTaskReset(userId);

        // Initialize daily tasks with the completed tasks we just loaded
        initializeDailyTasks(result.completedTasks);

        // Double-check that the UI is updated with the correct completion status
        // This is important because sometimes the state updates don't propagate correctly
        setTimeout(() => {
          console.log("Double-checking task completion status");
          updateTaskCompletionStatus(result.completedTasks);
        }, 500);
      };

      fetchTasks();
    }
  }, [isAuthenticated, user]);

  // Function to update task completion status
  const updateTaskCompletionStatus = (completedTaskIds) => {
    if (!Array.isArray(completedTaskIds)) {
      console.warn("completedTaskIds is not an array:", completedTaskIds);
      return;
    }

    console.log("Updating task completion status with:", completedTaskIds);

    // For each task, check if it's locked (already completed today)
    const updatedTasks = dailyTasks.map(task => {
      // Check if the task is in the completedTaskIds array
      const isCompleted = completedTaskIds.includes(task.id);

      // If the task is completed, also check if it's locked
      let isLocked = false;
      if (isCompleted && user?.id) {
        isLocked = isTaskLocked(user.id, task.id);
      }

      return {
        ...task,
        isCompleted: isCompleted || isLocked // Task is completed if it's in the array or locked
      };
    });

    // Check if any task status has changed
    const hasChanges = updatedTasks.some((task, index) =>
      task.isCompleted !== dailyTasks[index].isCompleted
    );

    if (hasChanges) {
      console.log("Task completion status has changed, updating UI");
      setDailyTasks(updatedTasks);
    } else {
      console.log("No changes in task completion status");
    }
  };

  // Check if tasks need to be reset (new day)
  const checkTaskReset = async (userId) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayTimestamp = today.getTime();

    console.log("Checking if tasks need to be reset. Last reset:", lastTaskReset, "Today:", todayTimestamp);

    // Convert lastTaskReset to a timestamp if it's a Date object
    let lastResetTimestamp = lastTaskReset;
    if (lastTaskReset instanceof Date) {
      lastResetTimestamp = lastTaskReset.getTime();
    } else if (typeof lastTaskReset === 'string') {
      lastResetTimestamp = new Date(lastTaskReset).getTime();
    }

    // If lastResetTimestamp is not a valid number, set it to null
    if (isNaN(lastResetTimestamp)) {
      lastResetTimestamp = null;
    }

    if (!lastResetTimestamp || lastResetTimestamp < todayTimestamp) {
      console.log("Resetting tasks for new day");

      // Reset tasks for new day
      setCompletedTasks([]);
      setLastTaskReset(todayTimestamp);

      // Save to localStorage immediately as a backup
      localStorage.setItem(`completed-tasks-${userId}`, JSON.stringify([]));
      localStorage.setItem(`tasks-last-reset-${userId}`, todayTimestamp.toString());

      // Also clear all task completion records
      const allTaskIds = [
        "visit", "watch-episode", "add-anime", "rate-anime",
        "update-progress", "explore-genres", "watch-seasonal", "complete-all"
      ];

      allTaskIds.forEach(taskId => {
        const completionKey = `task-completion-${userId}-${taskId}`;
        localStorage.removeItem(completionKey);
        console.log(`Cleared completion record for task ${taskId}`);
      });

      // Update server with retry logic
      let retryCount = 0;
      const maxRetries = 3;

      const updateServer = async () => {
        try {
          console.log(`Attempt ${retryCount + 1} to reset tasks on profile server`);

          // Update tasks on profile server
          const tasksData = {
            tasks: {
              completedTasks: [],
              lastReset: new Date(todayTimestamp)
            }
          };

          // Update with profile API
          await profileApi.updateUserProfile(userId, tasksData);
          console.log("Daily tasks reset on profile server");
          return true;
        } catch (profileErr) {
          console.error(`Attempt ${retryCount + 1} failed:`, profileErr);

          if (retryCount < maxRetries - 1) {
            retryCount++;
            // Wait a bit before retrying
            await new Promise(resolve => setTimeout(resolve, 1000));
            return await updateServer();
          }

          console.error("All attempts to reset tasks on profile server failed, trying legacy API");

          try {
            // Try legacy API as fallback
            const tasksData = {
              completedTasks: [],
              lastReset: new Date(todayTimestamp)
            };

            // We're using updateUserActivity as a generic update method
            await userProgressApi.updateUserActivity(userId, { tasks: tasksData });
            console.log("Daily tasks reset on legacy server");
            return true;
          } catch (legacyErr) {
            console.error("Error resetting tasks on legacy server:", legacyErr);

            // Fallback to localStorage if both APIs fail
            // (We already saved to localStorage above)
            console.log("Daily tasks reset in localStorage (fallback)");
            return false;
          }
        }
      };

      // Don't await this to avoid blocking the UI
      updateServer().catch(err => {
        console.error("Unexpected error in updateServer:", err);
      });
    } else {
      console.log("Tasks don't need to be reset yet");
    }
  };

  // Initialize daily tasks
  const initializeDailyTasks = (tasksToMarkCompleted) => {
    // Use either the provided tasks or the current completedTasks state
    const tasksToMark = tasksToMarkCompleted || completedTasks;

    console.log("Initializing daily tasks with completed tasks:", tasksToMark);

    // Ensure we have a valid array
    const safeCompletedTasks = Array.isArray(tasksToMark) ? tasksToMark : [];

    // Check for task completion records from today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const dateKey = today.toISOString().split('T')[0];

    // Get all task IDs
    const allTaskIds = [
      "visit", "watch-episode", "add-anime", "rate-anime",
      "update-progress", "explore-genres", "watch-seasonal", "complete-all"
    ];

    // Check for completion records for each task
    const completedFromRecords = [];

    if (isAuthenticated && user?.id) {
      allTaskIds.forEach(taskId => {
        const completionKey = `task-completion-${user.id}-${taskId}`;
        const completionRecord = localStorage.getItem(completionKey);

        if (completionRecord) {
          try {
            const record = JSON.parse(completionRecord);
            // If the record is from today, mark the task as completed
            if (record.dateKey === dateKey) {
              console.log(`Found completion record for task ${taskId} from today`);
              if (!safeCompletedTasks.includes(taskId)) {
                completedFromRecords.push(taskId);
              }
            }
          } catch (error) {
            console.error(`Error parsing completion record for task ${taskId}:`, error);
          }
        }
      });
    }

    // Combine the completion records with the provided completed tasks
    const combinedCompletedTasks = [...safeCompletedTasks, ...completedFromRecords];
    console.log("Combined completed tasks:", combinedCompletedTasks);

    const tasks = [
      {
        id: "visit",
        title: "Daily Visit",
        description: "Visit the site today",
        xpReward: 10,
        icon: "calendar",
        difficulty: "easy",
        isCompleted: combinedCompletedTasks.includes("visit")
      },
      {
        id: "watch-episode",
        title: "Watch an Episode",
        description: "Watch at least one anime episode",
        xpReward: 15,
        icon: "play",
        difficulty: "easy",
        isCompleted: combinedCompletedTasks.includes("watch-episode")
      },
      {
        id: "add-anime",
        title: "Add to List",
        description: "Add an anime to your list",
        xpReward: 20,
        icon: "plus",
        difficulty: "easy",
        isCompleted: combinedCompletedTasks.includes("add-anime")
      },
      {
        id: "rate-anime",
        title: "Rate an Anime",
        description: "Rate an anime in your list",
        xpReward: 25,
        icon: "star",
        difficulty: "medium",
        isCompleted: combinedCompletedTasks.includes("rate-anime")
      },
      {
        id: "update-progress",
        title: "Update Progress",
        description: "Update your progress on an anime",
        xpReward: 15,
        icon: "refresh",
        difficulty: "easy",
        isCompleted: combinedCompletedTasks.includes("update-progress")
      },
      {
        id: "explore-genres",
        title: "Explore Genres",
        description: "Browse anime from a specific genre",
        xpReward: 20,
        icon: "layers",
        difficulty: "medium",
        isCompleted: combinedCompletedTasks.includes("explore-genres")
      },
      {
        id: "watch-seasonal",
        title: "Watch Seasonal",
        description: "Watch an episode from a currently airing anime",
        xpReward: 30,
        icon: "zap",
        difficulty: "medium",
        isCompleted: combinedCompletedTasks.includes("watch-seasonal")
      },
      {
        id: "complete-all",
        title: "Complete All Tasks",
        description: "Complete all other daily tasks",
        xpReward: 50,
        icon: "check-circle",
        difficulty: "hard",
        isCompleted: combinedCompletedTasks.includes("complete-all"),
        isBonus: true
      }
    ];

    // Log the completion status for debugging
    const completionStatus = tasks.map(t => ({ id: t.id, completed: t.isCompleted }));
    console.log("Setting daily tasks with completion status:", completionStatus);

    // Check if any tasks are marked as completed
    const hasCompletedTasks = tasks.some(t => t.isCompleted);
    if (hasCompletedTasks) {
      console.log("Some tasks are marked as completed");
    } else {
      console.log("No tasks are marked as completed");
    }

    setDailyTasks(tasks);
  };

  // Complete a task
  const completeTask = async (taskId) => {
    if (!isAuthenticated || !user?.id) return;

    // Find the task
    const task = dailyTasks.find(t => t.id === taskId);
    if (!task) {
      console.error(`Task ${taskId} not found in dailyTasks`);
      return;
    }

    // Check if there's a completion record for today
    const todayDate = new Date();
    todayDate.setHours(0, 0, 0, 0);
    const dateKey = todayDate.toISOString().split('T')[0];
    const completionKey = `task-completion-${user.id}-${taskId}`;
    const completionRecord = localStorage.getItem(completionKey);

    if (completionRecord) {
      try {
        const record = JSON.parse(completionRecord);
        // If the record is from today, the task is already completed
        if (record.dateKey === dateKey) {
          console.log(`Task ${taskId} already completed today according to completion record`);

          // Make sure completedTasks includes this task
          if (!completedTasks.includes(taskId)) {
            const newCompletedTasks = [...completedTasks, taskId];
            setCompletedTasks(newCompletedTasks);

            // Save to localStorage immediately
            localStorage.setItem(`completed-tasks-${user.id}`, JSON.stringify(newCompletedTasks));
          }

          // Make sure UI shows task as completed
          const updatedTasks = dailyTasks.map(t =>
            t.id === taskId ? { ...t, isCompleted: true } : t
          );
          setDailyTasks(updatedTasks);

          return;
        }
      } catch (error) {
        console.error(`Error parsing completion record for task ${taskId}:`, error);
      }
    }

    // If task is already completed in the UI or completedTasks array, just ensure UI is updated
    if (task.isCompleted || completedTasks.includes(taskId)) {
      console.log(`Task ${taskId} is already completed, ensuring UI is updated`);

      // Make sure completedTasks includes this task
      if (!completedTasks.includes(taskId)) {
        const newCompletedTasks = [...completedTasks, taskId];
        setCompletedTasks(newCompletedTasks);

        // Save to localStorage immediately
        localStorage.setItem(`completed-tasks-${user.id}`, JSON.stringify(newCompletedTasks));
      }

      // Make sure UI shows task as completed
      const updatedTasks = dailyTasks.map(t =>
        t.id === taskId ? { ...t, isCompleted: true } : t
      );
      setDailyTasks(updatedTasks);

      // Create and save a completion record for today
      const newCompletionRecord = {
        taskId,
        completedAt: Date.now(),
        dateKey
      };
      localStorage.setItem(completionKey, JSON.stringify(newCompletionRecord));

      return;
    }

    console.log(`Attempting to complete task ${taskId} for user ${user.id}`);

    // Get the verification function for this task
    const verifyTask = getVerificationFunction(taskId);

    // Check if the task is already locked (completed)
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayTimestamp = today.getTime();
    const taskCompletedKey = `task-completed-${user.id}-${taskId}-${todayTimestamp}`;
    const taskIsLocked = isTaskLocked(user.id, taskId);

    if (taskIsLocked) {
      console.log(`Task ${taskId} is locked (already completed today)`);
      // Mark as completed in UI and return
      const updatedTasks = dailyTasks.map(t =>
        t.id === taskId ? { ...t, isCompleted: true } : t
      );
      setDailyTasks(updatedTasks);

      // Make sure completedTasks includes this task
      if (!completedTasks.includes(taskId)) {
        const newCompletedTasks = [...completedTasks, taskId];
        setCompletedTasks(newCompletedTasks);
        localStorage.setItem(`completed-tasks-${user.id}`, JSON.stringify(newCompletedTasks));
      }

      return;
    }

    // Special case for visit task which is auto-completed
    let verificationResult;
    if (taskId === "visit") {
      console.log("Visit task is auto-verified");
      verificationResult = { success: true };
    }
    // In development mode, we can bypass verification for easier testing
    else if (process.env.NODE_ENV === 'development' && localStorage.getItem('bypass-task-verification') === 'true') {
      console.log(`DEV MODE: Bypassing verification for task ${taskId}`);
      verificationResult = { success: true };
    } else {
      // Verify that the user has actually completed the task
      // Pass the completedTasks array to the verification function
      console.log(`Verifying task ${taskId} with completedTasks:`, completedTasks);
      verificationResult = await verifyTask(user.id, completedTasks);
    }

    // If verification fails, show error message and return
    if (!verificationResult.success) {
      // In development mode, we can show an option to bypass verification
      if (process.env.NODE_ENV === 'development') {
        toast.error("Task Verification Failed", {
          description: (
            <>
              {verificationResult.message || "You need to complete the required action first"}
              <button
                onClick={() => {
                  localStorage.setItem('bypass-task-verification', 'true');
                  toast.success("Verification bypass enabled for development");
                  // Try completing the task again
                  setTimeout(() => completeTask(taskId), 500);
                }}
                className="mt-2 bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded text-xs"
              >
                Enable Dev Bypass
              </button>
            </>
          ),
          duration: 5000
        });
      } else {
        toast.error("Task Verification Failed", {
          description: verificationResult.message || "You need to complete the required action first",
          duration: 3000
        });
      }
      return;
    }

    // Mark task as completed
    const updatedTasks = dailyTasks.map(t =>
      t.id === taskId ? { ...t, isCompleted: true } : t
    );

    // Get today's date in YYYY-MM-DD format for the completion record
    // (reusing the variables from above)

    // Create a completion record with the date
    const newCompletionRecord = {
      taskId,
      completedAt: Date.now(),
      dateKey: todayDate.toISOString().split('T')[0]
    };

    // Update completed tasks array
    const newCompletedTasks = [...completedTasks, taskId];
    setCompletedTasks(newCompletedTasks);
    setDailyTasks(updatedTasks);

    // Save to localStorage immediately as a backup
    localStorage.setItem(`completed-tasks-${user.id}`, JSON.stringify(newCompletedTasks));

    // Also save the completion record with date information
    localStorage.setItem(completionKey, JSON.stringify(newCompletionRecord));

    // Set a flag to prevent completing the same task multiple times
    localStorage.setItem(taskCompletedKey, 'true');

    // Update server with retry logic
    let retryCount = 0;
    const maxRetries = 3;

    const updateServer = async () => {
      try {
        console.log(`Attempt ${retryCount + 1} to complete task ${taskId} on profile server`);

        // Double-check with the server if the task is already completed
        try {
          const tasksData = await profileApi.getDailyTasks(user.id);
          if (tasksData && tasksData.completedTasks && tasksData.completedTasks.includes(taskId)) {
            console.log(`Task ${taskId} already completed on server, skipping completion`);
            return true;
          }
        } catch (error) {
          console.error("Error checking if task is already completed:", error);
          // Continue with completion if we can't check the server
        }

        // Complete task on profile server
        const response = await profileApi.completeTask(user.id, taskId);
        console.log(`Task ${taskId} completed on profile server:`, response);

        // Award XP through profile API
        await profileApi.addXp(user.id, task.xpReward, 'task_completion');

        // Refresh user profile data after a short delay
        setTimeout(() => {
          console.log("Refreshing user profile data after completing task");
          fetchUserActivity(user.id);
        }, 1000);

        return true;
      } catch (profileErr) {
        console.error(`Attempt ${retryCount + 1} failed:`, profileErr);

        if (retryCount < maxRetries - 1) {
          retryCount++;
          // Wait a bit before retrying
          await new Promise(resolve => setTimeout(resolve, 1000));
          return await updateServer();
        }

        console.error("All attempts to complete task on profile server failed, trying legacy API");

        try {
          // Try legacy API as fallback
          await userProgressApi.completeTask(user.id, taskId);
          console.log(`Task ${taskId} completed on legacy server`);

          // Award XP through legacy API
          await userProgressApi.addXp(user.id, task.xpReward, 'task_completion');

          // Also update the user profile with the completed tasks
          const tasksData = {
            tasks: {
              completedTasks: newCompletedTasks,
              lastReset: lastTaskReset || new Date().setHours(0, 0, 0, 0)
            }
          };

          await userProgressApi.updateUserActivity(user.id, tasksData);

          // Refresh user profile data after a short delay
          setTimeout(() => {
            console.log("Refreshing user profile data after completing task (legacy)");
            fetchUserActivity(user.id);
          }, 1000);

          return true;
        } catch (legacyErr) {
          console.error("Error completing task on legacy server:", legacyErr);

          // Fallback to localStorage if both APIs fail
          // (We already saved to localStorage above)

          // Award XP locally
          addXp(task.xpReward);

          console.log(`Task ${taskId} completed in localStorage (fallback)`);
          return false;
        }
      }
    };

    // Don't await this to avoid blocking the UI
    updateServer().catch(err => {
      console.error("Unexpected error in updateServer:", err);
    });

    // Show toast notification
    toast.success(`Task Completed: ${task.title}`, {
      description: `You earned ${task.xpReward} XP!`,
      duration: 3000
    });

    // Check if all tasks are completed (except the bonus "complete-all" task)
    const regularTasks = dailyTasks.filter(t => !t.isBonus);
    const allRegularTasksCompleted = regularTasks.every(t =>
      t.id === taskId ? true : newCompletedTasks.includes(t.id)
    );

    if (allRegularTasksCompleted && !newCompletedTasks.includes("complete-all")) {
      // Complete the bonus task automatically
      setTimeout(() => {
        completeTask("complete-all");
      }, 1000);
    }
  };

  // Ensure task completion status is maintained when navigating between pages
  useEffect(() => {
    if (isAuthenticated && user?.id && dailyTasks.length > 0) {
      console.log("Checking task completion status consistency");

      // Get today's date in YYYY-MM-DD format
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const dateKey = today.toISOString().split('T')[0];

      // Check for completion records for each task
      const tasksCompletedToday = [];

      // First check completedTasks array
      if (completedTasks.length > 0) {
        // Check if any tasks in completedTasks are not marked as completed in dailyTasks
        completedTasks.forEach(taskId => {
          if (!tasksCompletedToday.includes(taskId)) {
            tasksCompletedToday.push(taskId);
          }
        });
      }

      // Then check localStorage for completion records
      dailyTasks.forEach(task => {
        const completionKey = `task-completion-${user.id}-${task.id}`;
        const completionRecord = localStorage.getItem(completionKey);

        if (completionRecord) {
          try {
            const record = JSON.parse(completionRecord);
            // If the record is from today and the task is not already in our list
            if (record.dateKey === dateKey && !tasksCompletedToday.includes(task.id)) {
              console.log(`Found completion record for task ${task.id} from today`);
              tasksCompletedToday.push(task.id);
            }
          } catch (error) {
            console.error(`Error parsing completion record for task ${task.id}:`, error);
          }
        }
      });

      // If we found any completed tasks, update the UI
      if (tasksCompletedToday.length > 0) {
        console.log("Found tasks completed today, updating UI:", tasksCompletedToday);
        updateTaskCompletionStatus(tasksCompletedToday);

        // Also update the completedTasks state if needed
        if (JSON.stringify(completedTasks.sort()) !== JSON.stringify(tasksCompletedToday.sort())) {
          console.log("Updating completedTasks state:", tasksCompletedToday);
          setCompletedTasks(tasksCompletedToday);

          // Save to localStorage
          localStorage.setItem(`completed-tasks-${user.id}`, JSON.stringify(tasksCompletedToday));
        }
      }
    }
  }, [isAuthenticated, user, dailyTasks, completedTasks]);

  // Auto-complete the daily visit task
  useEffect(() => {
    if (isAuthenticated && user?.id && dailyTasks.length > 0) {
      // Check if we've already completed the visit task today
      const visitTask = dailyTasks.find(t => t.id === "visit");
      const visitAlreadyCompleted = completedTasks.includes("visit");

      // Check if the visit task is already locked (completed today)
      const visitTaskLocked = isTaskLocked(user.id, "visit");

      // Check if we've already auto-completed the visit task today
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const visitAutoCompletedKey = `visit-auto-completed-${user.id}`;
      const visitAutoCompletedTimestamp = localStorage.getItem(visitAutoCompletedKey);
      const visitAutoCompletedToday = visitAutoCompletedTimestamp &&
        parseInt(visitAutoCompletedTimestamp) === today.getTime();

      // Only auto-complete if:
      // 1. The task exists
      // 2. It's not already completed in the UI
      // 3. It's not already in the completedTasks array
      // 4. It's not already locked
      // 5. We haven't already auto-completed it today
      if (visitTask && !visitTask.isCompleted && !visitAlreadyCompleted &&
          !visitTaskLocked && !visitAutoCompletedToday) {
        console.log("Auto-completing daily visit task - not already completed");

        // The visit task is special - it's automatically verified by the user's presence
        // We'll bypass the normal verification for this task only
        const autoCompleteVisitTask = async () => {
          try {
            // Check if we've already completed this task today (double-check from server)
            let alreadyCompletedOnServer = false;

            try {
              const tasksData = await profileApi.getDailyTasks(user.id);
              if (tasksData && tasksData.completedTasks && tasksData.completedTasks.includes("visit")) {
                console.log("Visit task already completed on server, skipping auto-completion");
                alreadyCompletedOnServer = true;

                // Update local state to match server
                if (!completedTasks.includes("visit")) {
                  const newCompletedTasks = [...completedTasks, "visit"];
                  setCompletedTasks(newCompletedTasks);

                  // Update tasks display
                  const updatedTasks = dailyTasks.map(t =>
                    t.id === "visit" ? { ...t, isCompleted: true } : t
                  );
                  setDailyTasks(updatedTasks);
                }

                return; // Skip the rest of the function
              }
            } catch (error) {
              console.error("Error checking if visit task is already completed:", error);
              // Continue with auto-completion if we can't check the server
            }

            if (alreadyCompletedOnServer) {
              return;
            }

            console.log("Auto-completing daily visit task");

            // Mark task as completed
            const updatedTasks = dailyTasks.map(t =>
              t.id === "visit" ? { ...t, isCompleted: true } : t
            );

            // Update completed tasks array
            const newCompletedTasks = [...completedTasks, "visit"];
            setCompletedTasks(newCompletedTasks);
            setDailyTasks(updatedTasks);

            // Save to localStorage immediately as a backup
            localStorage.setItem(`completed-tasks-${user.id}`, JSON.stringify(newCompletedTasks));

            // Set a flag in localStorage to indicate we've already auto-completed the visit task today
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            localStorage.setItem(`visit-auto-completed-${user.id}`, today.getTime().toString());

            // Update server with retry logic
            let retryCount = 0;
            const maxRetries = 3;

            const updateServer = async () => {
              try {
                console.log(`Attempt ${retryCount + 1} to complete visit task on profile server`);

                // Complete task on profile server
                const response = await profileApi.completeTask(user.id, "visit");
                console.log(`Visit task completed on profile server:`, response);

                // Award XP through profile API
                await profileApi.addXp(user.id, visitTask.xpReward, 'task_completion');

                return true;
              } catch (profileErr) {
                console.error(`Attempt ${retryCount + 1} failed:`, profileErr);

                if (retryCount < maxRetries - 1) {
                  retryCount++;
                  // Wait a bit before retrying
                  await new Promise(resolve => setTimeout(resolve, 1000));
                  return await updateServer();
                }

                console.error("All attempts to complete visit task on profile server failed, trying legacy API");

                try {
                  // Try legacy API as fallback
                  await userProgressApi.completeTask(user.id, "visit");
                  console.log(`Visit task completed on legacy server`);

                  // Award XP through legacy API
                  await userProgressApi.addXp(user.id, visitTask.xpReward, 'task_completion');

                  // Also update the user profile with the completed tasks
                  const tasksData = {
                    tasks: {
                      completedTasks: newCompletedTasks,
                      lastReset: lastTaskReset || new Date().setHours(0, 0, 0, 0)
                    }
                  };

                  await userProgressApi.updateUserActivity(user.id, tasksData);

                  return true;
                } catch (legacyErr) {
                  console.error("Error completing visit task on legacy server:", legacyErr);

                  // Fallback to localStorage if both APIs fail
                  // (We already saved to localStorage above)

                  // Award XP locally
                  addXp(visitTask.xpReward);

                  console.log(`Visit task completed in localStorage (fallback)`);
                  return false;
                }
              }
            };

            // Don't await this to avoid blocking the UI
            updateServer().catch(err => {
              console.error("Unexpected error in updateServer:", err);
            });

            // Show toast notification
            toast.success(`Task Completed: ${visitTask.title}`, {
              description: `You earned ${visitTask.xpReward} XP!`,
              duration: 3000
            });
          } catch (error) {
            console.error("Error auto-completing visit task:", error);
          }
        };

        autoCompleteVisitTask();
      } else if (visitTask && !visitTask.isCompleted && visitAlreadyCompleted) {
        // If the task is in completedTasks but not marked as completed in the UI, update the UI
        console.log("Visit task already completed but not shown in UI, updating UI");
        const updatedTasks = dailyTasks.map(t =>
          t.id === "visit" ? { ...t, isCompleted: true } : t
        );
        setDailyTasks(updatedTasks);
      }
    }
  }, [isAuthenticated, user, dailyTasks, completedTasks]);

  // Get task completion progress
  const getTaskProgress = () => {
    if (dailyTasks.length === 0) return 0;

    const regularTasks = dailyTasks.filter(t => !t.isBonus);
    const completedRegularTasks = regularTasks.filter(t => t.isCompleted);

    return Math.round((completedRegularTasks.length / regularTasks.length) * 100);
  };

  // Get total XP available from tasks
  const getTotalAvailableXp = () => {
    return dailyTasks.reduce((total, task) => total + task.xpReward, 0);
  };

  // Get earned XP from completed tasks
  const getEarnedXp = () => {
    return dailyTasks
      .filter(task => task.isCompleted)
      .reduce((total, task) => total + task.xpReward, 0);
  };

  // Debug function to check task verification status
  const debugTaskVerification = () => {
    if (user?.id) {
      const status = checkTaskVerificationStatus(user.id);
      console.log('Task Verification Status:', status);

      // Show toast with basic info
      toast.info('Task Verification Status', {
        description: `Check console for details. Watch Episode: ${status.watchedEpisode.set ? 'Set' : 'Not Set'}`,
        duration: 5000
      });

      return status;
    }
    return null;
  };

  // Debug function to set a task verification flag
  const debugSetTaskFlag = (flagName) => {
    if (user?.id) {
      const result = setTaskVerificationFlag(user.id, flagName);
      console.log(`Set Task Flag Result (${flagName}):`, result);

      // Show toast with result
      if (result.success) {
        toast.success('Task Flag Set', {
          description: result.message,
          duration: 3000
        });
      } else {
        toast.error('Failed to Set Task Flag', {
          description: result.error,
          duration: 3000
        });
      }

      return result;
    }
    return null;
  };

  return (
    <TaskContext.Provider
      value={{
        dailyTasks,
        completedTasks,
        completeTask,
        getTaskProgress,
        getTotalAvailableXp,
        getEarnedXp,
        debugTaskVerification,
        debugSetTaskFlag
      }}
    >
      {children}
    </TaskContext.Provider>
  );
};
