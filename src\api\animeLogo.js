import axios from "axios";

/**
 * Fetches anime logo from the custom API
 * @param {number|string} anilistId - The AniList ID of the anime
 * @returns {Promise<Object>} - Object containing logo information
 */
export const getAnimeLogo = async (anilistId) => {
  try {
    console.log(`Fetching logo for anime ID: ${anilistId}`);
    const response = await axios.get(`https://misty-tooth-a88b.exodraw.workers.dev/api/anime/${anilistId}/logo`);
    console.log(`Logo data for anime ID ${anilistId}:`, response.data);
    return response.data;
  } catch (error) {
    console.error(`Error fetching logo for anime ID ${anilistId}:`, error);
    return null;
  }
};
