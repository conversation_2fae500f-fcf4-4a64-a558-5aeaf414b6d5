import React, { memo, useMemo } from 'react';
import { <PERSON> } from 'react-router-dom';
import Image from '@/components/ui/Image';
import { Calendar, Clock, Star, CalendarClock, Eye, Bell } from 'lucide-react';
import AgeRating from '@/components/ui/AgeRating';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";

// Memoized ComingSoonAnime component
const ComingSoonAnime = memo(({ data }) => {
  // Calculate days until release for an anime - memoized helper function
  const getDaysUntilRelease = useMemo(() => {
    return (anime) => {
      if (!anime?.release_date) return null;

      // Try to parse the release date
      try {
        const releaseDate = new Date(anime.release_date);
        const today = new Date();
        const diffTime = releaseDate - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays < 0) return null;
        return diffDays;
      } catch (e) {
        return null;
      }
    };
  }, []);

  // Handle empty or loading state
  if (!data || data.length === 0) {
    return (
      <div className="w-full flex flex-col gap-4">
        <div className="flex items-center gap-2 mb-3">
          <div className="bg-white/20 p-1.5 rounded-md animate-pulse">
            <CalendarClock size={16} className="text-white/50" />
          </div>
          <div className="h-6 w-48 bg-white/10 animate-pulse rounded"></div>
          <div className="flex-grow"></div>
          <div className="h-4 w-16 bg-white/10 animate-pulse rounded"></div>
        </div>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="flex flex-col gap-2">
              <div className="aspect-[1/1.45] bg-white/10 animate-pulse rounded-xl"></div>
              <div className="h-4 w-full bg-white/5 animate-pulse rounded"></div>
              <div className="h-4 w-2/3 bg-white/5 animate-pulse rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col gap-5">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <div className="bg-black/40 backdrop-blur-sm p-2 rounded-md border border-white/10">
            <CalendarClock size={16} className="text-white" />
          </div>
          <h2 className="text-xl lg:text-2xl font-semibold">Coming Soon</h2>
        </div>
        <Link
          to="/explore?status=NOT_YET_RELEASED"
          className="bg-black/40 backdrop-blur-sm hover:bg-black/60 text-white px-3 py-1.5 rounded-md text-sm transition-all duration-200 flex items-center gap-2 border border-white/10 hover:border-white/20"
        >
          <span>View All</span>
          <Eye size={14} />
        </Link>
      </div>

      {/* Featured Coming Soon - First anime gets special treatment */}
      {data.length > 0 && (
        <div className="w-full">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Featured anime - takes up 2/3 of the space on desktop */}
            <div className="md:col-span-2">
              <Link
                to={data[0]?.id ? `/anime/${data[0]?.id}` : ""}
                className="group relative block w-full h-[240px] md:h-[320px] rounded-xl overflow-hidden border border-white/10 hover:border-white/20 transition-all duration-300 shadow-lg"
              >
                {/* Background image */}
                <div className="absolute inset-0">
                  <Image
                    src={data[0]?.images?.bannerLarge || data[0]?.images?.bannerSmall || data[0]?.images?.coverLarge}
                    className="object-cover w-full h-full group-hover:scale-105 transition-all duration-500"
                    quality="high"
                    loading="lazy"
                  />
                  {/* Simplified overlay - removed backdrop-blur */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-black/20 opacity-70 group-hover:opacity-80 transition-opacity duration-300"></div>
                </div>

                {/* Content */}
                <div className="absolute inset-0 flex flex-col justify-end p-6 z-10">
                  {/* Coming soon badge */}
                  {getDaysUntilRelease(data[0]) !== null && (
                    <div className="absolute top-4 right-4 bg-black/60 text-white px-3 py-1.5 rounded-md text-sm font-bold border border-white/10 flex items-center gap-2">
                      <Clock size={16} />
                      {getDaysUntilRelease(data[0]) === 0 ? 'Releasing Today' : `${getDaysUntilRelease(data[0])} days left`}
                    </div>
                  )}

                  {/* Title and info */}
                  <h3 className="text-2xl md:text-3xl font-bold text-white group-hover:text-white/90 transition-colors duration-300 mb-2">{data[0]?.title}</h3>

                  {/* Status Indicator - With breathing effect */}
                  <div className="flex items-center gap-2 mb-3">
                    <span className="relative flex h-3 w-3 bg-yellow-500 rounded-full">
                      <span className="absolute inline-flex h-full w-full rounded-full bg-yellow-400 opacity-75 animate-ping"></span>
                    </span>
                    <span className="text-sm text-white/80">Coming Soon</span>
                  </div>

                  <div className="flex flex-wrap items-center text-sm text-gray-300 mb-3">
                    <span className="uppercase">{data[0]?.type}</span>
                    {data[0]?.episodes && (
                      <>
                        <span className="mx-1">•</span>
                        <span>{data[0]?.episodes} Episodes</span>
                      </>
                    )}
                    {data[0]?.season && (
                      <>
                        <span className="mx-1">•</span>
                        <span>{data[0]?.season} {data[0]?.release_date?.slice(-4)}</span>
                      </>
                    )}
                  </div>

                  {/* Genres - limited to 3 */}
                  {data[0]?.genres && data[0].genres.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-4">
                      {data[0].genres.slice(0, 3).map((genre, i) => (
                        <span key={i} className="text-xs px-2 py-0.5 bg-black/60 text-white rounded-full border border-white/10">
                          {typeof genre === 'string' ? genre : ''}
                        </span>
                      ))}
                    </div>
                  )}

                  {/* Add to watchlist button */}
                  <div className="bg-white text-black font-bold px-4 py-2 rounded-full flex items-center gap-2 w-fit">
                    <Bell fill="black" size={16} />
                    <span>Add to Watchlist</span>
                  </div>
                </div>
              </Link>
            </div>

            {/* Right side - next 2 upcoming anime */}
            <div className="flex flex-col gap-4">
              {data.slice(1, 3).map((anime, index) => (
                <Link
                  key={index}
                  to={anime?.id ? `/anime/${anime?.id}` : ""}
                  className="group relative block w-full h-[150px] rounded-xl overflow-hidden border border-white/10 hover:border-white/20 transition-all duration-300 shadow-lg"
                >
                  {/* Background image */}
                  <div className="absolute inset-0">
                    <Image
                      src={anime?.images?.bannerLarge || anime?.images?.bannerSmall || anime?.images?.coverLarge}
                      className="object-cover w-full h-full group-hover:scale-105 transition-all duration-500"
                      quality="high"
                      loading="lazy"
                    />
                    {/* Simplified overlay - removed backdrop-blur */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-black/20 opacity-70 group-hover:opacity-80 transition-opacity duration-300"></div>
                  </div>

                  {/* Content */}
                  <div className="absolute inset-0 flex flex-col justify-end p-4 z-10">
                    {/* Coming soon badge */}
                    {getDaysUntilRelease(anime) !== null && (
                      <div className="absolute top-2 right-2 bg-black/60 text-white px-2 py-1 rounded-md text-xs font-bold border border-white/10 flex items-center gap-1">
                        <Clock size={12} />
                        {getDaysUntilRelease(anime) === 0 ? 'Today' : `${getDaysUntilRelease(anime)}d`}
                      </div>
                    )}

                    {/* Title and info */}
                    <h3 className="text-lg font-bold text-white group-hover:text-white/90 transition-colors duration-300 line-clamp-1">{anime?.title}</h3>

                    {/* Status Indicator - With breathing effect */}
                    <div className="flex items-center gap-1.5 mt-1">
                      <span className="relative flex h-2 w-2 bg-yellow-500 rounded-full">
                        <span className="absolute inline-flex h-full w-full rounded-full bg-yellow-400 opacity-75 animate-ping"></span>
                      </span>
                      <span className="text-[10px] text-white/80">Coming Soon</span>
                    </div>

                    <div className="flex items-center text-xs text-gray-300 mt-1">
                      <span className="uppercase">{anime?.type}</span>
                      {anime?.season && (
                        <>
                          <span className="mx-1">•</span>
                          <span>{anime?.season} {anime?.release_date?.slice(-4)}</span>
                        </>
                      )}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Carousel for remaining upcoming anime */}
      <div className="w-full">
        <Carousel
          opts={{
            align: "start",
            loop: true,
          }}
          className="w-full"
        >
          <CarouselContent className="-ml-4">
            {data.slice(3, 15).map((anime, index) => (
              <CarouselItem key={index} className="pl-4 basis-1/2 sm:basis-1/3 md:basis-1/4 lg:basis-1/5 xl:basis-1/6">
                <ComingSoonCard anime={anime} />
              </CarouselItem>
            ))}
          </CarouselContent>
          <div className="absolute -bottom-12 left-0 right-0 flex justify-center gap-2 mt-4">
            <CarouselPrevious className="static translate-y-0 bg-black/40 backdrop-blur-sm hover:bg-black/60 border border-white/10 hover:border-white/20 text-white h-8 w-8" />
            <CarouselNext className="static translate-y-0 bg-black/40 backdrop-blur-sm hover:bg-black/60 border border-white/10 hover:border-white/20 text-white h-8 w-8" />
          </div>
        </Carousel>
      </div>
    </div>
  );
});

// Memoized ComingSoonCard component for better performance
const ComingSoonCard = memo(({ anime }) => {
  // Calculate days until release - memoized to prevent recalculation
  const daysUntil = useMemo(() => {
    if (!anime?.release_date) return null;

    // Try to parse the release date
    try {
      const releaseDate = new Date(anime.release_date);
      const today = new Date();
      const diffTime = releaseDate - today;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays < 0) return null;
      return diffDays;
    } catch (e) {
      return null;
    }
  }, [anime?.release_date]);

  return (
    <div className="flex size-full flex-col group cursor-pointer">
      {/* Card Container */}
      <div className="relative w-full h-full transition-all duration-300 group-hover:scale-[1.03]">
        {/* Main Card */}
        <div className="relative w-full aspect-[1/1.45] rounded-xl overflow-hidden shadow-lg border border-white/10">
          {/* Background Image */}
          <div className="absolute inset-0 w-full h-full">
            <Image
              src={anime?.images?.coverLarge || anime?.images?.coverMedium || anime?.images?.coverSmall}
              quality="high"
              className="!object-cover !w-full !h-full transition-all duration-500 group-hover:scale-110"
              loading="lazy"
            />
          </div>

          {/* Simplified Overlay - removed backdrop-blur for better performance */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-black/20 opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>

          {/* Content Container */}
          <Link
            to={anime?.id ? `/anime/${anime?.id}` : ""}
            className="absolute inset-0 flex flex-col justify-between p-3 z-10"
          >
            {/* Top Section */}
            <div className="flex flex-col items-start gap-1">
              {/* Age Rating */}
              <AgeRating isAdult={anime?.isAdult} genres={anime?.genres} compact={true} />

              {/* Coming soon badge */}
              {daysUntil !== null && (
                <div className="absolute top-2 right-2 bg-black/60 text-white text-xs font-bold px-2 py-1 rounded-md border border-white/10 flex items-center gap-1">
                  <Clock size={12} />
                  {daysUntil === 0 ? 'Today' : `${daysUntil}d`}
                </div>
              )}
            </div>

            {/* Bottom Section */}
            <div className="mt-auto">
              {/* Title */}
              <h3 className="text-sm font-medium line-clamp-2 text-white mb-1">
                {anime?.title}
              </h3>

              {/* Status Indicator - With breathing effect */}
              <div className="flex items-center gap-1.5 mb-1">
                <span className="relative flex h-2 w-2 bg-yellow-500 rounded-full">
                  <span className="absolute inline-flex h-full w-full rounded-full bg-yellow-400 opacity-75 animate-ping"></span>
                </span>
                <span className="text-[10px] text-white/80">Coming Soon</span>
              </div>

              {/* Metadata */}
              <div className="flex items-center gap-2 text-[10px] text-white/70">
                <span className="uppercase">{anime?.type}</span>
                {anime?.episodes && (
                  <>
                    <span className="mx-0.5">•</span>
                    <span>{anime?.episodes} Eps</span>
                  </>
                )}
                {anime?.release_date && (
                  <>
                    <span className="mx-0.5">•</span>
                    <span>{anime?.release_date?.slice(-4)}</span>
                  </>
                )}
              </div>

              {/* Release season badge */}
              {anime?.season && (
                <div className="absolute bottom-3 left-3 bg-black/60 text-white text-xs px-2 py-1 rounded-md border border-white/10 flex items-center gap-1">
                  <Calendar size={12} />
                  {anime.season} {anime?.release_date?.slice(-4)}
                </div>
              )}

              {/* Rating */}
              {Number(anime?.rating) > 0 && (
                <div className="absolute bottom-3 right-3 flex items-center gap-1 bg-black/60 text-white px-2 py-1 rounded-md text-xs border border-white/10">
                  <Star fill="white" stroke="white" size={10} />
                  <span>{Number(anime?.rating)?.toFixed(1)}</span>
                </div>
              )}
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
});

export default ComingSoonAnime;
