import React, { useEffect, useRef, useState } from 'react';

/**
 * ScrollingText component that automatically scrolls text when it's too long for its container
 * Optimized for small screens with a simpler, more reliable animation
 *
 * @param {Object} props
 * @param {string} props.text - The text to display and potentially scroll
 * @param {string} props.className - Additional CSS classes for the container
 * @param {number} props.maxWidth - Maximum width in pixels before scrolling is triggered
 * @param {number} props.speed - Speed of the scrolling animation (pixels per second)
 */
const ScrollingText = ({
  text,
  className = "",
  maxWidth = 150,
  speed = 30
}) => {
  const containerRef = useRef(null);
  const textRef = useRef(null);
  const [shouldScroll, setShouldScroll] = useState(false);
  const [animationName, setAnimationName] = useState('');

  // Check if text needs to scroll and set up the animation
  useEffect(() => {
    if (!containerRef.current || !textRef.current) return;

    // Reset any existing animation
    if (textRef.current.style.animation) {
      textRef.current.style.animation = 'none';
    }

    // Force reflow
    void textRef.current.offsetWidth;

    // Measure text and container
    const textWidth = textRef.current.scrollWidth;
    const containerWidth = containerRef.current.clientWidth;

    // Only scroll if text is wider than container
    if (textWidth > containerWidth) {
      setShouldScroll(true);

      // Create a unique animation name to avoid conflicts
      const uniqueName = `scroll_${Math.random().toString(36).substr(2, 9)}`;
      setAnimationName(uniqueName);

      // Calculate animation duration based on text length and desired speed
      // Use a minimum duration to ensure smooth animation even for short text
      const duration = Math.max(5, textWidth / speed);

      // Create and insert the keyframes - simplified for player header
      const styleSheet = document.styleSheets[0];
      const keyframesRule = `
        @keyframes ${uniqueName} {
          0%, 15% { transform: translateX(0); }
          85%, 100% { transform: translateX(-${textWidth - containerWidth}px); }
        }
      `;

      try {
        styleSheet.insertRule(keyframesRule, styleSheet.cssRules.length);

        // Apply the animation - use alternate for a back-and-forth effect
        textRef.current.style.animation = `${uniqueName} ${duration}s linear 1s infinite alternate`;
      } catch (e) {
        console.error('Error setting up scroll animation:', e);
      }
    } else {
      setShouldScroll(false);
    }

    // Clean up animation rules when component unmounts
    return () => {
      if (animationName) {
        try {
          // Find and remove the keyframes rule
          for (let i = 0; i < document.styleSheets[0].cssRules.length; i++) {
            const rule = document.styleSheets[0].cssRules[i];
            if (rule.name === animationName) {
              document.styleSheets[0].deleteRule(i);
              break;
            }
          }
        } catch (e) {
          // Ignore errors during cleanup
        }
      }
    };
  }, [text, maxWidth, speed]);

  return (
    <div
      ref={containerRef}
      className={`overflow-hidden ${className}`}
      style={{
        maxWidth: `${maxWidth}px`,
        display: 'inline-block',
        verticalAlign: 'middle'
      }}
    >
      <div
        ref={textRef}
        className="whitespace-nowrap inline-block text-ellipsis"
        style={{
          textOverflow: 'ellipsis',
          overflow: 'hidden'
        }}
      >
        {text}
      </div>
    </div>
  );
};

export default ScrollingText;
