import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}
export const discover_options = {
  types: [
    { name: "Movie", value: "movie" },
    { name: "Show", value: "tv" },
  ],
  studios: [
    { name: "Any", value: "any" },
    { name: "Toei Animation", value: "2" },
    { name: "Kyoto Animation", value: "3" },
    { name: "Madhouse", value: "11" },
    { name: "Production I.G", value: "10" },
    { name: "<PERSON>", value: "4" },
    { name: "<PERSON>", value: "14" },
    { name: "A-1 Pictures", value: "1" },
    { name: "J.C.Staff", value: "7" },
    { name: "Studio Ghibli", value: "6" },
    { name: "Wit Studio", value: "858" },
    { name: "MAPPA", value: "569" },
    { name: "ufotable", value: "43" },
    { name: "<PERSON><PERSON>", value: "44" },
    { name: "<PERSON><PERSON>", value: "803" },
    { name: "White Fox", value: "314" },
    { name: "CloverWorks", value: "1835" },
    { name: "Studio Deen", value: "37" },
    { name: "Gainax", value: "6" },
    { name: "Pierrot", value: "1" },
    { name: "P.A. Works", value: "132" },
    { name: "Silver Link", value: "300" },
    { name: "Gonzo", value: "3" },
    { name: "Brain's Base", value: "112" },
    { name: "Lerche", value: "456" },
  ],
  sorts: {
    movie: [
      { name: "Most Popular", value: "popularity.desc" },
      { name: "Latest Release", value: "primary_release_date.desc" },
      { name: "Oldest Release ", value: "primary_release_date.asc" },
      { name: "Title Asc", value: "title.desc" },
      { name: "Title Desc", value: "title.asc" },
      { name: "High Rated", value: "vote_average.desc" },
      { name: "Low Rated", value: "vote_average.asc" },
    ],
    tv: [
      { name: "Most Popular", value: "popularity.desc" },
      { name: "Latest Release", value: "first_air_date.desc" },
      { name: "Oldest Release ", value: "first_air_date.asc" },
      { name: "Title Asc", value: "name.asc" },
      { name: "Title Desc", value: "name.desc" },
      { name: "High Rated", value: "vote_average.desc" },
      { name: "Low Rated", value: "vote_average.asc" },
    ],
  },
  years: [
    { name: "Any", value: "any" },
    { name: "2024", value: "2024" },
    { name: "2023", value: "2023" },
    { name: "2022", value: "2022" },
    { name: "2021", value: "2021" },
    { name: "2020", value: "2020" },
    { name: "2019", value: "2019" },
    { name: "2018", value: "2018" },
    { name: "2017", value: "2017" },
    { name: "2016", value: "2016" },
    { name: "2015", value: "2015" },
    { name: "2014", value: "2014" },
    { name: "2013", value: "2013" },
    { name: "2012", value: "2012" },
    { name: "2011", value: "2011" },
    { name: "2010", value: "2010" },
    { name: "2009", value: "2009" },
    { name: "2008", value: "2008" },
    { name: "2007", value: "2007" },
    { name: "2006", value: "2006" },
    { name: "2005", value: "2005" },
    { name: "2004", value: "2004" },
    { name: "2003", value: "2003" },
    { name: "2002", value: "2002" },
    { name: "2001", value: "2001" },
    { name: "2000", value: "2000" },
    { name: "1999", value: "1999" },
    { name: "1998", value: "1998" },
    { name: "1997", value: "1997" },
    { name: "1996", value: "1996" },
    { name: "1995", value: "1995" },
    { name: "1994", value: "1994" },
    { name: "1993", value: "1993" },
    { name: "1992", value: "1992" },
    { name: "1991", value: "1991" },
    { name: "1990", value: "1990" },
    { name: "1989", value: "1989" },
    { name: "1988", value: "1988" },
    { name: "1987", value: "1987" },
    { name: "1986", value: "1986" },
    { name: "1985", value: "1985" },
    { name: "1984", value: "1984" },
    { name: "1983", value: "1983" },
    { name: "1982", value: "1982" },
    { name: "1981", value: "1981" },
    { name: "1980", value: "1980" },
    { name: "1979", value: "1979" },
    { name: "1978", value: "1978" },
    { name: "1977", value: "1977" },
    { name: "1976", value: "1976" },
    { name: "1975", value: "1975" },
    { name: "1974", value: "1974" },
    { name: "1973", value: "1973" },
    { name: "1972", value: "1972" },
    { name: "1971", value: "1971" },
    { name: "1970", value: "1970" },
    { name: "1969", value: "1969" },
    { name: "1968", value: "1968" },
    { name: "1967", value: "1967" },
    { name: "1966", value: "1966" },
    { name: "1965", value: "1965" },
    { name: "1964", value: "1964" },
    { name: "1963", value: "1963" },
    { name: "1962", value: "1962" },
    { name: "1961", value: "1961" },
    { name: "1960", value: "1960" },
    { name: "1959", value: "1959" },
    { name: "1958", value: "1958" },
    { name: "1957", value: "1957" },
    { name: "1956", value: "1956" },
    { name: "1955", value: "1955" },
    { name: "1954", value: "1954" },
    { name: "1953", value: "1953" },
    { name: "1952", value: "1952" },
    { name: "1951", value: "1951" },
    { name: "1950", value: "1950" },
    { name: "1949", value: "1949" },
    { name: "1948", value: "1948" },
    { name: "1947", value: "1947" },
    { name: "1946", value: "1946" },
    { name: "1945", value: "1945" },
    { name: "1944", value: "1944" },
    { name: "1943", value: "1943" },
    { name: "1942", value: "1942" },
    { name: "1941", value: "1941" },
    { name: "1940", value: "1940" },
  ],
  genres: {
    movie: [
      { name: "Any", value: "any" },
      {
        value: "28",
        name: "Action",
      },
      {
        value: "12",
        name: "Adventure",
      },
      {
        value: "16",
        name: "Animation",
      },
      {
        value: "35",
        name: "Comedy",
      },
      {
        value: "80",
        name: "Crime",
      },
      {
        value: "99",
        name: "Documentary",
      },
      {
        value: "18",
        name: "Drama",
      },
      {
        value: "10751",
        name: "Family",
      },
      {
        value: "14",
        name: "Fantasy",
      },
      {
        value: "36",
        name: "History",
      },
      {
        value: "27",
        name: "Horror",
      },
      {
        value: "10402",
        name: "Music",
      },
      {
        value: "9648",
        name: "Mystery",
      },
      {
        value: "10749",
        name: "Romance",
      },
      {
        value: "878",
        name: "Science Fiction",
      },
      {
        value: "10770",
        name: "TV Movie",
      },
      {
        value: "53",
        name: "Thriller",
      },
      {
        value: "10752",
        name: "War",
      },
      {
        value: "37",
        name: "Western",
      },
    ],
    tv: [
      { name: "Any", value: "any" },
      {
        value: "10759",
        name: "Action & Adventure",
      },
      {
        value: "16",
        name: "Animation",
      },
      {
        value: "35",
        name: "Comedy",
      },
      {
        value: "80",
        name: "Crime",
      },
      {
        value: "99",
        name: "Documentary",
      },
      {
        value: "18",
        name: "Drama",
      },
      {
        value: "10751",
        name: "Family",
      },
      {
        value: "10762",
        name: "Kids",
      },
      {
        value: "9648",
        name: "Mystery",
      },
      {
        value: "10763",
        name: "News",
      },
      {
        value: "10764",
        name: "Reality",
      },
      {
        value: "10765",
        name: "Sci-Fi & Fantasy",
      },
      {
        value: "10766",
        name: "Soap",
      },
      {
        value: "10767",
        name: "Talk",
      },
      {
        value: "10768",
        name: "War & Politics",
      },
      {
        value: "37",
        name: "Western",
      },
    ],
  },
  country_codes: [
    { name: "Any", value: "any" },
    { name: "Afghanistan", value: "AF" },
    { name: "Albania", value: "AL" },
    { name: "Algeria", value: "DZ" },
    { name: "Andorra", value: "AD" },
    { name: "Angola", value: "AO" },
    { name: "Antigua and Barbuda", value: "AG" },
    { name: "Argentina", value: "AR" },
    { name: "Armenia", value: "AM" },
    { name: "Australia", value: "AU" },
    { name: "Austria", value: "AT" },
    { name: "Azerbaijan", value: "AZ" },
    { name: "Bahamas", value: "BS" },
    { name: "Bahrain", value: "BH" },
    { name: "Bangladesh", value: "BD" },
    { name: "Barbados", value: "BB" },
    { name: "Belarus", value: "BY" },
    { name: "Belgium", value: "BE" },
    { name: "Belize", value: "BZ" },
    { name: "Benin", value: "BJ" },
    { name: "Bhutan", value: "BT" },
    { name: "Bolivia", value: "BO" },
    { name: "Bosnia and Herzegovina", value: "BA" },
    { name: "Botswana", value: "BW" },
    { name: "Brazil", value: "BR" },
    { name: "Brunei Darussalam", value: "BN" },
    { name: "Bulgaria", value: "BG" },
    { name: "Burkina Faso", value: "BF" },
    { name: "Burundi", value: "BI" },
    { name: "Cambodia", value: "KH" },
    { name: "Cameroon", value: "CM" },
    { name: "Canada", value: "CA" },
    { name: "Cape Verde", value: "CV" },
    { name: "Central African Republic", value: "CF" },
    { name: "Chad", value: "TD" },
    { name: "Chile", value: "CL" },
    { name: "China", value: "CN" },
    { name: "Colombia", value: "CO" },
    { name: "Comoros", value: "KM" },
    { name: "Congo (Brazzaville)", value: "CG" },
    { name: "Congo (Kinshasa)", value: "CD" },
    { name: "Costa Rica", value: "CR" },
    { name: "Croatia", value: "HR" },
    { name: "Cuba", value: "CU" },
    { name: "Cyprus", value: "CY" },
    { name: "Czech Republic", value: "CZ" },
    { name: "Côte d'Ivoire", value: "CI" },
    { name: "Denmark", value: "DK" },
    { name: "Djibouti", value: "DJ" },
    { name: "Dominica", value: "DM" },
    { name: "Dominican Republic", value: "DO" },
    { name: "Ecuador", value: "EC" },
    { name: "Egypt", value: "EG" },
    { name: "El Salvador", value: "SV" },
    { name: "Equatorial Guinea", value: "GQ" },
    { name: "Eritrea", value: "ER" },
    { name: "Estonia", value: "EE" },
    { name: "Eswatini", value: "SZ" },
    { name: "Ethiopia", value: "ET" },
    { name: "Fiji", value: "FJ" },
    { name: "Finland", value: "FI" },
    { name: "France", value: "FR" },
    { name: "Gabon", value: "GA" },
    { name: "Gambia", value: "GM" },
    { name: "Georgia", value: "GE" },
    { name: "Germany", value: "DE" },
    { name: "Ghana", value: "GH" },
    { name: "Greece", value: "GR" },
    { name: "Grenada", value: "GD" },
    { name: "Guatemala", value: "GT" },
    { name: "Guinea", value: "GN" },
    { name: "Guinea-Bissau", value: "GW" },
    { name: "Guyana", value: "GY" },
    { name: "Haiti", value: "HT" },
    { name: "Honduras", value: "HN" },
    { name: "Hungary", value: "HU" },
    { name: "Iceland", value: "IS" },
    { name: "India", value: "IN" },
    { name: "Indonesia", value: "ID" },
    { name: "Iran", value: "IR" },
    { name: "Iraq", value: "IQ" },
    { name: "Ireland", value: "IE" },
    { name: "Israel", value: "IL" },
    { name: "Italy", value: "IT" },
    { name: "Jamaica", value: "JM" },
    { name: "Japan", value: "JP" },
    { name: "Jordan", value: "JO" },
    { name: "Kazakhstan", value: "KZ" },
    { name: "Kenya", value: "KE" },
    { name: "Kiribati", value: "KI" },
    { name: "Kuwait", value: "KW" },
    { name: "Kyrgyzstan", value: "KG" },
    { name: "Laos", value: "LA" },
    { name: "Latvia", value: "LV" },
    { name: "Lebanon", value: "LB" },
    { name: "Lesotho", value: "LS" },
    { name: "Liberia", value: "LR" },
    { name: "Libya", value: "LY" },
    { name: "Liechtenstein", value: "LI" },
    { name: "Lithuania", value: "LT" },
    { name: "Luxembourg", value: "LU" },
    { name: "Madagascar", value: "MG" },
    { name: "Malawi", value: "MW" },
    { name: "Malaysia", value: "MY" },
    { name: "Maldives", value: "MV" },
    { name: "Mali", value: "ML" },
    { name: "Malta", value: "MT" },
    { name: "Marshall Islands", value: "MH" },
    { name: "Mauritania", value: "MR" },
    { name: "Mauritius", value: "MU" },
    { name: "Mexico", value: "MX" },
    { name: "Micronesia", value: "FM" },
    { name: "Moldova", value: "MD" },
    { name: "Monaco", value: "MC" },
    { name: "Mongolia", value: "MN" },
    { name: "Montenegro", value: "ME" },
    { name: "Morocco", value: "MA" },
    { name: "Mozambique", value: "MZ" },
    { name: "Myanmar", value: "MM" },
    { name: "Namibia", value: "NA" },
    { name: "Nauru", value: "NR" },
    { name: "Nepal", value: "NP" },
    { name: "Netherlands", value: "NL" },
    { name: "New Zealand", value: "NZ" },
    { name: "Nicaragua", value: "NI" },
    { name: "Niger", value: "NE" },
    { name: "Nigeria", value: "NG" },
    { name: "North Korea", value: "KP" },
    { name: "North Macedonia", value: "MK" },
    { name: "Norway", value: "NO" },
    { name: "Oman", value: "OM" },
    { name: "Pakistan", value: "PK" },
    { name: "Palau", value: "PW" },
    { name: "Panama", value: "PA" },
    { name: "Papua New Guinea", value: "PG" },
    { name: "Paraguay", value: "PY" },
    { name: "Peru", value: "PE" },
    { name: "Philippines", value: "PH" },
    { name: "Poland", value: "PL" },
    { name: "Portugal", value: "PT" },
    { name: "Qatar", value: "QA" },
    { name: "Romania", value: "RO" },
    { name: "Russia", value: "RU" },
    { name: "Rwanda", value: "RW" },
    { name: "Saint Kitts and Nevis", value: "KN" },
    { name: "Saint Lucia", value: "LC" },
    { name: "Saint Vincent and the Grenadines", value: "VC" },
    { name: "Samoa", value: "WS" },
    { name: "San Marino", value: "SM" },
    { name: "Sao Tome and Principe", value: "ST" },
    { name: "Saudi Arabia", value: "SA" },
    { name: "Senegal", value: "SN" },
    { name: "Serbia", value: "RS" },
    { name: "Seychelles", value: "SC" },
    { name: "Sierra Leone", value: "SL" },
    { name: "Singapore", value: "SG" },
    { name: "Slovakia", value: "SK" },
    { name: "Slovenia", value: "SI" },
    { name: "Solomon Islands", value: "SB" },
    { name: "Somalia", value: "SO" },
    { name: "South Africa", value: "ZA" },
    { name: "South Korea", value: "KR" },
    { name: "South Sudan", value: "SS" },
    { name: "Spain", value: "ES" },
    { name: "Sri Lanka", value: "LK" },
    { name: "Sudan", value: "SD" },
    { name: "Suriname", value: "SR" },
    { name: "Sweden", value: "SE" },
    { name: "Switzerland", value: "CH" },
    { name: "Syria", value: "SY" },
    { name: "Taiwan", value: "TW" },
    { name: "Tajikistan", value: "TJ" },
    { name: "Tanzania", value: "TZ" },
    { name: "Thailand", value: "TH" },
    { name: "Timor-Leste", value: "TL" },
    { name: "Togo", value: "TG" },
    { name: "Tonga", value: "TO" },
    { name: "Trinidad and Tobago", value: "TT" },
    { name: "Tunisia", value: "TN" },
    { name: "Turkey", value: "TR" },
    { name: "Turkmenistan", value: "TM" },
    { name: "Tuvalu", value: "TV" },
    { name: "Uganda", value: "UG" },
    { name: "Ukraine", value: "UA" },
    { name: "United Arab Emirates", value: "AE" },
    { name: "United Kingdom", value: "GB" },
    { name: "United States", value: "US" },
    { name: "Uruguay", value: "UY" },
    { name: "Uzbekistan", value: "UZ" },
    { name: "Vanuatu", value: "VU" },
    { name: "Vatican City", value: "VA" },
    { name: "Venezuela", value: "VE" },
    { name: "Vietnam", value: "VN" },
    { name: "Yemen", value: "YE" },
    { name: "Zambia", value: "ZM" },
    { name: "Zimbabwe", value: "ZW" },
  ],
};
