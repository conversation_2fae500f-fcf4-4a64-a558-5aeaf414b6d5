import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import Image from "../ui/Image";
import { Link } from "react-router-dom";
import { useMemo, memo } from "react";
import { Star, Play, Eye } from "lucide-react";
import AgeRating from "@/components/ui/AgeRating";

// Memoized Card component for better performance
const Card = memo(({ data: x }) => {
  return (
    <div
      title={x?.title + " - " + x?.description}
      className="flex size-full flex-col group cursor-pointer"
    >
      {/* Card Container */}
      <div className="relative w-full h-full transition-all duration-300 group-hover:scale-[1.03]">
        {/* Main Card */}
        <div className="relative w-full aspect-[1/1.45] rounded-xl overflow-hidden shadow-lg border border-white/10">
          {/* Background Image */}
          <div className="absolute inset-0 w-full h-full">
            <Image
              src={x?.images?.coverLarge || x?.images?.coverMedium || x?.images?.coverSmall}
              quality="high"
              className="!object-cover !w-full !h-full transition-all duration-500 group-hover:scale-110 group-hover:brightness-110"
              loading="lazy"
            />
          </div>

          {/* Simplified Overlay - removed backdrop-blur for better performance */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-black/20 opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>

          {/* Content Container */}
          <Link
            to={x?.id ? `/anime/${x?.id}` : ""}
            className="absolute inset-0 flex flex-col justify-between p-3 z-10"
          >
            {/* Top Section - Repositioned to avoid overlap */}
            <div className="flex flex-col items-start gap-1">
              {/* Age Rating - Positioned at top left */}
              <AgeRating isAdult={x?.isAdult} genres={x?.genres} compact={true} />

              {/* Rating Badge - Positioned at top right - simplified */}
              {Number(x?.rating) > 0 && (
                <div className="absolute top-2 right-2 flex items-center gap-1 bg-black/60 text-white px-2 py-1 rounded-md text-xs border border-white/20">
                  <Star fill="white" stroke="white" size={12} />
                  <span>{Number(x?.rating)?.toFixed(1)}</span>
                </div>
              )}
            </div>

            {/* Bottom Section */}
            <div className="mt-auto">
              {/* Title */}
              <h3 className="text-sm font-medium line-clamp-2 text-white mb-1">
                {x?.title}
              </h3>

              {/* Status Indicator - With breathing effect for Airing */}
              {x?.status && (
                <div className="flex items-center gap-1.5 mb-1">
                  <span className={`relative flex h-2 w-2 ${
                    x?.status === "RELEASING" ? "bg-green-500" :
                    x?.status === "FINISHED" ? "bg-blue-500" :
                    x?.status === "NOT_YET_RELEASED" ? "bg-yellow-500" :
                    x?.status === "CANCELLED" ? "bg-red-500" :
                    "bg-gray-500"
                  } rounded-full`}>
                    {x?.status === "RELEASING" && (
                      <span className="absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75 animate-ping"></span>
                    )}
                  </span>
                  <span className="text-[10px] text-white/80">
                    {x?.status === "RELEASING" ? "Airing" :
                     x?.status === "FINISHED" ? "Completed" :
                     x?.status === "NOT_YET_RELEASED" ? "Coming Soon" :
                     x?.status === "CANCELLED" ? "Cancelled" :
                     "Unknown"}
                  </span>
                </div>
              )}

              {/* Metadata */}
              <div className="flex items-center gap-2 text-[10px] text-white/70">
                <span className="uppercase">{x?.type}</span>
                {x?.release_date && (
                  <>
                    <span className="mx-0.5">•</span>
                    <span>{x?.release_date?.slice(-4)}</span>
                  </>
                )}
              </div>
            </div>

            {/* Watch Button - Simplified */}
            <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="bg-white text-black font-bold px-4 py-2 rounded-full flex items-center gap-2">
                <Play fill="black" size={16} />
                <span>Watch</span>
              </div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
});

// Main Strip component for carousel display - memoized for better performance
const Strip = memo(({ title, data }) => {
  const d = useMemo(() => {
    return data?.length ? data : Array.from({ length: 16 });
  }, [data]);

  // Extract genre from title for view all link
  const getGenreFromTitle = () => {
    if (!title) return '';
    const words = title.split(' ');
    return words[0].toLowerCase();
  };

  return (
    <div className="w-full flex flex-col gap-4">
      {/* Glassy Black and White Section Header */}
      <div className="flex justify-between items-center mb-2">
        <div className="flex items-center gap-2">
          <h2 className="text-xl lg:text-2xl font-semibold text-white">{title}</h2>
        </div>

        <Link
          to={`/explore?genre=${getGenreFromTitle()}`}
          className="bg-black/40 hover:bg-black/60 text-white px-3 py-1.5 rounded-md text-sm transition-all duration-200 flex items-center gap-2 border border-white/10 hover:border-white/20"
        >
          <span>View All</span>
          <Eye size={14} />
        </Link>
      </div>

      {/* Carousel */}
      <div className="w-full">
        <Carousel
          opts={{
            dragFree: true,
            dragThreshold: 50,
            align: "start"
          }}
          className="w-full"
        >
          <CarouselContent className="w-full">
            {d?.map((x, index) => {
              return (
                <CarouselItem
                  key={index}
                  className="basis-[33.333%] sm:basis-[25%] md:basis-[20%] lg:basis-[16.666%] xl:basis-[14.285%] 2xl:basis-[12.5%] p-[.25rem] sm:p-[.3rem] md:p-1"
                >
                  <Card data={x} />
                </CarouselItem>
              );
            })}
          </CarouselContent>
        </Carousel>
      </div>
    </div>
  );
});

// Attach Card as a subcomponent
Strip.Card = Card;

export default Strip;
