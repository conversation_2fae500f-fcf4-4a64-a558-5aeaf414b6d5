import { createContext, useContext, useState, useEffect } from 'react';

// Create context
const TitlePreferenceContext = createContext();

// Title preference options
export const TITLE_PREFERENCES = {
  ENGLISH: 'english',
  ROMAJI: 'romaji'
};

export const TitlePreferenceProvider = ({ children }) => {
  // Initialize state from localStorage or default to English
  const [titlePreference, setTitlePreference] = useState(() => {
    const savedPreference = localStorage.getItem('titlePreference');
    return savedPreference || TITLE_PREFERENCES.ENGLISH;
  });

  // Save preference to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('titlePreference', titlePreference);
  }, [titlePreference]);

  // Toggle between English and Romaji
  const toggleTitlePreference = () => {
    console.log("Current preference:", titlePreference);
    setTitlePreference(prev => {
      const newPreference = prev === TITLE_PREFERENCES.ENGLISH
        ? TITLE_PREFERENCES.ROMAJI
        : TITLE_PREFERENCES.ENGLISH;
      console.log("Toggling from", prev, "to", newPreference);
      return newPreference;
    });
  };

  return (
    <TitlePreferenceContext.Provider value={{ titlePreference, toggleTitlePreference }}>
      {children}
    </TitlePreferenceContext.Provider>
  );
};

// Custom hook to use the title preference context
export const useTitlePreference = () => {
  const context = useContext(TitlePreferenceContext);
  if (!context) {
    throw new Error('useTitlePreference must be used within a TitlePreferenceProvider');
  }
  return context;
};
