import axios from 'axios';

/**
 * Middleware to verify AniList authentication token
 * This makes a request to AniList GraphQL API to verify the token
 * and attaches the user data to the request object
 */
export const verifyAniListToken = async (req, res, next) => {
  const token = req.headers.authorization?.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'No token provided' });
  }
  
  try {
    // Verify token with AniList by making a simple query
    const response = await axios.post('https://graphql.anilist.co', {
      query: `{ Viewer { id name avatar { medium } } }`
    }, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    if (response.data.data.Viewer) {
      // Attach user data to request
      req.user = response.data.data.Viewer;
      next();
    } else {
      res.status(401).json({ message: 'Invalid token' });
    }
  } catch (error) {
    console.error('Token verification error:', error.message);
    res.status(401).json({ message: 'Token verification failed' });
  }
};

/**
 * Middleware to check if the authenticated user matches the requested user ID
 * This prevents users from modifying other users' data
 */
export const checkUserMatch = (req, res, next) => {
  const requestedUserId = req.params.userId;
  const authenticatedUserId = req.user.id.toString();
  
  if (requestedUserId !== authenticatedUserId) {
    return res.status(403).json({ 
      message: 'You are not authorized to access this user\'s data' 
    });
  }
  
  next();
};
